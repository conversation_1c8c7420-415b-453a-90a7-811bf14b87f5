import type { Config } from "tailwindcss";

export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primaryColor: "var(--primaryColor)",
        secondaryColor: "var(--secondaryColor)",
        buttonColor: "var(--buttonColor)",
        studentSideMenuColor: "var(--studentSideMenuColor)",
        sideMenuTextColor: "var(--sideMenuTextColor)",
        blackColor: "var(--blackColor)",
        textColor: "var(--textColor)",
        cancelPriceColor: "var(--cancelPriceColor)",
      },
      fontFamily: {
        montserrat: ["Montserrat", "sans-serif"],
        // sans: ['BricolageGrotesque', 'sans-serif'],
      },
      keyframes: {
        "carousel-slide": {
          "0%": { transform: "translateX(0)" },
          "100%": { transform: "translateX(-50%)" },
        },
        "fade-in": {
          "0%": { opacity: "0", transform: "translateY(-10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
      },
      animation: {
        "carousel-slide": "carousel-slide 20s linear infinite",
        "fadeIn": "fade-in 0.2s ease-out forwards",
      },
    },
  },
  plugins: [],
} satisfies Config;


// always move with this prefix ,
// text for text
// bg for background
// text and the color you want in the color variable examples. text-blackColor