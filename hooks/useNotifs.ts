import { App } from 'antd';
import { Spin } from "antd";
import { SmileOutlined } from '@ant-design/icons';
import { ReactNode } from 'react';

export const useNotification = () => {
  const { notification } = App.useApp(); // Get notification API

  const showNotification = (
    type: 'success' | 'info' | 'warning' | 'error' ,
    message: string,
    description?: string,
    isLoading?: boolean,
    icon?: ReactNode,
    
    options: { placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'; duration?: number } = {}
  ) => {
    notification[type]({
      message,
      description,
      placement: options.placement || 'topRight',
      duration: isLoading? null: options.duration ?? 3,
      ...options,
      icon: isLoading? icon:null,
      
    });
  };
  const destroyNotifications = (loadingNotificationKey: void
   ) => {
    notification.destroy()
  };

  return {showNotification, destroyNotifications};
};
