'use client';
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'antd';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  CheckCircleOutlined,
  LockOutlined,
  SafetyOutlined,
  UserOutlined,
  UploadOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  CloseOutlined
} from '@ant-design/icons';
import "../../styles/globals.css";
import Layout from '@/components/general/indexPagelayout/layout'; 

const HeroSection = () => {
    const [logoWidth, setLogoWidth] = useState(0);
    const carouselRef = useRef<HTMLDivElement>(null);
    const logoRefs = useRef<(HTMLImageElement | null)[]>([]);

    const logos = [
        '/GCB.png', 
        '/ecobank.png', 
        '/knust.png', 
        '/htu.png',
        '/GCB.png', 
        '/ecobank.png', 
        '/knust.png', 
        '/htu.png'
    ];

    useEffect(() => {
        // Determine the maximum logo width
        if (logoRefs.current.length > 0) {
            const maxWidth = Math.max(
                ...logoRefs.current
                    .filter(ref => ref !== null)
                    .map(ref => ref?.offsetWidth || 0)
            );
            setLogoWidth(maxWidth);
        }
    }, []);

    return (
      <motion.section
        className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-r from-teal-700 text-white px-4 overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <motion.div
          className="text-center max-w-7xl mx-auto w-full"
          initial={{ y: -50 }}
          animate={{ y: 0 }}
          transition={{ duration: 1 }}
        >
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 px-4">
            Trusted by Institutions, Employers & Certification Bodies Worldwide
          </h1>
          <p className="text-base md:text-lg lg:text-xl mb-6 max-w-4xl mx-auto px-4">
            Ensure authenticity with our secure, AI-powered certificate verification system.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 px-4">
            <button className="w-full sm:w-auto bg-teal-700 text-white px-6 py-3 rounded-full font-semibold hover:bg-teal-800 transition border border-white shadow-md transform hover:scale-105 active:scale-95 duration-300">
              Sign up to verify
            </button>
            <button className="w-full sm:w-auto bg-transparent text-white px-6 py-3 rounded-full font-semibold hover:bg-white/20 transition border border-white shadow-md transform hover:scale-105 active:scale-95 duration-300">
              Learn more about Learnkonnect verification
            </button>
          </div>
          
          {/* Enhanced Logo Carousel */}
          <div className="relative w-full overflow-hidden py-8">
            <div 
              ref={carouselRef}
              className="flex animate-carousel-slide"
              style={{ 
                width: 'fit-content',
                animationDuration: '20s'
              }}
            >
              {logos.map((logo, index) => (
                <div 
                  key={index} 
                  className="mx-4 sm:mx-8 inline-block flex-shrink-0"
                  style={{ width: logoWidth || 'auto' }}
                >
                  <img 
                    ref={el => { logoRefs.current[index] = el; }}
                    src={logo} 
                    alt={`Partner ${index + 1}`} 
                    className="w-16 sm:w-24 h-16 sm:h-24 object-contain opacity-70 hover:opacity-100 transition-all duration-300" 
                  />
                </div>
              ))}
              {/* Duplicate logos for seamless infinite scroll */}
              {logos.map((logo, index) => (
                <div 
                  key={`duplicate-${index}`} 
                  className="mx-4 sm:mx-8 inline-block flex-shrink-0"
                  style={{ width: logoWidth || 'auto' }}
                >
                  <img 
                    src={logo} 
                    alt={`Partner ${index + 1}`} 
                    className="w-16 sm:w-24 h-16 sm:h-24 object-contain opacity-70 hover:opacity-100 transition-all duration-300" 
                  />
                </div>
              ))}
            </div>
            
            {/* Gradient Overlays */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute left-0 top-0 bottom-0 w-16 sm:w-32 bg-gradient-to-r from-teal-900/80 to-transparent z-10"></div>
              <div className="absolute right-0 top-0 bottom-0 w-16 sm:w-32 bg-gradient-to-l from-teal-900/80 to-transparent z-10"></div>
            </div>
          </div>
        </motion.div>
      </motion.section>
    );
  };
  
  const AnimatedCard: React.FC<{ children: React.ReactNode; delay?: number }> = ({ children, delay = 0 }) => {
    const controls = useAnimation();
    const [ref, inView] = useInView({ threshold: 0.1 });
  
    useEffect(() => {
      if (inView) {
        controls.start({
          opacity: 1,
          y: 0,
          transition: { duration: 0.6, delay }
        });
      }
    }, [controls, inView, delay]);
  
    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 50 }}
        animate={controls}
        whileHover={{ scale: 1.05 }}
        className="bg-teal-700 text-white p-6 rounded-lg shadow-xl"
      >
        {children}
      </motion.div>
    );
  };
  

const WhyChooseSection = () => {
    const features = [
      {
        title: 'AI-Powered Accuracy',
        description: 'Our system detects fraudulent certificates instantly.',
        icon: <CheckCircleOutlined className="text-2xl" />
      },
      {
        title: 'Blockchain Security',
        description: 'Immutable verification prevents document tampering.',
        icon: <LockOutlined className="text-2xl" />
      },
      {
        title: 'Globally Recognized',
        description: 'Used by universities, employers, and professional boards worldwide.',
        icon: <SafetyOutlined className="text-2xl" />
      }
    ];
  
    return (
      <motion.section
        className="py-16 bg-gray-50 text-teal-700 px-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1 }}
      >
        <div className="max-w-6xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Why Choose Our Verification System?</h2>
          <p className="text-lg text-gray-600">
            Proof of Reliability: Verified over 5M+ certificates. Partnered with 200+ institutions.
            GDPR & ISO-compliant data security.
          </p>
        </div>
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
          {features.map((feature, index) => (
            <AnimatedCard key={index} delay={index * 0.15}>
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
              <p>{feature.description}</p>
            </AnimatedCard>
          ))}
        </div>
      </motion.section>
    );
  };

  const HowItWorksSection = () => {
    const steps = [
      {
        title: 'Sign Up & Access Portal',
        description: 'Create your secure account and enter the verification dashboard.',
        icon: <UserOutlined className="text-2xl" />
      },
      {
        title: 'Upload Certificate or Enter Details',
        description: 'Scan, upload, or manually enter certificate information.',
        icon: <UploadOutlined className="text-2xl" />
      },
      {
        title: 'Instant Verification & Results',
        description: 'AI-powered analysis confirms authenticity in seconds.',
        icon: <ClockCircleOutlined className="text-2xl" />
      }
    ];
  
    return (
      <motion.section
        className="py-16 bg-white text-teal-700 px-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1 }}
      >
        <div className="max-w-6xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">How It Works</h2>
          <p className="text-gray-600">
            Built with cutting-edge technology ensuring fast, accurate, and tamper-proof verification.
          </p>
        </div>
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
          {steps.map((step, index) => (
            <AnimatedCard key={index} delay={index * 0.15}>
              <div className="mb-4">{step.icon}</div>
              <h3 className="text-xl font-bold mb-2">{step.title}</h3>
              <p>{step.description}</p>
            </AnimatedCard>
          ))}
        </div>
      </motion.section>
    );
  };

  

  const TestimonialsSection = () => {
    const testimonials = [
      {
        name: "Musharof Chy",
        role: "Founder @TailGrids",
        text: "Our members are so impressed. It's intuitive, clean, and distraction-free.",
        image: "/student2.jpg"
      },
      {
        name: "Devid Weilium",
        role: "Founder @Uideck",
        text: "Our members are so impressed. It's intuitive, clean, and distraction-free.",
        image: "/student2.jpg"
      },
      {
        name: "Lithium Frenci",
        role: "Founder @Lineicons",
        text: "Our members are so impressed. It's intuitive, clean, and distraction-free.",
        image: "/student2.jpg"
      }
    ];
  
    return (
      <motion.section
        className="py-16 bg-gray-50 px-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1 }}
      >
        <div className="max-w-6xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-teal-700 mb-4">What Our Clients Say</h2>
          <p className="text-gray-600 mb-6">
            There are many variations of passages of Lorem Ipsum available but the majority have suffered alteration.
          </p>
        </div>
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 0.15}>
              <p className="text-color-white mb-4">"{testimonial.text}"</p>
              <div className="flex items-center justify-center mt-4">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-10 h-11 rounded-full mr-2"
                />
                <div>
                  <h4 className="font-bold">{testimonial.name}</h4>
                  <p className="text-gray-500 text-sm">{testimonial.role}</p>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </motion.section>
    );
  };
  


  const HelpModal = () => {
    const [visible, setVisible] = useState(false);
    const [videoSrc, setVideoSrc] = useState("");
  
    const openModal = () => {
      setVideoSrc("https://www.youtube.com/embed/MIA_w2BY7Yw?rel=0&autoplay=1"); 
      setVisible(true);
    };
  
    const closeModal = () => {
      setVisible(false);
      setVideoSrc("");
    };
  
    return (
      <div className="relative py-16 px-4 bg-white text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-4" style={{color:"#008080"}}>We are ready to help</h2>
          <p className="text-gray-600 mb-8 max-w-xl mx-auto">
            There are many variations of passages of Lorem Ipsum available but the majority have suffered alteration in some form.
          </p>
  
          <div className="relative max-w-3xl mx-auto rounded-lg overflow-hidden">
            <img src="/verify.png" alt="Help Video Thumbnail" className="w-full h-auto object-cover" />
            <motion.div className="absolute inset-0 flex items-center justify-center" transition={{ duration: 0.3 }}>
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  type="primary"
                  shape="circle"
                  icon={<PlayCircleOutlined style={{ fontSize: "3rem", color: "#fff" }} />}
                  onClick={openModal}
                  className="bg-white text-teal-700 border-none scale-150"
                />
              </motion.div>
            </motion.div>
          </div>
  
          {/* Video Modal */}
          <Modal
            open={visible}
            onCancel={closeModal}
            footer={null}
            centered
            width={800}
            styles={{
              body: { 
                padding: 0, 
                background: "transparent" 
              }
            }}
            className="video-modal"
            closeIcon={<CloseOutlined style={{ color: "#0d9488", fontSize: "1.5rem" }} />} 
            destroyOnClose={true}
          >
            {visible && (
              <div className="relative rounded-lg overflow-hidden">
                <iframe
                  width="100%"
                  height="450"
                  src={videoSrc}
                  frameBorder="0"
                  allow="autoplay; encrypted-media"
                  allowFullScreen
                ></iframe>
              </div>
            )}
          </Modal>
        </div>
      </div>
    );
  };




const CaseStudiesSection = () => {
  return (
    <motion.section
      className="py-16 bg-white text-teal-700 px-4"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 1 }}
    >
      <div className="max-w-6xl mx-auto text-center mb-12">
        <h2 className="text-3xl font-bold mb-4">Real-Time Impact Metrics</h2>
        <p className="text-gray-600 mb-8">Who Uses Our Verification System?</p>
      </div>
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
        <motion.div
          className="p-8 bg-gray-100 rounded-lg shadow-md"
          initial={{ scale: 0.9 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-2xl font-bold">5,200,000+</h3>
          <p className="mt-2">Certificates Verified</p>
        </motion.div>
        <motion.div
          className="p-8 bg-gray-100 rounded-lg shadow-md"
          initial={{ scale: 0.9 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h3 className="text-2xl font-bold">1,000+</h3>
          <p className="mt-2">Institutions Worldwide</p>
        </motion.div>
        <motion.div
          className="p-8 bg-gray-100 rounded-lg shadow-md"
          initial={{ scale: 0.9 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h3 className="text-2xl font-bold">98.7%</h3>
          <p className="mt-2">Accuracy Rate</p>
        </motion.div>
      </div>
      <div className="max-w-6xl mx-auto text-center mt-8">
      <button 
                className="mt-4 px-4 py-2 bg-teal-700 text-white rounded hover:bg-teal-800"
              >
                Read our cases
              </button>
      </div>
    </motion.section>
  );
};

const SecuritySection = () => {
  const certifications = [
    'Blockchain-Powered',
    'ISO 27001 Certified',
    'GDPR &amp; HIPAA Compliant'
  ];

  return (
    <motion.section
      className="py-16 bg-teal-700 text-white px-4"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 1 }}
    >
      <div className="max-w-6xl mx-auto text-center mb-12">
        <h2 className="text-3xl font-bold mb-4">Enterprise-Grade Security</h2>
        <p className="text-lg text-gray-200">
          Built with military-grade encryption and multi-factor authentication
        </p>
      </div>
      <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
        {certifications.map((certification, index) => (
          <motion.div
            key={index}
            className="flex flex-col items-center p-4 border border-white rounded-lg"
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.2 }}
          >
            <CheckCircleOutlined className="text-2xl mb-2" />
            <h3 className="text-lg font-semibold text-center">{certification}</h3>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
};

export default function Home() {
  return (
      <Layout>
    <main className="min-h-screen">
      <HeroSection />
      <WhyChooseSection />
      <HowItWorksSection />
      <SecuritySection />
      <TestimonialsSection />
      <CaseStudiesSection />
      <HelpModal />
    </main>
    </Layout>
  );
}

