'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircleOutlined, 
  PlayCircleOutlined, 
  BookOutlined, 
  TeamOutlined, 
  GlobalOutlined, 
  StarOutlined,
  RocketOutlined,
  TrophyOutlined,
  LockOutlined
} from '@ant-design/icons';
import Layout from '@/components/general/indexPagelayout/layout'; 
import { ReactNode } from 'react';


interface AnimatedSectionProps {
  children: ReactNode;
  direction?: 'up' | 'down';
}

const AnimatedSection = ({ children, direction = 'up' }: AnimatedSectionProps) => {
  const variants = {
    hidden: { 
      opacity: 0, 
      y: direction === 'up' ? 50 : -50 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

const HeroSection = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-700 to-teal-900 flex items-center justify-center text-white">
      <motion.div 
        className="max-w-4xl text-center px-4"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.7 }}
      >
        <h1 className="text-4xl md:text-6xl font-bold mb-6">
          Transform Your Learning Journey with LearnKonnect
        </h1>
        <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
          Personalized, adaptive learning experiences powered by cutting-edge AI technology
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <motion.button 
            className="bg-white text-teal-700 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Start Learning
          </motion.button>
          <motion.button 
            className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full hover:bg-white/20 transition"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Watch Demo
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

const FeaturesSection = () => {
  const features = [
    {
      icon: <BookOutlined className="text-4xl text-teal-600" />,
      title: 'Adaptive Curriculum',
      description: 'Personalized learning paths tailored to your unique learning style and pace.'
    },
    {
      icon: <TeamOutlined className="text-4xl text-teal-600" />,
      title: 'Expert Mentorship',
      description: 'Connect with industry experts and receive personalized guidance.'
    },
    {
      icon: <GlobalOutlined className="text-4xl text-teal-600" />,
      title: 'Global Community',
      description: 'Learn and collaborate with students from around the world.'
    }
  ];

  return (
    <AnimatedSection>
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-teal-700">Why Choose LearnKonnect?</h2>
            <p className="text-xl text-gray-600">
              Innovative features designed to accelerate your learning
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div 
                key={index}
                className="bg-white p-8 rounded-lg shadow-lg text-center"
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 10px 20px rgba(0,0,0,0.12)"
                }}
              >
                <div className="mb-6">{feature.icon}</div>
                <h3 className="text-2xl font-semibold mb-4 text-teal-800">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </AnimatedSection>
  );
};

const CourseHighlightSection = () => {
  const [activeTab, setActiveTab] = useState(0);
  const courses = [
    {
      title: 'Advanced Data Science',
      description: 'Master machine learning, AI, and big data analytics with hands-on projects.',
      skills: ['Python', 'Machine Learning', 'Statistical Analysis']
    },
    {
      title: 'Full Stack Web Development',
      description: 'Learn modern web technologies and build scalable, interactive web applications.',
      skills: ['React', 'Node.js', 'Database Design']
    },
    {
      title: 'Digital Marketing Mastery',
      description: 'Become a top-tier digital marketing professional with comprehensive training.',
      skills: ['SEO', 'Social Media Marketing', 'Analytics']
    }
  ];

  return (
    <AnimatedSection>
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-teal-700">Explore Top Courses</h2>
            <p className="text-xl text-gray-600">
              Curated learning paths for career transformation
            </p>
          </div>
          <div className="flex flex-col md:flex-row gap-8">
            <div className="w-full md:w-1/3 space-y-4">
              {courses.map((course, index) => (
                <motion.div 
                  key={index}
                  className={`p-6 rounded-lg cursor-pointer transition ${
                    activeTab === index 
                    ? 'bg-teal-600 text-white' 
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                  onClick={() => setActiveTab(index)}
                  whileHover={{ scale: 1.02 }}
                >
                  <h3 className="text-2xl font-semibold">{course.title}</h3>
                </motion.div>
              ))}
            </div>
            <motion.div 
              key={activeTab}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full md:w-2/3 bg-gray-100 p-8 rounded-lg"
            >
              <h3 className="text-3xl font-bold mb-4 text-teal-800">
                {courses[activeTab].title}
              </h3>
              <p className="text-xl text-gray-700 mb-6">
                {courses[activeTab].description}
              </p>
              <div className="flex flex-wrap gap-4 mb-6">
                {courses[activeTab].skills.map((skill, index) => (
                  <span 
                    key={index} 
                    className="bg-teal-100 text-teal-800 px-4 py-2 rounded-full"
                  >
                    {skill}
                  </span>
                ))}
              </div>
              <motion.button
                className="bg-teal-600 text-white px-8 py-3 rounded-full hover:bg-teal-700 transition"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Enroll Now
              </motion.button>
            </motion.div>
          </div>
        </div>
      </section>
    </AnimatedSection>
  );
};

const TestimonialSection = () => {
  const testimonials = [
    {
      name: 'Freda Elves',
      role: 'Data Scientist at LearnKonnect',
      quote: 'The personalized learning path was exactly what I needed to advance my career.',
      rating: 5
    },
    {
      name: 'Freda Elves',
      role: 'Web Developer, LearnKonnect Graduate',
      quote: 'Incredible mentorship and practical projects. I landed my dream job within months!',
      rating: 5
    }
  ];

  return (
    <AnimatedSection>
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-teal-700">What Our Students Say</h2>
            <p className="text-xl text-gray-600">
              Real stories of transformation and success
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div 
                key={index}
                className="bg-white p-8 rounded-lg shadow-lg"
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 10px 20px rgba(0,0,0,0.12)"
                }}
              >
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarOutlined key={i} className="text-yellow-500 text-2xl" />
                  ))}
                </div>
                <p className="text-xl italic mb-6">"{testimonial.quote}"</p>
                <div className="flex items-center">
                  <div>
                    <h4 className="text-xl font-semibold text-teal-800">{testimonial.name}</h4>
                    <p className="text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </AnimatedSection>
  );
};

const LearningPathwaysSection = () => {
  const pathways = [
    {
      icon: <RocketOutlined className="text-4xl text-teal-700" />,
      title: 'Exam preparation',
      description: 'Tailored learning paths to fast-track your professional growth.',
      benefits: [
        'Personalized Skill Development',
        'Industry-Aligned Curriculum',
        'Career Placement Support'
      ],
      difficulty: 'Advanced',
      color: 'bg-teal-50'
    },
    {
      icon: <TrophyOutlined className="text-4xl text-teal-700" />,
      title: 'Certificate verification',
      description: 'Comprehensive programs for in-depth expertise.',
      benefits: [
        'Expert-Led Courses',
        'Advanced Certification',
        'Mentorship Program'
      ],
      difficulty: 'Professional',
      color: 'bg-teal-100'
    },
    {
      icon: <LockOutlined className="text-4xl text-teal-700" />,
      title: 'AI support',
      description: 'Customized learning for organizations and teams.',
      benefits: [
        'Team Learning Paths',
        'Custom Corporate Training',
        'Performance Tracking'
      ],
      difficulty: 'Enterprise',
      color: 'bg-teal-200'
    },
    {
        icon: <LockOutlined className="text-4xl text-teal-700" />,
        title: 'Mentorship',
        description: 'Customized learning for organizations and teams.',
        benefits: [
          'Team Learning Paths',
          'Custom Corporate Training',
          'Performance Tracking'
        ],
        difficulty: 'Enterprise',
        color: 'bg-teal-200'
      },
      {
        icon: <RocketOutlined className="text-4xl text-teal-700" />,
        title: 'Courses',
        description: 'Tailored learning paths to fast-track your professional growth.',
        benefits: [
          'Personalized Skill Development',
          'Industry-Aligned Curriculum',
          'Career Placement Support'
        ],
        difficulty: 'Advanced',
        color: 'bg-teal-50'
      },
      {
        icon: <TrophyOutlined className="text-4xl text-teal-700" />,
        title: 'Certificate verification',
        description: 'Comprehensive programs for in-depth expertise.',
        benefits: [
          'Expert-Led Courses',
          'Advanced Certification',
          'Mentorship Program'
        ],
        difficulty: 'Professional',
        color: 'bg-teal-100'
      },
  ];

  return (
    <AnimatedSection>
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-teal-700">LearnKonnect Learning Pathways</h2>
            <p className="text-xl text-gray-600">
              Unlock Your Potential with Tailored Learning Experiences
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {pathways.map((pathway, index) => (
              <motion.div 
                key={index}
                className={`p-8 rounded-lg shadow-lg relative overflow-hidden ${pathway.color}`}
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 15px 30px rgba(0,0,0,0.1)"
                }}
              >
                <div className="absolute top-0 right-0 m-4 px-3 py-1 bg-teal-700 text-white rounded-full text-sm">
                  {pathway.difficulty}
                </div>
                <div className="mb-6">{pathway.icon}</div>
                <h3 className="text-2xl font-semibold mb-4 text-teal-800">{pathway.title}</h3>
                <p className="text-gray-700 mb-6">{pathway.description}</p>
                <div className="space-y-3 mb-6">
                  {pathway.benefits.map((benefit, benefitIndex) => (
                    <div 
                      key={benefitIndex} 
                      className="flex items-center text-teal-900"
                    >
                      <CheckCircleOutlined className="mr-2 text-teal-700" />
                      {benefit}
                    </div>
                  ))}
                </div>
                <motion.button
                  className="w-full bg-teal-700 text-white px-8 py-3 rounded-full hover:bg-teal-800 transition"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Explore Pathway
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </AnimatedSection>
  );
};

export default function LearnKonnectEducationPage() {
  return (
<Layout>
    <main>
      <HeroSection />
      <FeaturesSection />
      <CourseHighlightSection />
      <TestimonialSection />
      <LearningPathwaysSection />
    </main>
    </Layout>
  );
}