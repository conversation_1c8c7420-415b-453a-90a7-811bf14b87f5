import {StudentLayout} from '@/components/general/studentLayout'  
import type { Metadata } from 'next'
//import { Inter } from 'next/font/google'
//const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'LearnKonnect',
  description: 'LearnKonnect',
}
export default async function Layout({
  children,
  // params: { locale },
}: {
  children: React.ReactNode
  
}) {

  return (
    <html >
      <body>
           <StudentLayout>{children}</StudentLayout>
       </body>
    </html>
  )
}
