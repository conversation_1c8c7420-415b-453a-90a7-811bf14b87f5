'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { LeftOutlined, RightOutlined, FilterOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Pagination, Input, Empty, Spin, Select } from 'antd';
import '@/styles/globals.css';
import { MonthSection, NoteCard } from './notecards/components';
import { useNoteLogic } from './logic/useNoteLogic';
import NoteModal from './logic/NoteModal';
import ViewNoteModal from './logic/ViewNoteModal';
import { useUserStore } from '@/store/userStore';
import { useStudentStore } from '@/store/studentStore';
import { formatDate, isRecent, isYesterday } from './logic/dateUtils';
import { Loader } from '@/components/general/loader';

// Main page component for course notes
const NotePage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(6); 
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [noteToEdit, setNoteToEdit] = useState<any>(null);
  const [noteToView, setNoteToView] = useState<any>(null);

  const [noteOpLoading, setNoteOpLoading] = useState(false);
  const loaderTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    loading,
    setLoading,
    courses,
    notes,
    fetchStudentCourses,
    fetchNotesByCourse,
    formatNotesByMonthAndCategory,
    userProfileImage
  } = useNoteLogic();

  // Get user information from stores
  const { user } = useUserStore();
  const { student } = useStudentStore();

  // Track if we've already fetched courses to prevent repeat calls
  const coursesLoadedRef = useRef(false);

  // Fetch courses once on component mount
  useEffect(() => {
    if (courses.length === 0 && !coursesLoadedRef.current) {
      coursesLoadedRef.current = true;
      fetchStudentCourses(false).catch(() => {});
    }
  }, [fetchStudentCourses, courses.length]);

  // Track if we're already fetching notes for a course
  const fetchingNotesRef = useRef<string | null>(null);

  // Fetch notes once when selected course changes
  useEffect(() => {
    if (!selectedCourse) return;
    
    // Skip if we're already fetching for this course
    if (fetchingNotesRef.current === selectedCourse) return;
    
    fetchingNotesRef.current = selectedCourse;
    
    fetchNotesByCourse(selectedCourse, false)
      .catch(() => {})
      .finally(() => {
        // Reset loading states when done
        setLoading(false);
        setNoteOpLoading(false);
        fetchingNotesRef.current = null;
      });
  }, [selectedCourse, fetchNotesByCourse, setLoading]);
  
  // Force-reset loading state on mount and at regular intervals
  useEffect(() => {
    // Clear on mount
    setLoading(false);
    setNoteOpLoading(false);
    
    // Force-clear every 3 seconds to prevent stuck loader
    const safetyInterval = setInterval(() => {
      setLoading(false);
      setNoteOpLoading(false);
    }, 3000);
    
    // Clear on unmount
    return () => {
      clearInterval(safetyInterval);
      setLoading(false);
      setNoteOpLoading(false);
    };
  }, [setLoading]);
  
  // Also reset loading whenever notes array changes
  useEffect(() => {
    setLoading(false);
    setNoteOpLoading(false);
  }, [notes, setLoading]);
  
  // Show loader with auto-hide functionality
  const showLoaderWithTimeout = useCallback((duration = 2000) => {
    setNoteOpLoading(true);
    setTimeout(() => {
      setNoteOpLoading(false);
      setLoading(false);
    }, duration);
  }, [setLoading]); 

  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    
    // Reset to first page when searching to show the first page of results
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll back to top of the notes section for better UX
    const notesContainer = document.querySelector('.custom-scrollbar');
    if (notesContainer) {
      notesContainer.scrollTop = 0;
    }
  };
  
  // Handle page size change if we implement that in the future
  const handlePageSizeChange = (current: number, size: number) => {
    setPageSize(size);
    setCurrentPage(1); 
  };

  const showModal = () => {
    // Reset edit mode for new note creation
    setIsEditMode(false);
    setNoteToEdit(null);
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    // Reset edit state when modal is closd
    setIsEditMode(false);
    setNoteToEdit(null);
  };
  
  // Handle view modal close
  const handleViewCancel = () => {
    setIsViewModalVisible(false);
    setNoteToView(null);
  };

  // Handle editng a note
  const handleEditNote = (noteId: string) => {
    // Find the note in our notes array
    const note = notes.find(note => note.id === noteId);
    if (note) {
      setNoteToEdit(note);
      setIsEditMode(true);
      // Make sure the correct course is selectd
      setSelectedCourse(note.course_id);
      setIsModalVisible(true);
    }
  };
  
  // Handle viewing a full note
  const handleViewNote = (noteId: string) => {
    // Find the note in our notes array
    const note = notes.find(note => note.id === noteId);
    if (note) {
      setNoteToView(note);
      setIsViewModalVisible(true);
    }
  };

  // Get total number of notes across all categories for pagination
  const getTotalNotesCount = () => {
    return notes.length;
  };
  
  // Filter notes based on search query
  const getFilteredNotes = () => {
    if (!searchQuery || !searchQuery.trim()) {
      return notes;
    }
    
    try {
      const query = searchQuery.toLowerCase().trim();
      console.log('Searching for:', query);
      console.log('Total notes to search through:', notes.length);
      
      // More robust way to filter, with null/undefined checks
      const filtered = notes.filter(note => {
        // Ensure we have valid strings to search through
        const title = (note.title || '').toLowerCase();
        const content = (note.content || '').toLowerCase();
        const chapter = (note.chapter || '').toLowerCase();
        
        // Check if any field contains the query
        const matchesTitle = title.includes(query);
        const matchesContent = content.includes(query);
        const matchesChapter = chapter.includes(query);
        
        // Return true if any field matches
        return matchesTitle || matchesContent || matchesChapter;
      });
      
      console.log('Found matches:', filtered.length);
      return filtered;
    } catch (error) {
      console.error('Error in search function:', error);
      return notes; 
    }
  };
  
  // Sort notes to show today's notes first
  const getSortedNotes = (notesToSort: any[]) => {
    return [...notesToSort].sort((a, b) => {
      // First sort by recency
      const aIsToday = isRecent(a.created_at);
      const bIsToday = isRecent(b.created_at);
      
      if (aIsToday && !bIsToday) return -1;
      if (!aIsToday && bIsToday) return 1;
      
      // Then sort by creation date (newest first)
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  };
  
  // Get filtered, sorted, and paginated notes
  const getProcessedNotes = () => {
    const filtered = getFilteredNotes();
    const sorted = getSortedNotes(filtered);
    
    // Then paginate
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return sorted.slice(startIndex, endIndex);
  };
  
  // Get notes for the current page only - filtered, sorted, and paginated
  const paginatedNotes = getProcessedNotes();
  
  // Get total count for pagination after filtering
  const filteredTotalCount = getFilteredNotes().length;
  

  // Create course options for dropdown
  const courseOptions = courses.map(course => ({
    label: `${course.name}`,
    value: course.id
  }));

  // Find the selected course object
  const selectedCourseObj = selectedCourse ? courses.find(course => course.id === selectedCourse) : null;
  const selectedCourseName = selectedCourseObj ? `${selectedCourseObj.name} (${selectedCourseObj.code})` : "";

  return (
    <>
      {/* Full-page loader - show for both regular loading and note operations */}
      {(loading || noteOpLoading) && <Loader />}

      <div className="h-full w-full overflow-y-auto custom-scrollbar" style={{ height: 'calc(100vh - 64px)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6">
        {/* Header with the curved blue background */}
        <div className="w-full rounded-lg overflow-hidden mb-6 relative">
          <img
            src="/notification.png"
            alt="Course Notes Header"
            className="w-full"
          />

          {/* Text overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <h1 className="text-xl sm:text-2xl font-medium text-white">
              Course Notes & Summaries
            </h1>
          </div>

          {/* Circle icon in the middle bottom */}
          <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-2 shadow-md z-10">
            <div className="h-8 w-8 flex items-center justify-center text-teal-500">
              <img src="/logo.png" alt="Logo" className="w-5 h-5" />
            </div>
          </div>
        </div>

        {/* Search and filter section */}
        <div className="flex flex-col sm:flex-row items-center gap-3 mb-8">
          <div className="relative w-full sm:flex-1">
            <Input
              prefix={<SearchOutlined className="text-teal-600" />}
              placeholder="Search for notes or summaries..."
              className="rounded-md border-teal-600 focus:border-teal-700 hover:border-teal-500"
              style={{ borderColor: '#0D9488' }}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              allowClear
            />
          </div>

          <div className="flex gap-2 w-full sm:w-auto">
            <Select
              placeholder="Select Course"
              className="min-w-[200px]"
              options={courseOptions}
              loading={loading}
              onChange={(value) => {
                console.log('Course selected from dropdown:', value);
                console.log('Available course options:', courseOptions);
                const selectedCourseObj = courses.find(course => course.id === value);
                console.log('Selected course object:', selectedCourseObj);
                setSelectedCourse(value);
              }}
              suffixIcon={<FilterOutlined className="text-teal-600" />}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              notFoundContent={loading ? <Spin size="small" /> : "No courses found"}
            />

            <Button
              type="primary"
              icon={<PlusOutlined />}
              className="flex-1 sm:flex-none bg-teal-600 hover:bg-teal-700 border-teal-600"
              onClick={showModal}
            >
              Create New Note
            </Button>
          </div>
        </div>

        {/* Loading state indicator was moved to the full-page loader */}

        {/* Empty state */}
        {!loading && notes.length === 0 && (
          <Empty
            description={
              <span className="text-gray-500">
                {selectedCourse
                  ? "No notes found for this course. Create your first note!"
                  : "Select a course to view notes or create a new note."}
              </span>
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="my-12"
          />
        )}

        {/* Notes grid - simplified layout with 3 notes per row */}
        {!loading && notes.length > 0 ? (
          <div className="mb-8">
            <div className="flex flex-wrap -mx-2">
              {paginatedNotes.map(note => (
                <div key={note.id} className="w-full sm:w-1/2 lg:w-1/3 px-2 mb-4">
                  <NoteCard
                    id={note.id}
                    title={note.title}
                    description={note.content}
                    timeCategory={isRecent(note.created_at) ? 'today' : isYesterday(note.created_at) ? 'yesterday' : 'outdated'}
                    author={{
                      name: user?.first_name ? `${user.first_name} ${user.last_name || ''}` : (student?.first_name ? `${student.first_name} ${student.last_name || ''}` : 'Me'),
                      avatar: userProfileImage,
                    }}
                    date={formatDate(note.created_at)}
                    onEdit={handleEditNote}
                    onView={handleViewNote}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : (
          !loading && selectedCourse && (
            <div className="text-center py-8">
              <p className="text-gray-500"></p>
            </div>
          )
        )}

        {/* Pagination controls */}
        {!loading && notes.length > 0 && (
          <div className="flex justify-center mt-8 pb-8 items-center">
            <Pagination
              current={currentPage}
              onChange={handlePageChange}
              total={filteredTotalCount}
              pageSize={pageSize}
              showSizeChanger={false}
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} notes`}
              itemRender={(_, type, originalElement) => {
                if (type === 'prev') {
                  return <Button type="text" size="small" icon={<LeftOutlined />} className="border-none text-teal-600">Previous</Button>;
                }
                if (type === 'next') {
                  return <Button type="text" size="small" icon={<RightOutlined className="ml-1" />} className="border-none text-teal-600">Next</Button>;
                }
                return originalElement;
              }}
              className="custom-pagination"
            />
          </div>
        )}

        {/* Note creation/edit modal with auto-hiding loader */}
        <NoteModal
          visible={isModalVisible}
          onCancel={handleCancel}
          selectedCourse={selectedCourse}
          selectedCourseName={selectedCourseName}
          availableCourses={courses}
          editMode={isEditMode}
          noteToEdit={noteToEdit}
          setNoteOpLoading={setNoteOpLoading}
          showLoaderWithTimeout={showLoaderWithTimeout}
        />
        
        {/* View note modal */}
        <ViewNoteModal
          visible={isViewModalVisible}
          onCancel={handleViewCancel}
          note={noteToView}
          authorName={user?.first_name ? `${user.first_name} ${user.last_name || ''}` : (student?.first_name ? `${student.first_name} ${student.last_name || ''}` : 'Me')}
          authorAvatar={userProfileImage}
        />
      </div>
    </div>
    </>
  );
};

export default NotePage;
