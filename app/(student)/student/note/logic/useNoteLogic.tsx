'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useUserStore } from "@/store/userStore";
import { useStudentStore } from "@/store/studentStore";
import { useApi } from "@/hooks/useRequest";
import { useFetch } from "@/hooks/useFetch";
import { useNotification } from "@/hooks/useNotifs";
import { Spin, Form } from "antd";

// Define types for notes
export interface Note {
  id: string;
  user_id: string;
  course_id: string;
  chapter: string;
  title: string;
  content: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

// Define types for courses
export interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  level: string;
  credits: number;
  base_price: number;
  teacher_id: string;
  teacher_commission_percentage: number;
  cover_image_path?: string;
  cover_image?: string;
  duration: number;
  has_cohorts: boolean;
  cohort_duration_weeks: number;
  max_students_per_cohort: number;
  auto_create_cohorts: boolean;
  days_between_cohorts: number;
  is_published: boolean;
}

// Define the return type for this hook
interface NoteLogicHook {
  loading: boolean;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  courses: Course[];
  notes: Note[];
  selectedCourseId: string | null;
  setSelectedCourseId: React.Dispatch<React.SetStateAction<string | null>>;
  noteForm: any; // Form instance
  fetchStudentCourses: (showToast?: boolean) => Promise<any[]>;
  fetchNotesByCourse: (courseId: string, showToast?: boolean) => Promise<any[]>;
  createNote: (noteData: any) => Promise<any>;
  updateNote: (noteId: string, noteData: any) => Promise<any>;
  formatNotesByMonthAndCategory: (notes: Note[]) => Record<string, Record<string, Note[]>>;
  userProfileImage: string;
}

export const useNoteLogic = (): NoteLogicHook => {
  const { user } = useUserStore();
  const { student } = useStudentStore();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();

  const [loading, setLoading] = useState(false);
  const [courses, setCourses] = useState<Course[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null);
  const [userProfileImage, setUserProfileImage] = useState<string>("/avatar1.png"); // Default avatar
  const [noteForm] = Form.useForm();

  // Use a ref to track if we're already fetching to prevent duplicate requests
  const isFetchingRef = useRef(false);

  // Fetch student courses
  const fetchStudentCourses = useCallback(async (showToast = false) => {
    // Prevent duplicate requests
    if (isFetchingRef.current) {
      return [];
    }

    isFetchingRef.current = true;

    try {
      setLoading(true);

      if (showToast) {
        destroyNotifications();
        showNotification('info', 'Loading', 'Fetching your courses...', true, <Spin />);
      }

      const response = await request("GET", "/protected/me", null, "multipart/form-data");

      if (showToast) {
        destroyNotifications();
      }

      if (response && response.status === 200) {
        // Extract profile image if available
        if (response.data?.data?.student?.profile_image_path) {
          setUserProfileImage(response.data.data.student.profile_image_path);
        } else if (response.data?.data?.user?.profile_image_path) {
          setUserProfileImage(response.data.data.user.profile_image_path);
        }
        
        if (response.data?.data?.courses) {
          const studentCourses = response.data.data.courses || [];
          setCourses(studentCourses);
          return studentCourses;
        } else {
          setCourses([]);
          return [];
        }
      } else {
        if (showToast) {
          showNotification('error', 'Error', 'Failed to fetch courses');
        }
        return [];
      }
    } catch (error) {
      if (showToast) {
        showNotification('error', 'Error', 'Failed to fetch courses');
      }
      return [];
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [request, showNotification, destroyNotifications]);

  // Fetch notes for a specific course
  const fetchNotesByCourse = useCallback(async (courseId: string, showToast = false) => {
    // Prevent duplicate requests
    if (isFetchingRef.current) {
      return [];
    }

    isFetchingRef.current = true;

    try {
      setLoading(true);

      if (showToast) {
        destroyNotifications();
        showNotification('info', 'Loading', 'Fetching notes...', true, <Spin />);
      }

      const response = await request("GET", `/note/course/${courseId}`);

      if (showToast) {
        destroyNotifications();
      }

      if (response && response.status === 200) {
        if (response.data?.data) {
          let courseNotes = [];
          if (Array.isArray(response.data.data)) {
            courseNotes = response.data.data;
          } else if (typeof response.data.data === 'object') {
            courseNotes = response.data.data.notes || [];
          }
          
          const transformedNotes = courseNotes.map((note: Note) => ({
            ...note,
            content: Array.isArray(note.content) ? note.content.join('\n') : note.content
          }));
          
          setNotes(transformedNotes);
          return transformedNotes;
        } else {
          setNotes([]);
          return [];
        }
      } else {
        if (showToast) {
          showNotification('error', 'Error', 'Failed to fetch notes');
        }
        setNotes([]);
        return [];
      }
    } catch (error) {
      if (showToast) {
        showNotification('error', 'Error', 'Failed to fetch notes');
      }
      setNotes([]);
      return [];
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [request, showNotification, destroyNotifications]);

  // Create a new note
  const createNote = useCallback(async (noteData: {
    course_id: string;
    chapter: string;
    title: string;
    content: string;
    tags: string[];
  }) => {
    try {
      if (!user?.id) {
        showNotification('error', 'Error', 'User ID not found');
        return null;
      }

      // Clear any existing notifications
      destroyNotifications();
      const payload = {
        user_id: user.id,
        course_id: noteData.course_id,
        chapter: noteData.chapter,
        title: noteData.title,
        content: noteData.content,
        tags: noteData.tags
      };
      
      // Make the API request
      const response = await request('POST', '/note', payload);
      destroyNotifications();

      if (response && response.status >= 200 && response.status < 300) {
        showNotification('success', 'Success', 'Note created successfully');
        noteForm.resetFields();
        
        // Always reset loading state first
        setLoading(false);
        
        // Only fetch if we have a selected course
        if (selectedCourseId) {
          await fetchNotesByCourse(selectedCourseId, false);
        }

        // Reset loading state again after all operations
        setLoading(false);
        return response.data.data;
      } else {
        showNotification('error', 'Error', 'Failed to create note');
        setLoading(false);
        return null;
      }
    } catch (error) {
      destroyNotifications();
      showNotification('error', 'Error', 'Failed to create note');
      setLoading(false);
      return null;
    }
  }, [user, request, showNotification, destroyNotifications, noteForm, selectedCourseId, fetchNotesByCourse]);

  // Format notez by month and category
  const formatNotesByMonthAndCategory = useCallback((notesList: Note[]) => {
    const groupedNotes: Record<string, Record<string, Note[]>> = {};

    // Check if notesList is valid and not empty
    if (!notesList || !Array.isArray(notesList) || notesList.length === 0) {
      return groupedNotes;
    }

    notesList.forEach(note => {
      // Skip invalid notes
      if (!note || !note.created_at) {
        return;
      }

      try {
        const date = new Date(note.created_at);
        // Check if date is valid
        if (isNaN(date.getTime())) {
          return;
        }

        const month = date.toLocaleString('default', { month: 'long' });
        const category = note.chapter || 'Uncategorized';

        if (!groupedNotes[month]) {
          groupedNotes[month] = {};
        }

        if (!groupedNotes[month][category]) {
          groupedNotes[month][category] = [];
        }

        groupedNotes[month][category].push(note);
      } catch (error) {
        // Silent error handling
      }
    });

    return groupedNotes;
  }, []);

// Update an existing note
const updateNote = useCallback(async (noteId: string, noteData: {
  course_id: string;
  chapter: string;
  title: string;
  content: string;
  tags: string[];
}) => {
  try {
    if (!user?.id) {
      showNotification('error', 'Error', 'User ID not found');
      return null;
    }


    
    // Clear any existing notifications
    destroyNotifications();


    const payload = {
      id: noteId,
      user_id: user.id,
      course_id: noteData.course_id,
      chapter: noteData.chapter,
      title: noteData.title,
      content: Array.isArray(noteData.content) ? noteData.content : [noteData.content],
      tags: noteData.tags
    };
    
    // Make the  request
    const response = await request('PUT', '/note', payload);
    destroyNotifications();

    if (response && response.status >= 200 && response.status < 300) {
      showNotification('success', 'Success', 'Note updated successfully');
      
      // Always reset loading state immediately
      setLoading(false);
      
      // Fetch the updated notes
      if (selectedCourseId) {
        await fetchNotesByCourse(selectedCourseId, false);
      } else if (noteData.course_id) {
        await fetchNotesByCourse(noteData.course_id, false);
      }
      
      // Reset loading state again after all operations to be absolutely sure
      setLoading(false);
      return response.data.data;
    } else {
      showNotification('error', 'Error', 'Failed to update note');
      setLoading(false);
      return null;
    }
  } catch (error) {
    destroyNotifications();
    showNotification('error', 'Error', 'Failed to update note');
    setLoading(false);
    return null;
  }
}, [user, request, showNotification, destroyNotifications, selectedCourseId, fetchNotesByCourse]);

  return {
    loading,
    setLoading,  
    courses,
    notes,
    selectedCourseId,
    setSelectedCourseId,
    noteForm,
    fetchStudentCourses,
    fetchNotesByCourse,
    createNote,
    updateNote,
    formatNotesByMonthAndCategory,
    userProfileImage
  };
};
