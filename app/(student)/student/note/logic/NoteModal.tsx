'use client';

import React, { useEffect, useState } from 'react';
import { Modal, Form, Select, Input, Button, Tag, Spin } from 'antd';
import { CloseOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useNoteLogic } from './useNoteLogic';

interface NoteModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedCourse: string | null;
  selectedCourseName?: string;
  availableCourses?: any[];
  // For editing mode
  editMode?: boolean;
  setNoteOpLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  showLoaderWithTimeout?: (duration?: number) => void;
  noteToEdit?: {
    id: string;
    course_id: string;
    chapter: string;
    title: string;
    content: string;
    tags: string[];
  } | null;
}

const NoteModal: React.FC<NoteModalProps> = ({
  visible,
  onCancel,
  selectedCourse,
  selectedCourseName,
  availableCourses = [],
  editMode = false,
  setNoteOpLoading,
  showLoaderWithTimeout,
  noteToEdit = null
}) => {
  const {
    loading,
    courses: logicCourses,
    noteForm,
    createNote,
    updateNote,
    setLoading 
  } = useNoteLogic();

  // Use courses from props if available, otherwise use from logic
  const courses = availableCourses && availableCourses.length > 0 ? availableCourses : logicCourses;

  const [tags, setTags] = useState<string[]>([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const [tagInputLength, setTagInputLength] = useState(0);
  const [courseOptions, setCourseOptions] = useState<{ label: string; value: string }[]>([]);

  // Reset form when modal becoms visible, or initialize with edit data if in edit mode
  useEffect(() => {
    if (visible) {
      noteForm.resetFields();
      setTags([]);
      setTagInputLength(0);

      // Update course options immediately
      let options: { label: string; value: string }[] = [];

      if (courses && courses.length > 0) {
        options = courses.map(course => ({
          label: `${course.name} (${course.code})`,
          value: course.id
        }));
      } else if (selectedCourse && selectedCourseName) {
        options = [{
          label: selectedCourseName,
          value: selectedCourse
        }];
      }

      setCourseOptions(options);

      // Set form values for editing mode
      if (editMode && noteToEdit) {
        // Set the course ID
        noteForm.setFieldValue('course_id', noteToEdit.course_id);
        
        // Set othr fields
        noteForm.setFieldValue('chapter', noteToEdit.chapter);
        noteForm.setFieldValue('title', noteToEdit.title);
        noteForm.setFieldValue('content', noteToEdit.content);

        // Set tags if they exist
        if (noteToEdit.tags && Array.isArray(noteToEdit.tags)) {
          setTags(noteToEdit.tags);
        }
      } 
      // For creation mode, just set the selected course
      else if (selectedCourse) {

        setTimeout(() => {
          noteForm.setFieldValue('course_id', selectedCourse);
        }, 100);
      }
    }
  }, [visible, noteForm, courses, selectedCourse, selectedCourseName, editMode, noteToEdit]);

  // Update course options when courses change
  useEffect(() => {
    if (!visible) return; 

    // Create course options with proper label and value format
    let options: { label: string; value: string }[] = [];

    if (courses && courses.length > 0) {
      options = courses.map(course => ({
        label: `${course.name}`,
        value: course.id
      }));

      console.log('Setting course options from courses array:', options);
      setCourseOptions(options);

      // Find the selected course object if we have a selectedCourse ID
      if (selectedCourse && noteForm) {
        const selectedCourseObj = courses.find(course => course.id === selectedCourse);
        console.log('Found selected course object:', selectedCourseObj);

        if (selectedCourseObj) {
          // Set the form value with the course ID
          noteForm.setFieldValue('course_id', selectedCourse);
        }
      }
    } else if (selectedCourse && selectedCourseName && options.length === 0) {
      // If we have a selected course but no courses array, create an option for it
      options = [{
        label: selectedCourseName,
        value: selectedCourse
      }];

      console.log('Setting course option from selectedCourseName:', options);
      setCourseOptions(options);
      noteForm.setFieldValue('course_id', selectedCourse);
    }
  }, [courses, selectedCourse, selectedCourseName, noteForm, visible]);

  // Handle tag input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Limit tag input to 50 characters
    if (e.target.value.length <= 50) {
      setInputValue(e.target.value);
      setTagInputLength(e.target.value.length);
    }
  };

  const handleInputConfirm = () => {
    if (inputValue && !tags.includes(inputValue) && inputValue.length <= 50) {
      setTags([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  // We've removed content length tracking

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate form fields
      const values = await noteForm.validateFields();

      // Prepare payload with form values and tags
      const payload = {
        ...values,
        tags: tags
      };
      
      // First close the modal
      onCancel();
      
      // Show loader during operation
      if (showLoaderWithTimeout) {
        showLoaderWithTimeout(5000);
      } else if (setNoteOpLoading) {
        setNoteOpLoading(true);
      }
      
      setLoading(true);
      
      try {
        if (editMode && noteToEdit && noteToEdit.id) {
          await updateNote(noteToEdit.id, payload);
        } else {
          await createNote(payload);
        }
      } catch (apiError) {
        // Reset loading state on error
        setLoading(false);
        if (setNoteOpLoading) setNoteOpLoading(false);
      }
    } catch (error) {
      // Reset loading states on form validation error
      setLoading(false);
      if (setNoteOpLoading) setNoteOpLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      footer={null}
      onCancel={onCancel}
      width={650}
      centered
      className="create-note-modal"
      title={null}
      styles={{
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.65)' },
        body: { padding: 0 }
      }}
      style={{ borderRadius: '12px', overflow: 'hidden' }}
    >
      <div className="relative overflow-hidden">
        {/* Decorative curved header */}
        <div className="bg-teal-500 h-12 flex items-center justify-center relative overflow-hidden">
          <h2 className="text-white font-medium text-lg relative z-10">Create New Note</h2>
          <Button
            type="text"
            icon={<CloseOutlined />}
            className="absolute right-2 top-2 text-white hover:text-gray-200 hover:bg-transparent"
            onClick={onCancel}
          />
        </div>

        {/* Form content */}
        <div className="p-4">
          <Form
            form={noteForm}
            layout="vertical"
            requiredMark={false}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Form.Item
                  name="course_id"
                  label={<span className="text-sm font-medium text-teal-700">Course</span>}
                  rules={[{ required: true, message: 'Please select a course' }]}
                  className="mb-3"
                >
                  <Select
                    placeholder="Select a course"
                    options={courseOptions}
                    className="border-teal-600 hover:border-teal-700"
                    loading={loading}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    style={{ width: '100%' }}
                    notFoundContent={loading ? <Spin size="small" /> : "No data"}
                    onChange={(value) => {
                      console.log('Course selected in modal:', value);
                      console.log('Available course options:', courseOptions);
                    }}
                  />
                </Form.Item>
              </div>

              <div>
                <Form.Item
                  name="chapter"
                  label={<span className="text-sm font-medium text-teal-700">Chapter/Topic</span>}
                  rules={[{ required: true, message: 'Please enter a chapter or topic' }]}
                  className="mb-3"
                >
                  <Input
                    placeholder="Enter chapter or topic"
                    className="border-teal-600 hover:border-teal-700"
                  />
                </Form.Item>
              </div>
            </div>

            <Form.Item
              name="title"
              label={<span className="text-sm font-medium text-teal-700">Note Title</span>}
              rules={[{ required: true, message: 'Please enter a title' }]}
              className="mb-3"
            >
              <Input
                placeholder="Enter a title for your note"
                className="border-teal-600 hover:border-teal-700"
              />
            </Form.Item>

            <Form.Item
              name="content"
              label={
                <div className="flex justify-between w-full">
                  <span className="text-sm font-medium text-teal-700">Note Content</span>
                </div>
              }
              rules={[
                { required: true, message: 'Please enter note content' }
              ]}
              className="mb-3"
            >
              <Input.TextArea
                placeholder="Write down all your notes"
                className="border-teal-600 hover:border-teal-700 resize-none"
                style={{ minHeight: '120px' }}
              />
            </Form.Item>

            <Form.Item
              label={
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-teal-700">Tags</span>
                  <span className="text-xs text-red-500 mt-1">Maximum 50 characters per tag</span>
                </div>
              }
              className="mb-4"
            >
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <Tag
                    key={tag}
                    closable
                    onClose={() => {
                      const newTags = [...tags];
                      newTags.splice(index, 1);
                      setTags(newTags);
                    }}
                    className="bg-teal-100 text-teal-700 border-teal-200"
                  >
                    {tag}
                  </Tag>
                ))}

                {inputVisible ? (
                  <Input
                    type="text"
                    size="small"
                    style={{ width: 120 }}
                    value={inputValue}
                    onChange={handleInputChange}
                    onBlur={handleInputConfirm}
                    onPressEnter={handleInputConfirm}
                    autoFocus
                    maxLength={50}
                    className="border-teal-600"
                    placeholder="Max 50 chars"
                  />
                ) : (
                  <Tag
                    onClick={() => setInputVisible(true)}
                    className="bg-white text-teal-600 border-dashed border-teal-600 cursor-pointer"
                  >
                    <PlusOutlined /> New Tag
                  </Tag>
                )}
              </div>
            </Form.Item>

            <div className="flex justify-end gap-3 mt-4">
              <Button
                onClick={onCancel}
                className="border-gray-300 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={loading}
                className="bg-teal-600 hover:bg-teal-700 border-teal-600"
              >
                {editMode ? 'Update Note' : 'Create Note'}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default NoteModal;
