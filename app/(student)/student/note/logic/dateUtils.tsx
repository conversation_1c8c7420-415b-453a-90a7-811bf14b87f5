

/**
 * Checks if a date is today
 */
export const isRecent = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

/**
 * Checks if a date is yesterday
 */
export const isYesterday = (dateString: string) => {
  const date = new Date(dateString);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
};

/**
 * Formats a date string to YYYY-MM-DD format
 */
export const formatDate = (dateString: string) => {
  // Check if date is invalid
  if (!dateString) return 'Unknown date';
  
  // Handle ISO format date string from API
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Invalid date';
  
  // Format the date as YYYY-MM-DD
  const year = date.getFullYear();
  // Month is 0-indexed in JavaScript, so add 1 and pad with leading zero if needed
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  // Pad the day with leading zero if needed
  const day = date.getDate().toString().padStart(2, '0');
  
  // Return the date in YYYY-MM-DD format
  return `${year}-${month}-${day}`;
};
