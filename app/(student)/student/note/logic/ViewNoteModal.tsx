'use client';

import React from 'react';
import { Modal, Avatar, Tag, Divider, Typography } from 'antd';
import { UserOutlined, CalendarOutlined, TagOutlined } from '@ant-design/icons';
import { formatDate } from './dateUtils';

const { Title, Text, Paragraph } = Typography;

interface ViewNoteModalProps {
  visible: boolean;
  onCancel: () => void;
  note: {
    id: string;
    title: string;
    content: string;
    created_at: string;
    updated_at?: string;
    chapter?: string;
    tags?: string[];
  } | null;
  authorName?: string;
  authorAvatar?: string;
}

const ViewNoteModal: React.FC<ViewNoteModalProps> = ({
  visible,
  onCancel,
  note,
  authorName = 'Me',
  authorAvatar = '/avatar1.png'
}) => {
  if (!note) return null;

  // Format content for display - handle both string and array content formats
  const formattedContent = Array.isArray(note.content) 
    ? note.content.join('\n\n') 
    : note.content;

  return (
    <Modal
      open={visible}
      footer={null}
      onCancel={onCancel}
      width={700}
      centered
      title={null}
      styles={{
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.65)' },
        body: { padding: '20px' }
      }}
      style={{ borderRadius: '12px', overflow: 'hidden' }}
    >
      <div className="bg-white rounded-lg">
        {/* Note header with course/chapter info */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
            <div className="bg-teal-50 text-teal-700 px-3 py-1 rounded-full text-xs font-medium inline-block">
              {note.chapter || 'General Note'}
            </div>
            <Text type="secondary" className="text-xs">
              <CalendarOutlined className="mr-1" />
              {formatDate(note.created_at)}
              {note.updated_at && note.updated_at !== note.created_at && 
                ` (Updated: ${formatDate(note.updated_at)})`}
            </Text>
          </div>
          <Title level={3} className="mt-2 mb-3 text-teal-800">{note.title}</Title>
        </div>

        {/* Author info */}
        <div className="flex items-center mb-4">
          <Avatar size="small" icon={<UserOutlined />} src={authorAvatar} />
          <Text className="ml-2 text-sm text-gray-600">{authorName}</Text>
        </div>

        <Divider className="my-4" />

        {/* Note content */}
        <div className="whitespace-pre-wrap mb-4">
          <Paragraph className="text-gray-700">
            {formattedContent}
          </Paragraph>
        </div>

        {/* Tags */}
        {note.tags && note.tags.length > 0 && (
          <div className="mt-6">
            <div className="flex items-center">
              <TagOutlined className="text-gray-500 mr-2" />
              <Text className="text-gray-500 text-sm">Tags:</Text>
            </div>
            <div className="mt-2">
              {note.tags.map((tag, index) => (
                <Tag key={index} className="mr-2 mb-2 bg-gray-100">
                  {tag}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ViewNoteModal;
