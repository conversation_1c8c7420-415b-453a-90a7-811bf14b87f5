"use client";
import React from "react";
import { Spin } from "antd";
import NoticeBoardExamsCard from "./widgets/notice-exams-card";
import ChatGroupInvitationCard from "./widgets/chat-group-invitation-card";
import { useNoticeBoard } from "./logic/useNoticeBoard";
interface Invitation {
  id: string;
  chat_group_id: string;
  status: string;
  created_at: string;
  group_name?: string;
  group_description?: string;
  message?: string;
}

const ITEMS_PER_PAGE = 4;

export default function NoticeBoard() {
  const {
    loading,
    noticeBoardData,
    groups,
    currentPage,
    totalPages,
    currentInvitations,
    handlePageChange,
    respondToInvitation,
    fetchNoticeBoardData,
  } = useNoticeBoard();

  const handleInvitationResponse = async (
    invitationId: string,
    chatGroupId: string,
    accept: boolean
  ) => {
    await respondToInvitation(invitationId, chatGroupId, accept);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin />
      </div>
    );
  }

  return (
    <div className="p-4 overflow-y-scroll">
      <h2 className="text-xl font-bold text-teal-800 underline mb-6">LearnKonnect Notice Board</h2>

      {/* Notice Board Cards */}
      {/* Chat Group Invitations */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">
            Chat Group Invitations
          </h3>
          {totalPages > 1 && (
            <div className="flex items-center">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          )}
        </div>
        {currentInvitations.length > 0 ? (
          <div className="relative " >
            <div
              id="invitations-container"
              className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-2 pb-4"
            >
              {currentInvitations.map((invitation) => (
                <div key={invitation.id} className="w-full " >
                  <ChatGroupInvitationCard
                    key={invitation.id}
                    invitation={{
                      id: invitation.id,
                      chat_group_id: invitation.chat_group_id,
                      status: invitation.status,
                      created_at: invitation.created_at,
                      group_name: invitation.group_name || groups[invitation.chat_group_id]?.name,
                      group_description: invitation.group_description,
                      message: invitation.message,
                    }}
                    onAccept={async () => {
                      await handleInvitationResponse(
                        invitation.id,
                        invitation.chat_group_id,
                        true
                      );
                    }}
                    onReject={async () => {
                      await handleInvitationResponse(
                        invitation.id,
                        invitation.chat_group_id,
                        false
                      );
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p className="text-gray-500">No pending chat group invitations</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Upcoming Exam */}
        {noticeBoardData.upcoming_exams &&
          noticeBoardData.upcoming_exams.map((exams: any) => {
            return <NoticeBoardExamsCard examID={exams.id} />;
          })}

        {/* <LearningCard exam={{
          "name": "Assignment 1",
          "id": "4dd0c6ef-1600-4803-b395-ec6733faec05",
          "pass_marks": 50.0,
          "status": "upcoming",
          "start_datetime": "2025-04-29T10:00:00",
          "published": false,
          "created_at": "2025-04-29T14:49:29.198773",
          "deleted_at": null,
          "total_marks": 100.0,
          "course_id": "4b2bcb32-0e9e-424c-bbf5-85073cef4086",
          "teacher_id": "f0214da6-135f-413f-ba8c-dd78f04c13d5",
          "duration": 120.0,
          "completed": false,
          "end_datetime": "2025-04-30T12:00:00",
          "is_published": false,
          "updated_at": "2025-04-29T14:49:29.198773"
        }} /> */}

        {/* Important Update */}
        {/* <div className="bg-white rounded-xl shadow-md p-4 space-y-2 flex flex-col justify-between">
          <div>
            <h3 className="font-semibold text-gray-800">Important update</h3>
            <p className="text-sm text-red-600">🔔 New Alerts!</p>
          </div>
          <button className="w-full border text-sm border-teal-600 text-teal-700 py-2 rounded hover:bg-teal-50">
            Check the latest updates
          </button>
        </div> */}

        {/* Next TA Session */}
        {/* <div className="bg-white rounded-xl shadow-md p-4 space-y-2 md:col-span-1 h-[250px] flex flex-col justify-between">
          <div>
            <h3 className="font-semibold text-gray-800">Next TA session</h3>
            <p className="text-sm text-red-600">🔔 Live session Alerts!</p>
          </div>
          <div>
             <img src="/courses.png" alt="Logo" className="w-[400px] h-[100px]" />
            <div className="flex items-center justify-between text-sm text-teal-700">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <img src="/logo.png" alt="Logo" />
                </div>
                <span>02-02-25: 8:30AM</span>
              </div>
              <div className="text-yellow-400 text-xs">★★★★☆</div>
            </div>

            <button className="w-full border text-sm border-teal-600 text-teal-700 py-2 rounded hover:bg-teal-50">
              GO live session
            </button>
          </div>
        </div> */}
      </div>
    </div>
  );
}
