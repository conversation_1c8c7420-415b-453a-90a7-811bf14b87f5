'use client';
import React from 'react';
import { <PERSON><PERSON>, <PERSON>, message } from 'antd';
import { useApi } from '@/hooks/useRequest';
import { useNotification } from '@/hooks/useNotifs';

interface ChatGroupInvitationCardProps {
  invitation: {
    id: string;
    chat_group_id: string;
    status: string;
    created_at: string;
    group_name?: string;
    group_description?: string;
    message?: string;
  };
  onAccept: (invitationId: string) => void;
  onReject: (invitationId: string) => void;
}

export default function ChatGroupInvitationCard({ invitation, onAccept, onReject }: ChatGroupInvitationCardProps) {
  const shortDescription = invitation.group_description && invitation.group_description.length > 500
    ? invitation.group_description.slice(0, 500) + '…'
    : invitation.group_description || '';
  const { request } = useApi();
  const { showNotification } = useNotification();
  const [loading, setLoading] = React.useState(false);

  const handleAccept = async () => {
    try {
      setLoading(true);
      const response = await request(
        'POST',
        `/chat-group/${invitation.chat_group_id}/invitations/accept`,
        { invitation_id: invitation.id },
        'application/json'
      );

      if (response?.status === 200) {
        message.success('Invitation accepted successfully');
        onAccept(invitation.id);
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to accept invitation');
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      showNotification('error', 'Error', 'An error occurred while accepting the invitation');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    try {
      setLoading(true);
      const response = await request(
        'POST',
        `/chat-group/${invitation.chat_group_id}/invitations/reject`,
        { invitation_id: invitation.id },
        'application/json'
      );

      if (response?.status === 200) {
        message.success('Invitation declined');
        onReject(invitation.id);
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to decline invitation');
      }
    } catch (error) {
      console.error('Error declining invitation:', error);
      showNotification('error', 'Error', 'An error occurred while declining the invitation');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { bg: string; text: string; label: string }> = {
      pending: { bg: 'bg-yellow-50', text: 'text-yellow-700', label: 'Pending' },
      accepted: { bg: 'bg-green-50', text: 'text-green-700', label: 'Accepted' },
      declined: { bg: 'bg-red-50', text: 'text-red-700', label: 'Declined' },
      expired: { bg: 'bg-gray-100', text: 'text-gray-700', label: 'Expired' },
    };
    
    const { bg, text, label } = statusMap[status.toLowerCase()] || { 
      bg: 'bg-gray-100', 
      text: 'text-gray-700',
      label: status 
    };
    
    return (
      <span className={`text-xs px-2 py-1 rounded-full ${bg} ${text} font-medium`}>
        {label}
      </span>
    );
  };

  return (
    <div className="flex flex-col justify-between bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden min-h-[320px]">
      <div className="p-3 flex-1 flex flex-col">
        <div className="flex items-center justify-between mb-1">
          <div className="text-[11px] text-gray-500">
            {new Date(invitation.created_at).toLocaleDateString()}
          </div>
          {getStatusBadge(invitation.status)}
        </div>
        <div className="flex items-center space-x-2 mt-1">
          <div className="flex-shrink-0 w-8 h-8 rounded-md bg-blue-50 flex items-center justify-center text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
            </svg>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-base font-semibold text-gray-900 truncate" title={invitation.group_name}>
              {invitation.group_name || 'Group Chat'}
            </h4>
            <p className="text-sm text-gray-500">Group Invitation</p>
            {invitation.message && (
              <p className="text-sm text-gray-700 leading-snug">
                {invitation.message}
              </p>
            )}
            {shortDescription && (
              <p className="text-sm text-gray-700 leading-snug" title={invitation.group_description}>
                {shortDescription}
              </p>
            )}
          </div>
        </div>
      </div>
      {invitation.status === 'pending' ? (
        <div className="bg-gray-50 px-2 py-1.5 flex justify-between border-t border-gray-100 space-x-2">
          <button
            onClick={handleAccept}
            disabled={loading}
            className="text-sm font-semibold text-white bg-teal-600 hover:bg-teal-700 px-4 py-2 rounded transition-colors flex-1"
          >
            {loading ? '...' : 'Accept'}
          </button>
          <button
            onClick={handleReject}
            disabled={loading}
            className="text-sm font-semibold text-gray-700 hover:bg-gray-100 px-4 py-2 rounded transition-colors border border-gray-300 flex-1"
          >
            Decline
          </button>
        </div>
      ) : (
        <div className="bg-gray-50 px-3 py-2 text-center border-t border-gray-100">
          <span className={`text-xs font-medium ${
            invitation.status === 'accepted' 
              ? 'text-green-600' 
              : 'text-gray-600'
          }`}>
            {invitation.status === 'accepted' ? '✓ Accepted' : '✗ Declined'}
          </span>
        </div>
      )}
    </div>
  );
}
