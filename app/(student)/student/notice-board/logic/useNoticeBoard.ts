import { useState, useEffect, useCallback } from 'react';
import { useApi } from '@/hooks/useRequest';
import { useNotification } from '@/hooks/useNotifs';

export interface NoticeBoardData {
  upcoming_exams: any[];
  assignment_due_dates: any[];
  next_sessions: any[];
  upcoming_tests: any[];
  important_updates: any[];
  chat_group_invitations: Array<{
    [x: string]: string | undefined;
    id: string;
    chat_group_id: string;
    status: string;
    created_at: string;
    group_name?: string;
    message?: string;
  }>;
}

export const useNoticeBoard = () => {
  const { request } = useApi();
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(true);
  const [noticeBoardData, setNoticeBoardData] = useState<NoticeBoardData>({
    upcoming_exams: [],
    assignment_due_dates: [],
    next_sessions: [],
    upcoming_tests: [],
    important_updates: [],
    chat_group_invitations: []
  });
  interface GroupInfo { name: string; description?: string }
  const [groups, setGroups] = useState<Record<string, GroupInfo>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;

  const fetchGroups = useCallback(async (): Promise<Record<string, GroupInfo>> => {
    try {
      const response = await request("GET", "/chat-group/", null, "application/json");
      if (response?.data?.data?.chat_groups) {
        const groupsMap: Record<string, GroupInfo> = response.data.data.chat_groups.reduce((acc: Record<string, GroupInfo>, group: any) => {
          acc[group.id] = { name: (group.name || group.group_name || group.title || 'Unnamed Group'), description: group.description || group.group_description || '' };
          return acc;
        }, {});
        setGroups(groupsMap);
        return groupsMap;
      }
      return {};
    } catch (error) {
      console.error('Error fetching groups:', error);
      return {};
    }
  }, [request]);

  const respondToInvitation = useCallback(async (invitationId: string, chatGroupId: string, accept: boolean) => {
    try {
      const response = await request(
        "POST",
        `/chat-group/${chatGroupId}/invitations/${accept ? 'accept' : 'reject'}`,
        null,
        "application/json"
      );
  
      if (response?.status === 200) {
        showNotification(
          'success',
          accept ? 'Invitation Accepted' : 'Invitation Declined',
          accept ? 'You have joined the group successfully!' : 'Invitation has been declined.'
        );
        
        // Remove the responded invitation from the state
        setNoticeBoardData(prev => ({
          ...prev,
          chat_group_invitations: prev.chat_group_invitations.filter(
            invite => invite.id !== invitationId
          )
        }));
  
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error responding to invitation:', error);
      showNotification('error', 'Error', 'Failed to process your response. Please try again.');
      return false;
    }
  }, [request, showNotification]);

  
  const fetchNoticeBoardData = useCallback(async () => {
    try {
      setLoading(true);
      
      const [noticeResponse, groupsMap] = await Promise.all([
        request("GET", "/protected/notice-board", null, "application/json"),
        fetchGroups()
      ]);

      // Debug: log the raw /protected/notice-board API response
      // console.log('Notice board response:', noticeResponse);

      if (noticeResponse?.status === 200) {
        const data = noticeResponse.data?.data || {};

        // ---------------- Ensure we have correct group details for each invitation ---------------
        let mergedGroupsMap: Record<string, GroupInfo> = { ...groupsMap };
        const invitationsRaw = (data.chat_group_invitations || []) as any[];

        const missingIds = invitationsRaw
          .filter(inv => !mergedGroupsMap[inv.chat_group_id])
          .map(inv => inv.chat_group_id);

        if (missingIds.length) {
          try {
            const fetched = await Promise.all(
              missingIds.map(id => request("GET", `/chat-group/${id}`, null, "application/json"))
            );
            fetched.forEach(resp => {
              if (resp?.status === 200 && resp.data?.data?.chat_group) {
                const g = resp.data.data.chat_group;
                mergedGroupsMap[g.id] = {
                  name: g.name || 'Group',
                  description: g.description || ''
                };
              }
            });
            // Persist any newly fetched groups into state
            setGroups(prev => ({ ...prev, ...mergedGroupsMap }));
          } catch (err) {
            console.error('Failed to fetch missing group information', err);
          }
        }

        console.log("Invitationid",noticeResponse)
        setNoticeBoardData({
          ...data,
          chat_group_invitations: invitationsRaw.map((invite: any) => ({
            ...invite,
            group_name: mergedGroupsMap[invite.chat_group_id]?.name || (invite.message?.match(/Join my group\s+\"?([^\"]+)\"?/i)?.[1] || 'Group'),
            group_description: mergedGroupsMap[invite.chat_group_id]?.description || '',
            message: invite.message || ''
          }))
        });
      } else {
        showNotification('error', 'Error', noticeResponse?.data?.detail || 'Failed to load notice board');
      }
    } catch (error) {
      console.error('Error:', error);
      showNotification('error', 'Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  }, [request, showNotification]);

  // Initialize data on mount
  useEffect(() => {
    const init = async () => {
      await fetchNoticeBoardData();
    };
    init();

  }, []);

  const totalPages = Math.ceil((noticeBoardData.chat_group_invitations?.length || 0) / itemsPerPage);
  const currentInvitations = noticeBoardData.chat_group_invitations?.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  ) || [];

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const container = document.getElementById('invitations-container');
    container?.scrollTo({ left: 0, behavior: 'smooth' });
  };

  return {
    loading,
    noticeBoardData,
    groups,
    currentPage,
    totalPages,
    currentInvitations,
    handlePageChange,
    respondToInvitation,
    fetchNoticeBoardData
  };
};
