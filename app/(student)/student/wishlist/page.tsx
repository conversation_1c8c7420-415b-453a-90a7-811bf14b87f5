'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { CloseCircleFilled, HeartFilled, HeartOutlined } from '@ant-design/icons';
import { Pagination, ConfigProvider, Spin } from 'antd';
import { motion } from 'framer-motion';
import ButtonTemplate from '@/components/ui/button-template';
import { WishlistLogics, WishlistItem } from './_logics/wishlist_logics';
import { CartLogics } from '../cart/_logics/cart_logics';

// We're using the WishlistItem type from the logics file

const WishlistPage = () => {
  // Get the logic functions
  const { getWishlistItems, getLatestWishlistItems, loading } = WishlistLogics();

   const { removeFromWishlist } = CartLogics()
  
  // State for wishlist items
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  
  // State for teacher loading
  const [teacherInfoLoading, setTeacherInfoLoading] = useState(false);
  
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);

  // Check if on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Callback for when teacher info is loaded
  const handleTeacherInfoUpdate = useCallback((updatedItems: WishlistItem[]) => {
    setWishlistItems(updatedItems);
    setTeacherInfoLoading(false);
  }, []);

  // Fetch wishlist items on component mount
  useEffect(() => {
    const fetchWishlistItems = async () => {
      setTeacherInfoLoading(true);
      // Pass the callback to update items when teacher info is ready
      const items = await getWishlistItems(handleTeacherInfoUpdate);
      setWishlistItems(items || []);
    };

    fetchWishlistItems();
  }, [handleTeacherInfoUpdate]);

  React.useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Filter wishlist items (for future search implementation)
  const filteredWishlistItems = wishlistItems;

  // Get current page data for wishlist
  const wishlistCoursesData = filteredWishlistItems.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );
  


  // Wishlist course card component
  const WishlistCourseCard = React.memo(({ course }: { course: WishlistItem }) => {
    // Function to handle clicking on the course card
    const handleCourseClick = () => {
      window.location.href = `/student/courses/${course.id}`;
    };

    return (
      <div 
        className="group bg-white text-sm rounded-lg overflow-hidden shadow-md border border-purple-100 hover:cursor-pointer w-full relative"
        // onClick={handleCourseClick}
      >
        
        <div className="relative">
          {/* Course Image with optimized handling */}
          {(() => {
            const getImageSrc = () => {
              if (course.cover_image) {
                // Handle base64 encoded images
                if (course.cover_image.startsWith('iVBOR')) {
                  return `data:image/png;base64,${course.cover_image}`;
                } else if (course.cover_image.startsWith('/9j/')) {
                  return `data:image/jpeg;base64,${course.cover_image}`;
                } else if (course.cover_image.startsWith('http')) {
                  // Handle URLs directly
                  return course.cover_image;
                } else {
                  // For other base64 formats assume JPEG
                  return `data:image/jpeg;base64,${course.cover_image}`;
                }
              } else if (course.cover_image_path) {
                return course.cover_image_path;
              } else {
                return '/course-frame.png';
              }
            };

            return (
              <img
                className="w-full h-[8rem] object-cover"
                src={getImageSrc()}
                alt={course.name}
                loading="lazy" 
                onError={(e) => {
                  console.log('Image failed to load, using fallback');
                  (e.target as HTMLImageElement).src = '/course-frame.png';
                }}
              />
            );
          })()}
          <div className="absolute bottom-3 left-3">
            <div className="bg-[#00a19a] text-white text-[10px] px-2.5 py-1 rounded-md font-medium capitalize">
              {course.level?.toLowerCase() || 'All Levels'}
            </div>
          </div>
        </div>

        <div className="p-3">
          <div className="flex justify-between items-start mb-2">
            <div className="font-semibold text-xs text-gray-800 pr-2 flex-1">
              {course.name}
            </div>
            <div className="flex items-center">
              <div className="flex">
                {[1, 2, 3, 4].map((star) => (
                  <svg key={star} className="w-3.5 h-3.5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                ))}
                <svg className="w-3.5 h-3.5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              </div>
              <span className="text-[11px] ml-1 text-gray-700 font-medium">4.00</span>
            </div>
          </div>
              <div className='flex justify-between items-center mb-3'>
          <div className="flex items-center ">
            <img
              className="w-5 h-5 rounded-full border border-gray-200"
              src={course.teacher_avatar ? 
                (course.teacher_avatar.startsWith('iVBOR') || course.teacher_avatar.startsWith('/9j/')) ? 
                  `data:image/${course.teacher_avatar.startsWith('iVBOR') ? 'png' : 'jpeg'};base64,${course.teacher_avatar}` 
                  : course.teacher_avatar
                : "/rename.webp"}
              alt="Teacher Avatar"
              onError={(e) => {
                console.log('Teacher image failed to load, using fallback');
                (e.target as HTMLImageElement).src = "/rename.webp";
              }}
            />
            <span className="ml-1.5 text-[#00a19a] text-[11px] font-medium">
              {course.teacher_name || (course.teacher_id ? `Teacher ID: ${course.teacher_id.substring(0, 8)}...` : 'Unknown Teacher')}
            </span>
          </div>
          <CloseCircleFilled
          onClick={async()=>{
         await   removeFromWishlist(course.id)
       const items = await  getWishlistItems()
       setWishlistItems(items)
          }}
          className=" group-hover:visible invisible animate-fadeIn text-red-400 text-lg absolute right-2 top-2 bg-white rounded-full " />
           
          </div>

          <div className="flex justify-between items-center">
            <span className="font-bold text-[#00a19a] text-sm">GHS {course.base_price}</span>
            <button 
              className="bg-[#2d3748] text-white text-[11px] px-3 py-1.5 rounded-md flex items-center"
              onClick={handleCourseClick}
            >
              View Course
              <svg className="w-3 h-3 ml-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  });





  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#00a19a",
        },
      }}
    >
      <div className="p-4 md:p-6 bg-gray-50 h-screen relative overflow-y-auto">


     
        {/* Header with wave pattern */}
        <div className="relative w-full flex justify-center">
          <div className="w-full h-50 bg-gradient-to-r from-blue-400 to-teal-600 rounded-lg overflow-hidden relative">
            {/* Wave pattern background */}
            <img
              src="/notification.png"
              alt="Wishlist Header"
              className="w-full h-full object-cover rounded-lg"
            />

            {/* Text centered in the header */}
            <div className="absolute inset-0 flex items-center justify-center">
              <h1 className="text-xl md:text-2xl font-bold text-white">My Wishlist</h1>
            </div>
          </div>

          {/* Heart icon positioned at bottom center */}
          <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2">
            <div className="bg-white p-2 rounded-full shadow-md">
              <div className="bg-red-500 rounded-full w-8 h-8 flex items-center justify-center">
                <HeartOutlined className="text-white text-lg" />
              </div>
            </div>
          </div>
        </div>

        {/* Personal Wishlist Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-10 mt-10"
        >
          <h2 className="text-xl font-semibold text-[#00a19a] mb-6">My Wishlist</h2>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
            </div>
          ) : wishlistCoursesData.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {wishlistCoursesData.map((course) => (
                  <WishlistCourseCard key={course.id} course={course} />
                ))}
              </div>

              {/* Pagination */}
              <div className="flex justify-center py-4 mt-4">
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={filteredWishlistItems.length}
                  onChange={(page) => setCurrentPage(page)}
                  showSizeChanger
                  onShowSizeChange={(_, size) => {
                    setCurrentPage(1);
                    setPageSize(size);
                  }}
                  pageSizeOptions={['4', '8', '12', '16']}
                  showTotal={(total, range) => (
                    <span className="hidden sm:inline">{`${range[0]}-${range[1]} of ${total} items`}</span>
                  )}
                  size={isMobile ? "small" : "default"}
                  className="text-xs sm:text-sm"
                />
              </div>
            </>
          ) : (
            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <HeartOutlined className="text-4xl text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-600">Your wishlist is empty</h3>
              <p className="text-gray-500 mb-4">Browse courses and add them to your wishlist</p>
              <ButtonTemplate
                label="Browse Courses"
                className="bg-[#00a19a] text-white mx-auto"
                onClick={() => window.location.href = '/student/explore'}
              />
            </div>
          )}
        </motion.div>

        {/* Extra space at bottom to allow scrolling past pagination */}
        <div className="h-24 md:h-32"></div>
      </div>
    </ConfigProvider>
  );
};

export default WishlistPage;