import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest";
import { useState } from "react";
import { useStudentStore } from "@/store/studentStore";
import { useUserStore } from "@/store/userStore";

// Define a wishlist item type based on the API response
export interface WishlistItem {
  id: string;
  name: string;
  cover_image_path?: string;
  cover_image?: string;
  code: string;
  duration: number;
  description: string;
  base_price: number;
  level: string;
  topics?: Record<string, string>;
  teacher_id: string;
  teacher_name?: string;
  teacher_avatar?: string;
  created_at: string;
  is_published: boolean;
}

export const WishlistLogics = () => {
  const { request } = useApi();
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const { student } = useStudentStore();
  const { user } = useUserStore();


  const teacherCache: Record<string, any> = {};

  let lastWishlistItems: WishlistItem[] = [];

  type UpdateCallback = (items: WishlistItem[]) => void;

  /**
   * @param onUpdate 
   */
  async function getWishlistItems(onUpdate?: UpdateCallback): Promise<WishlistItem[]> {
    try {
      setLoading(true);
      

      
      // Fetch wishlist data from the 
      console.log('Fetching wishlist');
      const response: any = await request('GET', '/wishlist/user', null, 'application/json');
      
      if (response && response?.status === 200) {
        const wishlistData = response?.data?.data?.wishlists || [];
        // Map the  response to our wishlist item format
        const processedWishlist = wishlistData.map((item: any) => ({
          id: item.wishlist.id,
          name: item.course.name,
          cover_image_path: item.cover_image_path,
          cover_image: item.course.cover_image,
          code: item.course.code,
          duration: item.course.duration,
          description: item.course.description,
          base_price: item.course.base_price,
          level: item.course.level,
          topics: item.course.topics,
          teacher_id: item.course.teacher_id,
          created_at: item.course.created_at,
          is_published: item.course.is_published
        }));
        console.log('processed wish list', processedWishlist)
        
      

        
        setLoading(false);
        
        // Store for later updates
        lastWishlistItems = [...processedWishlist];
        
        // Start teacher info fetching in the background without waiting
        fetchTeacherInfo(processedWishlist).then(updatedItems => {

          if (JSON.stringify(processedWishlist) !== JSON.stringify(updatedItems)) {

            lastWishlistItems = updatedItems;
            // Call the update callback if provided
            if (onUpdate) {
              onUpdate(updatedItems);
            }
          }
        }).catch(error => {
          console.error('Error fetching teacher info:', error);
        });

        return processedWishlist;
      } else {
        if(response.data?.detail === "404: No wishlists found for this user"){
          // showNotification("success", 'No wishlists found for this user',);
          setLoading(false);
          return [];
        }else{
        const errorMessage = response?.data?.error || 'Failed to retrieve wishlist';
        console.error('Error in wishlist response:', errorMessage);
        showNotification('error', 'Error', errorMessage);
        setLoading(false);
        return [];
        }
      }
    } catch (error) {
      console.error('Error fetching wishlist items:', error);
      showNotification('error', 'Error', 'Failed to load wishlist items');
      setLoading(false);
      return [];
    }
  }

  async function fetchTeacherInfo(items: WishlistItem[]): Promise<WishlistItem[]> {
    // Get unique teacher IDs from wishlist items that aren't already in the cache
    const teacherIds = [...new Set(items.map(item => item.teacher_id))]
      .filter(Boolean)
      .filter(id => !teacherCache[id]); 
    
    // If we have all teachers in cache already, just use the cache
    if (teacherIds.length === 0) {
      return enhanceItemsWithTeacherInfo(items, teacherCache);
    }
    
    try {
      // Fetch information for each teacher not in cache (in parallel)
      await Promise.all(
        teacherIds.map(async (teacherId) => {
          if (!teacherId) return;
          
          try {
            // Use the teacher endpoint 
            const response: any = await request("GET", `/teacher/${teacherId}`, null, "application/json");
            
            if (response && response?.status === 200) {
              const teacherData = response?.data?.data?.teacher || response?.data?.teacher || response?.data?.data || {};
              // Store in cache for future use
              teacherCache[teacherId] = {
                name: teacherData.name || 'Unknown Teacher',
                profile_image: teacherData.profile_image || null
              };
            }
          } catch (error) {
            console.error(`Error fetching teacher info for ID ${teacherId}:`, error);
            teacherCache[teacherId] = {
              name: 'Unknown Teacher',
              profile_image: null
            };
          }
        })
      );
      
      // Now enhance the items with teacher information from the cache
      return enhanceItemsWithTeacherInfo(items, teacherCache);
    } catch (error) {
      console.error('Error fetching teacher information:', error);
      return items;
    }
  }
  
  /**
   * Helper function to enhance items with teacher information
   */
  function enhanceItemsWithTeacherInfo(items: WishlistItem[], teacherMap: Record<string, any>): WishlistItem[] {
    return items.map(item => {
      if (item.teacher_id && teacherMap[item.teacher_id]) {
        // Add teacher information to the item
        const teacher = teacherMap[item.teacher_id];
        
        return {
          ...item,
          teacher_name: teacher.name || 'Unknown Teacher',
          teacher_avatar: teacher.profile_image || null
        };
      }
      
      return {
        ...item,
        teacher_name: 'Unknown Teacher',
        teacher_avatar: null
      };
    });
  }



  /**
   * Get the latest wishlist items with teacher info
   * This can be used to refresh the UI after teacher info is loaded
   */
  function getLatestWishlistItems(): WishlistItem[] {
    return lastWishlistItems;
  }
  
  return {
    getWishlistItems,
    getLatestWishlistItems,
    loading
  };
};
