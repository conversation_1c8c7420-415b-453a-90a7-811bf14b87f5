import React, { useEffect, useState } from 'react'
import LearningCard from './widgets/learning-card'
import { useApi } from "@/hooks/useRequest"
import { useNotification } from "@/hooks/useNotifs";
import { Spin } from "antd";
import { Course } from "@/types";

export default function AllLearningPage() {
  const { request } = useApi();
  const { showNotification } = useNotification();
  const [loading, setLoading] = useState(false);
  const [courses, setCourses] = useState<Course[]>([]);

  async function getMyCourses() {
    try {
      setLoading(true);
      const requestResponse: any = await request("GET", "/protected/me", null, "multipart/form-data");
      setLoading(false);
      
      if (requestResponse && requestResponse?.status === 200) {
        setCourses(requestResponse?.data?.data?.courses || []);
      } else {
        showNotification('error', 'Error fetching courses', requestResponse?.data?.detail);
      }
    } catch (error) {
      console.log('Error fetching courses:', error);
      setLoading(false);
    }
  }

  useEffect(() => {
    getMyCourses();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><Spin /></div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 pt-4">
      {courses.map((course) => (
        <LearningCard key={course.id} courseId={course.id} />
      ))}
    </div>
  )
}
