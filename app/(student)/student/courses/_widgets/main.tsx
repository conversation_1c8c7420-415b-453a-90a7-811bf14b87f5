'use client'
'use client'
import CourseCard from '@/components/ui/course-template'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import coursePerson from '@/public/course-man.png'
import InputTemplate from '@/components/ui/input-template'
import { Form, MenuProps, Modal } from 'antd'
import { FolderOpenFilled, FundProjectionScreenOutlined, SearchOutlined } from '@ant-design/icons'
import TagTemplate from '@/components/ui/tag-template'
import TopRatedCourses from './top-rated-course'
import ButtonTemplate from '@/components/ui/button-template'
import LearningPaths from '@/components/general/indexPagelayout/popular'
import { useFetch } from '@/hooks/useFetch'
import { AuthLogics } from '@/app/(auth)/auth/_logics/auth-logics'
import {  SchoolType } from '@/types'
import { useRouter } from 'next/navigation'
import { CoursesLogics } from '../_logics/courses_logics'
import { EducationLevelList } from '@/utils/levels'
import { div } from 'framer-motion/client'
import { CourseType } from '@/app/dashboard/dummydata/coursedata'

export default function MainCoursePage() {

  const [schoolList, setSchoolList] = useState<SchoolType[]>([])
  const [courseList, setCourseList] = useState<CourseType[]>([])
  const router = useRouter();
  const { searchCourse, loading } = CoursesLogics()
  const { data, loading: fetchingSchools } = useFetch('/unprotected/school/all')
  useEffect(() => {
    console.log('school', data?.data?.schools)
    setSchoolList(data?.data?.schools)
  }, [data])
  const handleCancel = (key: any) => {
    console.log(key);
    setOpenModal(false)
  };

  const items: MenuProps['items'] = [

  ];

  const [openModal, setOpenModal] = useState(false);


  return (
    <div className='p-8 bg-white h-full text-primaryColor overflow-y-scroll '>
      <Modal

        title="Search Results"
        open={openModal}
        onCancel={handleCancel}
        footer={null} // Hide default footer buttons
      >
        {courseList.length == 0 && 
        <div> 
          <div className='text-xs'>
            <div className='font-semibold'>{"No Course Found!"}</div>
            <div>{"No item matches your query, try again."}</div>
          </div>
        </div>
        }
        {courseList.length>0 && <div className='my-4'> 
          <div className='text-xs'>
            <div className='font-semibold'>{`${courseList.length} course${courseList.length === 1? ``:`s`} found`}</div>
           </div>
        </div>}
        { 
       
        
        courseList && courseList.map((course)=>(
          <div onClick={()=>{
            router.push(`/student/courses/${course.id}`)
          }} className='flex items-start justify-between hover:bg-stone-100 p-2 hover:cursor-pointer hover:rounded-md'>
          <div className='flex items-start '>
          <div className='border rounded-md p-2 px-4 mr-4'>
            <FolderOpenFilled />
          </div>
          <div className='text-xs'>
            <div className='font-semibold'>{course.name}</div>
            <div className='line-clamp-2'>{course.description}</div>
          </div>
          </div>
          <div className='text-xs'>
            <TagTemplate className='rounded-md !bg-lime-200 !text-black' value={`${EducationLevelList.find(level => level.id === course.level)?.name}`} />
          </div>
        </div>
        )) }



      </Modal>
      <div className='flex md:flex-nowrap flex-wrap  gap-4 justify-between   w-full'>
        <div>
          <h2 className='font-bold text-[28px]'>LearnKonnect Plus</h2>
          <h1 className='font-semibold text-[36px] mt-8 font leading-none'>{"Fast-track your career with expert-led courses"}</h1>
          <div className='text-gray-500'>{"Join 100,000+ professionals who've advanced their careers"}</div>

        </div>
        <div className='flex '>
          <Image src={coursePerson} height={500} width={1024} alt='course person' />
        </div>
      </div>
      {/* Search */}
      <div>
        <div className='flex justify-end mt-8'>
          {/* <SelectTemplate
            fieldName="school_id"
            fieldNames={{ label: 'name', value: 'id' }}
            options={schoolList && schoolList}
            label={""} className="" placeHolder={'School'} prefix={<FundProjectionScreenOutlined />} />
          <Dropdown menu={{ items }} placement="bottomLeft" className='border border-primaryColor rounded-md px-2 flex justify-center items-center py-0 mt-2 ml-4'>
            <Button><AppstoreOutlined /> Filter</Button>
          </Dropdown> */}

        </div>
        <div className='font-semibold text-[16px] '>Search 10,000+ learning courses</div>
        <Form
          name="searchForm"
          onFinish={(onFinish) => {
            console.log('searching', onFinish)
          }}
        >
          <InputTemplate onEnterPressed={async (val) => {
            console.log('enter pressed',val)
           var resp = await   searchCourse(val);
            setCourseList(resp??[])
            setOpenModal(true)

          }} loadingState={loading} required={false} inputType='search' placeHolder='e.g. Cloud computing' label={''} fieldName={'search'}
            prefix={<SearchOutlined className='text-gray-300' />}
          />
          <div className='flex gap-2 flex-wrap justify-center items-center font-semibold'>
            <div className='mr-8 text-primaryColor '>Popular</div>
            <TagTemplate className='border-none rounded-md !text-primaryColor !bg-[#DDEBEB]' value={'Business'} />
            <TagTemplate className='border-none rounded-md !text-primaryColor !bg-[#DDEBEB]' value={'Programming'} />
            <TagTemplate className='border-none rounded-md !text-primaryColor !bg-[#DDEBEB]' value={'Artificial Intelligence'} />
            <TagTemplate className='border-none rounded-md !text-primaryColor !bg-[#DDEBEB]' value={'Home Economics'} />
          </div>
        </Form>
      </div>

      {/* Popular Courses */}
      <div className=''>
        <LearningPaths className='bg-white !py-2 mb-10' />
      </div>

      {/* Trending Courses Section with Cards */}
      <div className='relative mb-20'>
        <div className="bg-primaryColor p-6 h-[12rem] pt-4 rounded-xl absolute w-full m">
          <h2 className="text-lg font-semibold text-white ">Trending Courses</h2>
        </div>
        {/* Grid for Course Cards on the Teal Background */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 pt-14 px-2">
          {/* {courses.map((course, index) => ( */}
          <CourseCard />
          <CourseCard />
          <CourseCard />
          <CourseCard />
          {/* ))} */}
        </div>
        <ButtonTemplate className='mt-6 mx-6 !px-2 justify-between flex' label={'Show more'} icon={<FundProjectionScreenOutlined />} />
      </div>



      {/* Top Rated Courses */}
      <TopRatedCourses />
      <ButtonTemplate className='mb-8 mx-6 !px-2 justify-between flex' label={'Show fewer'} icon={<FundProjectionScreenOutlined />} />


    </div>
  )
}
