import { CheckOutlined } from '@ant-design/icons'
import React from 'react'
import ButtonTemplate from "@/components/ui/button-template";
import TagTemplate from "@/components/ui/tag-template";
import courseFrame from '@/public/course-frame.png'
import mtn from '@/public/mtn.png'
import rating from '@/public/rating.svg'
import { Divider, Rate } from "antd";
import Image from 'next/image'
import { useState } from "react";
import Testimonials from '../testimonials';
import { Course } from '@/types';
const checklistItems = [
    "From basics of After Effects",
    "Shape Modifiers Animation",
    "Types of Keyframes",
    "Morphing Animation",
    "Lettering Animation",
    "You will learn Advanced Motion tracking",
    "You will learn how to create callout professional Titles",
    "You will learn how to make Modern Gradient Liquid Title Animations",
    "Animation Principles",
    "You will learn how to create up to date Infographics and charts",
    "Types of Graph editors",
    "Types of Keyframes", // Duplicate from image included for accuracy
];

type InformationPageProps={
    course?: Course
}


export default function InformationPage({course}: InformationPageProps) {
    return (
        <div>{/* Course Description */}
            <div className="mt-4 text-gray-700 space-y-3 text-xs mb-8">
                {course?.description}
            </div>

            {/* Companies */}
            {/* <div className="mb-8">
                <div className="text-black font-semibold text-lg mb-8">Top Companies looking for this skill</div>
                <div>
                    <Image src={mtn} height={80} width={80} alt='course frame' />,

                </div>
            </div> */}

            {/* What you will get */}
            {/* <div className="mb-8">
                <div className="text-black font-semibold text-lg mb-4">{"What you'll get from this course"}</div>
                <div>
                    <div className="grid grid-cols-2 gap-3 text-xs text-gray-700">
                        {checklistItems.map((item, index) => (
                            <div key={index} className="flex items-center space-x-2">
                                <CheckOutlined className="text-[#6F6CF0]" />
                                <span>{item}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div> */}

            {/* Reviews */}
            {/* <div className="mb-8">
                <div className="text-black font-semibold text-lg mb-2">{"Reviews"}</div>
                <div>
                    <Testimonials />
                </div>
            </div> */}
        </div>
    )
}
