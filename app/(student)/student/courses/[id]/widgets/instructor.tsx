import React from 'react'
import courseFrame from '@/public/course-frame.png'
import Image from 'next/image'
import { ReadFilled, ReadOutlined, StarFilled, UsergroupAddOutlined, UserOutlined } from '@ant-design/icons'
import CourseCard from '@/components/ui/course-template'
import OtherCourseCard from './other-courses-card'
import { Teacher } from '@/types'
import { Avatar } from 'antd'


type InstructorPageProps={
    instructor?: Teacher
}

export default function Instructor({instructor}:InstructorPageProps) {
  return (
    <div className='mt-8 text-black'>
      <h1 className="text-xl font-semibold mb-4 ">
        About Instructor
      </h1>
      {/* Course Image */}
      <div className="relative">
        <div className='flex justify-center'>
      <Avatar size={128} icon={<UserOutlined />} />
      </div>
    
        {/* <Image src={courseFrame} height={800} width={800} alt='course frame' /> */}
        <div className='pr-6'>
          <div className='mt-4 text-base font-semibold'>{instructor?.name}</div>
          <div className='mt-2 text-sm text-[#737C88] font-medium'>({instructor?.specialization})</div>
          <div className='flex mt-2 w-full justify-between '>
            <div>
              <span><StarFilled className='text-yellow-500 pr-2' /></span>
              <span className='font-semibold'>{instructor?.rating} Instructor Rating</span>
            </div>
            <div>
              <span><ReadFilled className='text-[#CF596F] pr-2' /></span>
              <span className='font-semibold'>12 Courses</span>
            </div>
            <div>
              <span><UsergroupAddOutlined className='text-[#3368CD] pr-2' /></span>
              <span className='font-semibold'>12 Courses</span>
            </div>
          </div>
          {/* <div className='mt-4'>
{instructor?.}

          </div> */}
          {/* <div className='my-8'>
          <div className='my-4 text-base font-semibold'>Other Courses from this Instructor</div>
          <div className='flex space-x-4'>
          <OtherCourseCard />
          <OtherCourseCard />
          <OtherCourseCard />
          </div>
          </div> */}
        </div>


      </div>
    </div>
  )
}
