import CollapseTemplate from '@/components/ui/collapse-template'
import { Curriculum } from '@/types';
import { CheckOutlined, LockFilled, LockOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { div } from 'framer-motion/client';
import React, { useEffect, useState } from 'react'


type CurriculumPageProps = {
  curriculum?: Curriculum
}
export default function CurriculumPage({ curriculum }: CurriculumPageProps) {

  const [curriculumData, setCurriculumData] = useState<any>([])
  useEffect(() => {
    setCurriculumData(
      curriculum?.sections.map((sectionData, index) => (
        {
          key: index,
          label: sectionData.title,
          children:
          
        (  <div className='space-y-4'>
         { sectionData?.items?.map((item) => (
            <>
            <div className='flex items-center'><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>{item.title}</div></div> <div className=''></div> </div>
            <div className='flex items-center'><div className='flex-1 flex items-center text-gray-500'> <div>{item.description}</div></div> <div className=''></div> </div>
            </>
          ))}
          </div>)
            
        }
      ))
    )
  }, [curriculum])
  const curriculumItems = [
    {
      key: '1',
      label: 'Unit 1 - Introduction',
      children:

        <div className='space-y-4'>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M1: Introduction</div></div> <div className=''><CheckOutlined className='text-[1rem] text-primaryColor' /></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M2: Job opportunities (career profile)</div></div> <div className=''></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M3: How to get the most out of this course!</div></div> <div className=''><LockOutlined className='text-[1rem] text-black' /></div> </div>
        </div>
    },
    {
      key: '2',
      label: 'Unit 2 - Learning Basics of Animation',
      children:

        <div className='space-y-4'>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M1: Introduction</div></div> <div className=''><CheckOutlined className='text-[1rem] text-primaryColor' /></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M2: Job opportunities (career profile)</div></div> <div className=''></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M3: How to get the most out of this course!</div></div> <div className=''><LockOutlined className='text-[1rem] text-black' /></div> </div>
        </div>
    },
    {
      key: '3',
      label: 'Unit 3 - Software and physics',
      children:

        <div className='space-y-4'>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M1: Introduction</div></div> <div className=''><CheckOutlined className='text-[1rem] text-primaryColor' /></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M2: Job opportunities (career profile)</div></div> <div className=''></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M3: How to get the most out of this course!</div></div> <div className=''><LockOutlined className='text-[1rem] text-black' /></div> </div>
        </div>
    },
    {
      key: '4',
      label: 'Unit 4 - After Effects and Scale',
      children:

        <div className='space-y-4'>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M1: Introduction</div></div> <div className=''><CheckOutlined className='text-[1rem] text-primaryColor' /></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M2: Job opportunities (career profile)</div></div> <div className=''></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M3: How to get the most out of this course!</div></div> <div className=''><LockOutlined className='text-[1rem] text-black' /></div> </div>
        </div>
    },
    {
      key: '5',
      label: 'Unit 5 - Rotation and movement of complex objects',
      children:

        <div className='space-y-4'>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M1: Introduction</div></div> <div className=''><CheckOutlined className='text-[1rem] text-primaryColor' /></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M2: Job opportunities (career profile)</div></div> <div className=''></div> </div>
          <div className='flex items-center '><div className='flex-1 flex items-center'><PlayCircleOutlined className='mr-4 text-[1.4rem]' /> <div>M3: How to get the most out of this course!</div></div> <div className=''><LockOutlined className='text-[1rem] text-black' /></div> </div>
        </div>
    },

  ];
  return (
    <div className='mt-8'>
      <CollapseTemplate items={curriculumData} />
    </div>
  )
}
