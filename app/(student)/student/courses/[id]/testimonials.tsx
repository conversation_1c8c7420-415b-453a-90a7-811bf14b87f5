import { Avatar } from "antd";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    time: "about an hour ago",
    message:
      "Access to IBM marketing what I was looking for and <PERSON> got me the solution",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
  },
  {
    name: "<PERSON><PERSON>",
    time: "about an hour ago",
    message:
      "Upon completion of this ipsum dolor sit amet, consectetur adipiscing elit. Odio dictum morbi odio facilisi. At posuere purus, eget pretium sem nec feugiat.",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    name: "<PERSON><PERSON>",
    time: "about an hour ago",
    message:
      "Access to IBM marketing what I was looking for and <PERSON> got me the solution",
    avatar: "https://randomuser.me/api/portraits/women/68.jpg",
  },
];

export default function Testimonials() {
  return (
    <div className="flex justify-start space-x-6 m-6">
      {testimonials.map((testimonial, index) => (
        <div key={index} className="relative">
          {/* Side Shadow Effect */}
          <div className="absolute inset-0 translate-x-2 translate-y-2 bg-[#737373] rounded-2xl"></div>
          
          {/* Testimonial Card */}
          <div className="relative bg-white p-6 rounded-2xl shadow-lg w-[14rem] text-center">
            <Avatar size={64} src={testimonial.avatar} />
            <h3 className="font-semibold mt-3 text-black line-clamp-1">{testimonial.name}</h3>
            <p className="text-stone-400 text-xs line-clamp-1">{testimonial.time}</p>
            <p className="mt-2 text-gray-700 line-clamp-2">{testimonial.message}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
