'use client'
import ButtonTemplate from "@/components/ui/button-template";
import TagTemplate from "@/components/ui/tag-template";
import rating from '@/public/rating.svg'
import { ArrowLeftOutlined, AudioOutlined, HeartOutlined, MenuOutlined, ShoppingCartOutlined, ShoppingOutlined, UserOutlined, VideoCameraOutlined } from "@ant-design/icons";
import { Divider, Rate } from "antd";
import Image from 'next/image'
import { useEffect, useState } from "react";
import InformationPage from "./widgets/information";
import Instructor from "./widgets/instructor";
import { useFetch } from "@/hooks/useFetch";
import { Loader } from "@/components/general/loader";
import { CourseResponseData } from "@/types";
import CurriculumPage from "./widgets/curriculum";
import Link from "next/link";
import { CartLogics } from "../../cart/_logics/cart_logics";

interface PageDetails {
  id: string
}
const CoursePageDetails = ({ id }: PageDetails) => {
  const { data, loading, error } = useFetch(`/course/${id}`)
  const [courseDetails, setCourseDetails] = useState<CourseResponseData>()
   const { addToCart, loading:addingCourse, addToWishlist } = CartLogics()


  useEffect(() => {
    console.log('here is id', id)
    setCourseDetails(data?.data)
  }, [data])


  const [activeTab, setActiveTab] = useState('Information')

  if (loading) return <Loader />

  const totalDurationMinutes = courseDetails?.curriculum?.sections.reduce((total, section) => {
    const sectionDuration = section.items.reduce((sum, item) => {
      return sum + (item.duration_minutes ?? 0);
    }, 0);
    return total + sectionDuration;
  }, 0);
  
  
  // Convert to hours and minutes
  const hours = Math.floor(totalDurationMinutes!/ 60);
  const minutes = totalDurationMinutes! % 60;


  return (
    <div className="w-full overflow-y-scroll p-4">
      {/* Course Title */}
      <Link href="/student/courses" className="gap-x-4 flex items-center my-4" passHref>
      <ArrowLeftOutlined className="!text-black" /> <div className="text-black">Back</div>
      </Link>

      {/* Course Content Layout */}
      <div className="flex flex-wrap gap-x-4 ">
        {/* Left Section (Image & Tabs) */}
        <div className=" flex-1 mb-10">
          <h1 className="text-3xl font-bold mb-4 text-black">
            {courseDetails?.course.code} - {courseDetails?.course.name}
          </h1>
          {/* Course Image */}
          <div className="relative">
            <Image src={ `${courseDetails?.cover_image && courseDetails?.cover_image.startsWith('iVBOR')? 'data:image/png;base64': 'data:image/jpeg;base64'},${courseDetails?.cover_image}`} height={800} width={1000} alt='course frame' />,

          </div>

          {/* Tabs Section */}

          <div className="flex  mt-4">
            <TagTemplate
              handleClick={() => {
                setActiveTab('Information')
              }}
              className={activeTab === 'Information' ? 'border-none rounded-2xl ' : 'border-primaryColor !bg-white !text-primaryColor rounded-2xl '} value={'Information'} />
            <TagTemplate handleClick={() => {
              setActiveTab('Curriculum')
            }} className={activeTab === 'Curriculum' ? 'border-none rounded-2xl' : 'border-primaryColor !bg-white !text-primaryColor rounded-2xl '} value={'Curriculum'} />
            <TagTemplate
              handleClick={() => {
                setActiveTab('Instructor')
              }}
              className={activeTab === 'Instructor' ? 'border-none rounded-2xl' : 'border-primaryColor !bg-white !text-primaryColor rounded-2xl '} value={'Instructor'} />


          </div>

          {
            activeTab === 'Information' ?
              <InformationPage course={courseDetails?.course} /> :
              activeTab === 'Curriculum' ?
                <CurriculumPage curriculum={courseDetails?.curriculum} /> :
                <Instructor instructor={courseDetails?.teacher} />
          }
        </div>


        {/* Right Sidebar (Course Details) */}
        <div className="flex mb-10">
          <div className="p-4 border rounded-lg  text-black h-max">
            {/* Price Section */}
            <div className=" items-start ">
              <div className="items-start flex ">
                <span className="text-xs   text-black mr-2">Full course</span>
                <span className="text-2xl font-bold text-primaryColor  ">GHS {courseDetails?.course?.base_price}</span>
              </div>
              {/* <div className="my-2">
                <span className="line-through text-yellow-500 mr-4">GHS 5</span>
                <TagTemplate className="!bg-[#C7C6F9] !text-black" value={"-5%"} />
                <span className="text-purple-600"></span>
              </div> */}
              {/* Courses */}
              <div className="text-[#505050] font-bold text-lg my-4">
                This course includes:
              </div>
              <div className="space-y-4 text-[#8A8A8A] text-xs">
                <div>  <span className="mr-3"><UserOutlined /></span>  <span>{courseDetails?.total_student_enrolled} Student Enrolled</span></div>
                <div><span className="mr-3"><VideoCameraOutlined /> </span> 
                 {/* <span>{courseDetails?.curriculum.sections.reduce((count, section) => count + section.items.length, 0)} Lessons (
                  {((courseDetails?.curriculum.sections.reduce((count, section) => count + section.items.reduce((timeCount, item) => timeCount + (item.duration_minutes ?? 0), 0), 0) ?? 0) / 60)}
                  -hrs  ) </span> */}
                  <span>
                  { courseDetails?.curriculum && courseDetails?.curriculum.sections.reduce((count, section) => count + section.items.length, 0)} Lessons ({hours}hrs {minutes} mins)
                  </span>
                  </div>
                <div>    <span className="mr-3"><MenuOutlined /></span>  <span>{courseDetails?.curriculum && courseDetails?.curriculum.sections.length} Units</span></div>
                <div> <span className="mr-3"><ShoppingOutlined /></span>  <span>Available on the app</span></div>
                <div> <span className="mr-3"><AudioOutlined /></span>  <span>Audio: English</span></div>
              </div>

              {/* Cart */}
              <div className="my-4 flex space-x-2 w-full" >
                <div className="w-full">
                  <ButtonTemplate handleClick={()=>{
                    addToCart(courseDetails?.course?.id!)
                  }} className="flex-1  w-[100%]" label={addingCourse? 'Adding to Cart...': "Add to Cart"} />
                </div>
                {/* Add to wishlist */}
                <ButtonTemplate
                disabled={addingCourse}
                handleClick={()=>{
                  addToWishlist(courseDetails?.course?.id!)
                }}
                className={`!px-2 ${addingCourse? 'border-stone-500 ': ''}`} label={<HeartOutlined className="" />} />
              </div>

              {/* Buy Now */}
              <div className="my-4 flex space-x-2 w-full" >
                <div className="w-full">
                  <ButtonTemplate className="flex-1  w-[100%] bg-primaryColor text-white" label={"Buy Now"} icon={<ShoppingCartOutlined />} />
                </div>
              </div>

              <Divider />
              {/* Rating */}
              <div className="flex items-center">
                <div className="flex pr-2">
                  <Image src={rating} height={48} width={48} alt='course frame' />
                </div>
                <div className=" w-[7rem]">
                  <div>Rating</div>

                </div>
                <div className="flex space-x-2">
                  <div>{courseDetails?.teacher.rating}</div>
                  <div><Rate className="text-sm w-[7rem]" value={courseDetails?.teacher.rating} /></div>
                </div>
              </div>


              {/* Publisher */}
              <div>
                <div className="flex items-center justify-between">
                  <div className="text-[#505050] font-bold text-lg my-4">
                    Publisher
                  </div>
                  <div>
                    <TagTemplate className="!bg-[#C7C6F9] !text-black" value={"View Profile"} />
                  </div>

                </div>
                <div className="flex items-center mb-2">
                  <img
                    className="w-8 h-8 rounded-full"
                    src="/rename.webp" // Replace with actual avatar path
                    alt="Avatar"
                  />
                  <span className="ml-2  text-md font-semibold">{courseDetails?.teacher?.name}</span>
                </div>
                <div className="w-[18rem] text-[#8A8A8A] text-sm">{courseDetails?.teacher?.introduction}</div>
              </div>
            </div>

          </div>
        </div>

      </div>

    </div>
  );
};

export default CoursePageDetails;
