import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form, Spin } from "antd";
import React, { useEffect } from "react";
import { useState } from "react"
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useCartStore } from '@/store/cartStore';

export const CoursesLogics = () => {
    const router = useRouter();
    const { request } = useApi();
    const { showNotification, destroyNotifications } = useNotification();
    const [loading, setLoading] = useState(false);
    const [authForm] = Form.useForm();
    const {cart} = useCartStore();
    
    
    

    //Search Course
    async function searchCourse(searchQuery: any) {
        try {
            console.log('data', searchQuery);
            // showNotification('success', 'Signing In', 'Signing into your account...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("GET", "/search/courses", null, "multipart/form-data",
                {
                    "query": searchQuery,
                    "page": 1,
                    "limit": 10,

                }

            );
            setLoading(false);
            // destroyNotifications();
            if (requestResponse && requestResponse?.status === 200) {
                console.log('response',requestResponse?.data?.data?.courses)
                return requestResponse?.data?.data?.courses;

            } else {
                showNotification('error', 'Error searching courses', requestResponse.data.detail);

            }



        } catch (error) {

        }
    }
    // async function addToCart(courseID: string) {
    //     try {
    //         console.log('courseID', courseID);
    //         showNotification('success', 'Adding to Cart', 'Adding item to your cart...', true, <Spin />);

    //         setLoading(true);
    //         const requestResponse: any = await request("POST", `/cart/courses/${courseID}`, null, "multipart/form-data",
                

    //         );
    //         setLoading(false);
    //         destroyNotifications();
    //         if (requestResponse && requestResponse?.status === 200) {
    //             console.log('response',requestResponse?.data?.data?.courses)
    //             showNotification('success', 'Added to Cart', 'Successfully added Course to cart');

    //             return requestResponse?.data?.data?.courses;

    //         } else {
    //             showNotification('error', 'Error adding to cart', requestResponse.data.detail);

    //         }



    //     } catch (error) {

    //     }
    // }
    
    




    return {
        loading, searchCourse, 

    }
}
