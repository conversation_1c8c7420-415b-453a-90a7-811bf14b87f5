'use client';

import React from 'react';
import { ChatSidebar } from './ChatSidebar';
import { ChatMessageArea } from './ChatMessageArea';
import { DirectChatMessageArea } from './DirectChatMessageArea';

import { ChatProvider, useChatContext } from '../_logics/chat_context';
import { useStudentStore } from '@/store/studentStore';
import { ChatMessageInput } from './ChatMessageInput';

export const ChatContainer = () => {
  const { student } = useStudentStore();
  
  // Format user data for ChatProvider
  const chatUser = student ? {
    id: String(student.id),
    name: `${student.first_name ?? ''} ${student.last_name ?? ''}`.trim() || 'Student',
    type: 'student',
    email: student.email,
    avatar: student.profile_image_path || '/logo.png'
  } : undefined;

  return (
    <ChatProvider user={chatUser}>
      <ChatContent />
    </ChatProvider>
  );
};

// Separate component to access ChatContext
const ChatContent = () => {
  const chatContext = useChatContext();
  const hasActiveRoom = !!chatContext?.state.activeRoomId;

  // Determine if the active room is a direct chat
  const activeRoom = chatContext?.state.rooms.find(room => room.id === chatContext?.state.activeRoomId);
  const isDirectChat = activeRoom && !activeRoom.isGroup;

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat main container */}
      <div className="flex flex-1 overflow-hidden">
        <ChatSidebar />
        <div className="flex-1 flex flex-col">
          {isDirectChat ? <DirectChatMessageArea /> : <ChatMessageArea />}
          {hasActiveRoom && <ChatMessageInput />}
        </div>
      </div>
    </div>
  );
};




