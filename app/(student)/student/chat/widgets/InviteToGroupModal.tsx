'use client';

import React, { useState } from 'react';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { useNotification } from '@/hooks/useNotifs';
import { useChatApiService } from '../_logics/chat_api_service';
import { InviteToGroupModalProps, UserSearchResult } from '../_logics/chat_types';





export const InviteToGroupModal: React.FC<InviteToGroupModalProps> = ({
  isOpen,
  onClose,
  groupId,
  groupName
}) => {
  const { isDark } = useTeacherTheme();
  const { showNotification } = useNotification();
  const { sendChatGroupInvite, searchStudents, searchTeachers } = useChatApiService();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
  const [inviteMessage, setInviteMessage] = useState<string>(`Join my group "${groupName}" on LearnKonnect!`);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [step, setStep] = useState<'search' | 'confirm'>('search');

  // Search for users by name or email
  const validateSearchQuery = (input: string): boolean => {
    // Allow alphanumeric, spaces, dots, @, and common special characters in names/emails
    const validPattern = /^[a-zA-Z0-9\s.@\-_]*$/;
    return validPattern.test(input);
  };

  const validateMessage = (input: string): { isValid: boolean; error?: string } => {
    const trimmed = input.trim();
    
    if (trimmed.length === 0) {
      return { isValid: false, error: 'Message cannot be empty' };
    }
    
    if (trimmed.length > 200) {
      return { isValid: false, error: 'Message must be 200 characters or less' };
    }
    
    if (/[<>]/.test(trimmed)) {
      return { isValid: false, error: 'Message contains invalid characters (< or >)' };
    }
    
    return { isValid: true };
  };

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }
    
    if (!validateSearchQuery(query)) {
      showNotification('error', 'Please use only letters, numbers, and common symbols');
      return;
    }
    
    // Minimum search length
    if (query.trim().length < 2) {
      showNotification('info', 'Please enter at least 2 characters to search');
      return;
    }

    setIsSearching(true);
    try {
      // Search both students and teachers concurrently
      const [students, teachers] = await Promise.all([
        searchStudents(query, 1, 10),
        searchTeachers(query, 1, 10)
      ]);

      const studentResults: UserSearchResult[] = students.map((stu: any) => ({
        id: stu.id || stu._id,
        _id: stu._id || stu.id,
        name: stu.name ?? `${stu.first_name ?? ''} ${stu.last_name ?? ''}`.trim(),
        email: stu.email ?? 'N/A',
        type: 'student',
        user_type: 'student'
      }));

      const teacherResults: UserSearchResult[] = teachers.map((t: any) => ({
        id: t.id || t._id,
        _id: t._id || t.id,
        name: t.name ?? `${t.first_name ?? ''} ${t.last_name ?? ''}`.trim(),
        email: t.email ?? 'N/A',
        type: 'teacher',
        user_type: 'teacher'
      }));

      setSearchResults(studentResults.concat(teacherResults));
      setIsSearching(false);
    } catch (error) {
      console.error('Error searching users:', error);
      showNotification('error', 'Failed to search users');
      setIsSearching(false);
    }
  };

  const handleSelectUser = (user: UserSearchResult) => {
    setSelectedUser(user);
    setStep('confirm');
  };

  const handleSendInvite = async (): Promise<void> => {
    if (!selectedUser) {
      showNotification('warning', 'Please select a user to invite');
      return;
    }
    
    const validation = validateMessage(inviteMessage);
    if (!validation.isValid) {
      showNotification('error', validation.error || 'Please enter a valid invitation message');
      return;
    }
    
    setIsLoading(true);
    try {
      // Prepare the request body according to the required format
      const requestBody = {
        invitee_id: selectedUser.id,
        invitee_type: selectedUser.user_type || 'student',
        message: inviteMessage
      };
      
      console.log('Sending invite with data:', requestBody);
      
      const success = await sendChatGroupInvite(
        groupId,
        requestBody.invitee_id,
        requestBody.invitee_type,
        requestBody.message
      );
      
      if (success) {
        showNotification('success', 'Invite sent successfully!');
        onClose();
      } else {
        throw new Error('Failed to send invite');
      }
    } catch (error) {
      console.error('Error sending invite:', error);
      showNotification('error', 'Failed to send invite. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 overflow-y-auto ${isDark ? 'bg-black/70' : 'bg-black/50'}`}>
      <div className="flex items-center justify-center min-h-screen p-4">
        <div 
          className={`relative w-full max-w-md rounded-lg p-6 ${isDark ? 'bg-[#1E2430]' : 'bg-white'}`}
          onClick={(e) => e.stopPropagation()}
        >
          <button 
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          <h2 className={`text-lg font-medium mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
            Invite to {groupName}
          </h2>
          
          {step === 'search' ? (
            <div>
              <div className="mb-4">
                <label htmlFor="search" className="block text-sm font-medium mb-1 text-gray-300">
                  Search for users
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="search"
                    className={`w-full px-3 py-2 rounded-md border ${
                      isDark 
                        ? 'bg-[#2D3440] border-[#384058] text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    placeholder="Search by username"
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      handleSearch(e.target.value);
                    }}
                  />
                  {isSearching && (
                    <div className="absolute right-3 top-2.5">
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                    </div>
                  )}
                  
                </div>
              </div>
              
              <div className="max-h-60 overflow-y-auto">
                {searchResults.length > 0 ? (
                  <ul className="space-y-2">
                    {searchResults.map((user) => (
                      <li 
                        key={user.id}
                        className={`p-3 rounded-md cursor-pointer hover:bg-opacity-10 hover:bg-white transition-colors ${
                          isDark ? 'text-white' : 'text-gray-900'
                        }`}
                        onClick={() => handleSelectUser(user)}
                      >
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm opacity-70">
                          {user.email} • {user.type.charAt(0).toUpperCase() + user.type.slice(1)}
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : searchQuery ? (
                  <div className={`text-center py-4 text-sm ${
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    {isSearching ? 'Searching...' : 'Sorry No user found to invite'}
                  </div>
                ) : (
                  <div className={`text-center py-4 text-sm ${
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    Search for users by name
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className="mb-4 p-4 rounded-lg bg-opacity-10 bg-white">
                <h3 className="font-medium text-black mb-3">Invite Details</h3>
                <div className="mb-4">
                  <div className="text-sm text-black mb-1">To: {selectedUser?.name || selectedUser?.email}</div>
                  <div className="relative">
                    <div className="relative">
                      <textarea
                        className={`w-full px-3 py-2 rounded-md border text-sm ${
                          isDark
                            ? 'bg-[#2D3440] border-[#384058] text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        } ${!validateMessage(inviteMessage).isValid && inviteMessage.length > 0 ? 'border-red-500' : 'border-gray-300'}`}
                        rows={3}
                        placeholder="Add a personal message (optional)"
                        value={inviteMessage}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value.length <= 200) {
                            setInviteMessage(value);
                          }
                        }}
                        onPaste={(e) => {
                          const pastedText = e.clipboardData.getData('text/plain');
                          const validation = validateMessage(pastedText);
                          
                          if (!validation.isValid) {
                            e.preventDefault();
                            showNotification('error', validation.error || 'Invalid content');
                          } else if (pastedText.length + inviteMessage.length > 200) {
                            e.preventDefault();
                            showNotification('warning', 'Pasted message would exceed character limit');
                          }
                        }}
                      />
                      {/* <div className={`text-xs mt-1 text-right ${
                        !validateMessage(inviteMessage).isValid && inviteMessage.length > 0 ? 'text-red-500' : 'text-gray-400'
                      }`}>
                        {!validateMessage(inviteMessage).isValid && inviteMessage.length > 0 
                          ? validateMessage(inviteMessage).error 
                          : `${inviteMessage.length}/200 characters`
                        }
                      </div> */}
                    </div>
                  
                    <div className="text-right text-xs mt-1 text-gray-400">
                      {inviteMessage.length}/200 characters
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setStep('search')}
                  className="px-4 py-2 text-sm font-medium rounded-md text-red-600 hover:text-red-700"
                  disabled={isLoading}
                >
                  Back
                </button>
                <button
                  type="button"
                  onClick={handleSendInvite}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    isLoading
                      ? 'bg-gray-500 cursor-not-allowed'
                      : 'bg-[#006060] hover:bg-[#004d4d]'
                  } text-white`}
                  disabled={isLoading}
                >
                  {isLoading ? 'Sending...' : 'Send Invite'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
