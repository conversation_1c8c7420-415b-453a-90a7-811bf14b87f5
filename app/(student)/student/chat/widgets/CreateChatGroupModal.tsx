'use client';

import React, { useState, useEffect } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { useNotification } from '@/hooks/useNotifs';
import { useChatApiService } from '../_logics/chat_api_service';
import { Course } from '../_logics/chat_types';

interface CreateChatGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CreateChatGroupModal: React.FC<CreateChatGroupModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { isDark } = useTeacherTheme();
  const { showNotification } = useNotification();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [courseId, setCourseId] = useState('');
  const [isPrivate, setIsPrivate] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [courses, setCourses] = useState<Course[]>([]);
  const [loadingCourses, setLoadingCourses] = useState(false);

  const { refreshChatRooms } = useChatContext();
  const { fetchStudentCourses, createChatGroup } = useChatApiService();

  // Fetch student courses when modal opens
  useEffect(() => {
    if (isOpen) {
      const loadCourses = async () => {
        setLoadingCourses(true);
        try {
          const studentCourses = await fetchStudentCourses();

          if (studentCourses && studentCourses.length > 0) {
            setCourses(studentCourses);
          } else {
            setCourses([]);
          }
        } catch (error) {
          console.error('CreateChatGroupModal - Error fetching courses:', error);
          setError('Failed to load courses. Please try again.');
        } finally {
          setLoadingCourses(false);
        }
      };
      loadCourses();
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Group name is required');
      return;
    }
    
    try {
      setIsSubmitting(true);
      setError('');
      
      console.log('Creating chat group:', {
        name,
        description,
        courseId,
        isPrivate
      });

      // Call the API service directly
      const chatGroupData = {
        name,
        description,
        type: 'student', // Since we're in student route
        course_id: courseId,
        is_private: isPrivate,
        initial_members: []
      };

      const result = await createChatGroup(chatGroupData);

      if (!result) {
        throw new Error('Failed to create chat group');
      }

      // Show success notification
      showNotification('success', 'Success', `Chat group "${name}" created successfully!`);

      // Refresh chat rooms to show the new group
      await refreshChatRooms();

      // Reset form
      setName('');
      setDescription('');
      setCourseId('');
      setIsPrivate(true);

      // Notify parent component of success
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating chat group:', error);
      setError('Failed to create chat group. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black opacity-50" onClick={onClose}></div>
      
      <div className={`relative w-full max-w-md p-6 rounded-lg shadow-lg ${isDark ? 'bg-[#1e232e] text-white' : 'bg-white text-gray-800'}`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Create New Chat Group</h2>
          <button
            onClick={onClose}
            className={`p-1 rounded-full ${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Group Name*</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'}`}
              placeholder="Enter group name"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'}`}
              placeholder="Enter group description"
              rows={3}
            />
          </div>



          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Course</label>
            <div className="relative">
              {loadingCourses ? (
                <div className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700' : 'bg-white border-gray-300'} flex items-center`}>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-[#006060] mr-2"></div>
                  <span className="text-sm">Loading courses...</span>
                </div>
              ) : (
                <select
                  value={courseId}
                  onChange={(e) => setCourseId(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${isDark ? 'bg-[#252B42] border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-800'}`}
                >
                  <option value="">Select a course (optional)</option>
                  {courses.length === 0 && (
                    <option value="" disabled>No courses available</option>
                  )}
                  {courses.map((course) => (
                    <option key={course.id} value={course.id}>
                      {course.coursename}
                    </option>
                  ))}
                </select>
              )}
            </div>
            <p className={`text-xs mt-1 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              Select a course to associate with this chat group
            </p>
          </div>

          <div className="mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isPrivate}
                onChange={(e) => setIsPrivate(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Make this group private</span>
            </label>
            <p className={`text-xs mt-1 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              Private groups require invitation to join
            </p>
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className={`px-4 py-2 rounded-md ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#006060] text-white rounded-md hover:bg-[#004e4e] disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Group'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
