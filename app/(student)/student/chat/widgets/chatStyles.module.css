.message-bubble {
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  /* WhatsApp-like bubble padding & shape */
  padding: 0.5rem 0.75rem;
  border-radius: 0.75rem;
  font-size: 0.875rem; /* text-sm */
  line-height: 1.4;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
}

/* Light mode colours */
.message-bubble.incoming {
  background-color: #f3f4f6; /* gray-100 */
  color: #111827; /* gray-900 */
}

.message-bubble.outgoing {
  background-color: #25d366; /* WhatsApp green */
  color: #ffffff;
}

/* Dark mode colours */
.dark .message-bubble.incoming {
  background-color: #2d3445 !important;
  color: #e5e7eb !important;
}

.dark .message-bubble.outgoing {
  background-color: #005c4b !important; /* darker green for dark mode */
  color: #ffffff !important;
}

/* Pseudo-elements for speech bubble tails */
.message-bubble.incoming::after,
.message-bubble.outgoing::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

/* Tail for incoming (left) */
.message-bubble.incoming::after {
  left: -6px;
  top: 8px;
  border-right-color: #f3f4f6; /* gray-100 */
}

/* Tail for outgoing (right) */
.message-bubble.outgoing::after {
  right: -6px;
  top: 8px;
  border-left-color: #25d366; /* WhatsApp green */
}

/* Dark mode overrides for tails */
:global(.dark) .message-bubble.incoming::after {
  border-right-color: #2d3445;
}

:global(.dark) .message-bubble.outgoing::after {
  border-left-color: #005c4b;
}