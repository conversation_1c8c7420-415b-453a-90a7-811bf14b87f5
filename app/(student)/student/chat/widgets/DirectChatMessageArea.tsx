'use client';

import React, { useMemo, useRef, useEffect, useState } from 'react';
import { VariableSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useChatContext } from '../_logics/chat_context';
import { useChatWebSocket } from '../_logics/useChatWebSocket';
import { ChatBubble } from '../components/ChatBubble';
import { useChatApiService } from '../_logics/chat_api_service';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { ChatMessage, MessageRowProps } from '../_logics/chat_types';

export const DirectChatMessageArea = () => {
  const [userMessages, setUserMessages] = useState<ChatMessage[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const { state, currentUser } = useChatContext();
  const { getUserMessages } = useChatApiService();
  // Start / keep WebSocket for the active room
  useChatWebSocket(state.activeRoomId);

  const { isDark } = useTeacherTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);

  // Active room and messages
  const activeRoom = state.rooms.find(r => r.id === state.activeRoomId);
  const rawMessages = state.activeRoomId ? state.messages[state.activeRoomId] || [] : [];

  // Fetch user messages when component mounts or active room changes
  useEffect(() => {
    const fetchUserMessages = async () => {
      if (!state.activeRoomId || !activeRoom || activeRoom.isGroup) {
        return; // Only fetch for direct chats
      }

      try {
        setIsFetching(true);
        const { messages, totalPages } = await getUserMessages(page);
        if (messages) {
          // Filter messages for the current direct chat conversation
          const filteredMessages = messages.filter(msg => {
            // For direct chats, check if the message involves the current user and the recipient
            if (activeRoom && !activeRoom.isGroup) {
              const recipientId = activeRoom.members.find(m => m.id !== currentUser?.id)?.id;
              return (
                (msg.senderId === currentUser?.id && msg.roomId.includes(recipientId || '')) ||
                (msg.senderId === recipientId && msg.roomId.includes(currentUser?.id || ''))
              );
            }
            return false;
          });

          setUserMessages(prev => [...prev, ...filteredMessages]);
          setHasMore(page < totalPages);
        }
      } catch (error) {
        console.error('Error fetching user messages:', error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchUserMessages();
  }, [page, getUserMessages, state.activeRoomId, activeRoom, currentUser?.id]);

  // Build quick map of memberId -> name for sender name fallback
  const memberMap = useMemo(() => {
    const map: Record<string, string> = {};
    activeRoom?.members?.forEach(m => {
      map[m.id] = m.name;
    });
    return map;
  }, [activeRoom?.members]);

  // Combine raw and user messages (filtered by room)
  const filteredUserMessages = useMemo(
    () => userMessages.filter(m => m.roomId === state.activeRoomId),
    [userMessages, state.activeRoomId]
  );

  const messages = useMemo(() => {
    const combined = [...rawMessages, ...filteredUserMessages];
    const sorted = combined.sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    return sorted.map(msg => ({
      ...msg,
      senderName: msg.senderName || memberMap[msg.senderId] || 'Unknown'
    }));
  }, [rawMessages, filteredUserMessages, memberMap]);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle scroll to load more messages
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement;
    const { scrollTop } = target;
    if (scrollTop === 0 && hasMore && !isFetching) {
      setPage(prev => prev + 1);
    }
  };

  // Row renderer for react-window list (unused in current implementation)
  /*
  const MessageRow = ({ index, style, data }: MessageRowProps) => {
    const { messages, currentUserId, isDark } = data;
    const message = messages[index];
    const prevMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    const isCurrentUser = message.senderId === currentUserId;
    const showHeader = !prevMessage || prevMessage.senderId !== message.senderId;
    const showTail = !nextMessage || nextMessage.senderId !== message.senderId;
    const isFirstInGroup = showHeader;
    const isLastInGroup = showTail;
    const isSingleMessage = isFirstInGroup && isLastInGroup;

    const getContainerStyle = () => ({
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
      width: '100%',
      paddingLeft: isCurrentUser ? '20%' : '8px',
      paddingRight: isCurrentUser ? '80px' : '20%',
      marginBottom: isLastInGroup ? '8px' : '1px',
    });

    return (
      <div style={style}>
        <div style={getContainerStyle()}>
          <ChatBubble
            isCurrentUser={isCurrentUser}
            isDark={isDark}
            isFirstInGroup={isFirstInGroup}
            isLastInGroup={isLastInGroup}
            isSingleMessage={isSingleMessage}
            showHeader={showHeader}
            senderName={message.senderName}
            content={message.content}
            timestamp={message.timestamp}
            status={message.status}
          />
          {index === 0 && hasMore && (
            <div className="flex justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            </div>
          )}
        </div>
      </div>
    );
  };
  */

  // Prepare display items for the message list with date headers
  const displayItems = useMemo(() => {
    const items: any[] = [];
    messages.forEach((msg, index) => {
      const date = new Date(msg.timestamp);
      const isToday = date.toDateString() === new Date().toDateString();
      const isYesterday = new Date(date.getTime() + 24 * 60 * 60 * 1000).toDateString() === new Date().toDateString();
      
      // Add date header if it's the first message or date has changed
      if (index === 0 || items[items.length - 1]?.type !== 'header') {
        let label = '';
        if (isToday) {
          label = 'Today';
        } else if (isYesterday) {
          label = 'Yesterday';
        } else {
          label = date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
        }
        items.push({ type: 'header', label });
      }
      
      // Add message item
      items.push({
        type: 'message',
        message: msg,
        msgIndex: index,
      });
    });
    return items;
  }, [messages]);

  // Current user is already available from context

  // Row renderer to handle date headers & messages
  const RowRenderer = ({ index, style, data }: any) => {
    const { messages, currentUserId, isDark } = data;
    const item = displayItems[index];
    
    if (item.type === 'header') {
      return (
        <div style={{ ...style, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <span style={{ fontSize: '13px', fontWeight: 500, color: isDark ? '#d1d5db' : '#6b7280' }}>
            {item.label}
          </span>
        </div>
      );
    }
    
    const message = messages[item.msgIndex];
    const prevMessage = item.msgIndex > 0 ? messages[item.msgIndex - 1] : null;
    const nextMessage = item.msgIndex < messages.length - 1 ? messages[item.msgIndex + 1] : null;
    const isCurrentUser = message.senderId === currentUserId;
    // Use senderName for header display instead of senderId
    const showHeader = !prevMessage || prevMessage.senderName !== message.senderName;
    const showTail = !nextMessage || nextMessage.senderName !== message.senderName;
    const isFirstInGroup = showHeader;
    const isLastInGroup = showTail;
    const isSingleMessage = isFirstInGroup && isLastInGroup;

    const getContainerStyle = () => ({
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
      width: '100%',
      paddingLeft: isCurrentUser ? '20%' : '8px',
      paddingRight: isCurrentUser ? '80px' : '20%',
      marginBottom: isLastInGroup ? '8px' : '1px',
    });

    return (
      <div style={style}>
        <div style={getContainerStyle()}>
          <ChatBubble
            isCurrentUser={isCurrentUser}
            isDark={isDark}
            isFirstInGroup={isFirstInGroup}
            isLastInGroup={isLastInGroup}
            isSingleMessage={isSingleMessage}
            showHeader={showHeader}
            senderName={message.senderName}
            content={message.content}
            timestamp={message.timestamp}
            status={message.status}
          />
          {item.msgIndex === 0 && hasMore && (
            <div className="flex justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Format member count (unused in current implementation)
  // const formatMemberCount = (count: number) => {
  //   if (count < 1000) return count.toString();
  //   return `${(count / 1000).toFixed(1)}k`;
  // };
  
  // Get online user count for active room
  const onlineCount = state.activeRoomId 
    ? (state.onlineUsers[state.activeRoomId] || []).length
    : 0;

  // Get total member count for active room
  const memberCount = activeRoom?.memberCount || 0;


  return (
    <div className="flex flex-col h-full" onScroll={handleScroll}>
      {/* Header */}
      <div className={`${isDark ? 'bg-[#2a2f32] border-[#384058]' : 'bg-[#f0f2f5] border-gray-200'} border-b p-3 py-2 flex items-center sticky top-0 z-20`}>
        <div className="flex items-center flex-1">
          <img 
            src={activeRoom?.avatar || '/logo.png'} 
            alt={activeRoom?.name || 'Chat'} 
            className="w-10 h-10 rounded-full object-cover shadow-md border border-gray-200"
            onError={(e) => {
              e.currentTarget.src = '/logo.png';
            }}
          />
          <div className="ml-3">
            <div className="flex items-center">
              <h2 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {activeRoom?.name || 'Chat'}
              </h2>
            </div>
            <div className={`flex items-center text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
              Online
              <span className="ml-2">• {onlineCount} of {memberCount} online</span>
              {/* <span className="text-xs text-gray-500">{formatMemberCount(groupMembers.length || activeRoom?.memberCount || 0)} members</span> */}
            </div>
          </div>
        </div>
      </div>
      
      {/* Messages Area */}
      <div className="flex-1 overflow-hidden p-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center px-4">
            <div className={`p-4 rounded-full ${isDark ? 'bg-[#2d3445]' : 'bg-gray-100'} mb-4`}>
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className={`text-lg font-medium mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>No messages yet</h3>
            <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              Send a message to start the conversation
            </p>
          </div>
        ) : (
          <div className="h-full">
            <AutoSizer>
              {({ height, width }) => (
                <List
                  ref={listRef}
                  initialScrollOffset={(messages.length - 1) * 120}
                  height={height}
                  itemCount={displayItems.length}
                  itemSize={(index:number)=> displayItems[index].type==='header'? 36: 120} 
                  width={width}
                  itemData={{
                    messages,
                    currentUserId: currentUser?.id,
                    isDark,
                  }}
                >
                  {RowRenderer}
                </List>
              )}
            </AutoSizer>
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
};




