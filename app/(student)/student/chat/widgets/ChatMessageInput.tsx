'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import dynamic from 'next/dynamic';

// Importing type for TS checking, but using dynamic import for the component
import type { Theme } from 'emoji-picker-react';

// Dynamically import EmojiPicker to avoid SSR issues
const EmojiPicker = dynamic(() => import('emoji-picker-react'), { ssr: false });

export const ChatMessageInput = () => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const { sendMessage } = useChatContext();
  const { isDark } = useTeacherTheme();

  // Handle emoji selection
  const handleEmojiClick = (emojiData: any) => {
    const emoji = emojiData.emoji || (emojiData.srcElement && emojiData.srcElement.innerText) || '';
    setMessage(prevMessage => prevMessage + emoji);
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current && 
        !emojiPickerRef.current.contains(event.target as Node) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      console.log('ChatMessageInput: Submitting message:', message);
      try {
        await sendMessage(message);
        console.log('ChatMessageInput: Message sent successfully');
        setMessage('');
        setShowEmojiPicker(false);
      } catch (error) {
        console.error('ChatMessageInput: Failed to send message:', error);
      }
    }
  };

  return (
    <div className={`${isDark ? 'bg-[#252B42] border-[#384058]' : 'bg-white border-gray-200'} border-t`}>
      <form onSubmit={handleSubmit} className="py-2.5 px-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button
              ref={emojiButtonRef}
              type="button"
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full"
              title="Emoji"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            
            {showEmojiPicker && (
              <div 
                ref={emojiPickerRef}
                className="absolute bottom-12 left-0 z-10" 
              >
                <EmojiPicker 
                  onEmojiClick={handleEmojiClick} 
                  theme={(isDark ? "dark" : "light") as Theme}
                  autoFocusSearch={false}
                />
              </div>
            )}
          </div>
          
          <div className={`flex-1 flex items-center px-4 py-2 rounded-full border ${isDark ? 
            'bg-[#1E2430] border-[#384058]' : 
            'bg-gray-100 border-gray-200'} rounded-full focus-within:ring-1 focus-within:ring-[#006060]`}>
            <input
              type="text"

              placeholder="Type a message..."
              className={`flex-1 bg-transparent border-none p-0 ${isDark ? 
                'text-white placeholder-gray-400' : 
                'text-gray-900 placeholder-gray-500'} focus:outline-none text-sm`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
            {/* <span className="text-gray-400 hover:text-gray-600 ml-1 p-1 rounded-full cursor-not-allowed" title="File attachments are not available">
              <Paperclip className="w-5 h-5" />
            </span> */}
          </div>
          
          <button
            type="submit"
            disabled={!message.trim()}
            className={`p-2 rounded-full ${message.trim() ? 'text-[#006060] hover:bg-gray-100' : 'text-gray-400'} transition-colors`}
            title="Send message"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};
