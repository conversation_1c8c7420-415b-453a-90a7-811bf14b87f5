'use client';

import { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';

import { useChatApiService } from './chat_api_service';
import { ChatContextType, ChatMessage, ChatRoom, ChatState, ChatGroup, ChatGroupMember, ChatMember } from './chat_types';
import { WebSocketService } from './websocketService';

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);
interface ChatProviderProps {
  children: ReactNode;
  user?: {
    id: string;
    name: string;
    type: string;
    email?: string;
    avatar?: string;
  };
}

// Context provider
export const ChatProvider = ({ children, user }: ChatProviderProps) => {
  const [state, setState] = useState<ChatState>({
    activeRoomId: null,
    rooms: [],
    messages: {},
    isLoading: true,
    error: null,
    onlineUsers: {}
  });
  
  const wsService = WebSocketService.getInstance();

  

  // Get service
  const {
    fetchAllChatGroupsAndPages,
    fetchChatGroupMembers,
    fetchChatGroupMessages,
    fetchDirectMessages,
    leaveChatGroup,
    getUserMessages
  } = useChatApiService();

  // Leave a chat group
  const handleLeaveGroup = async (groupId: string): Promise<boolean> => {
    try {
      const success = await leaveChatGroup(groupId);
      if (success) {
        // Remove the group from the rooms list
        setState(prevState => ({
          ...prevState,
          rooms: prevState.rooms.filter(room => room.id !== groupId),
          activeRoomId: prevState.activeRoomId === groupId ? null : prevState.activeRoomId
        }));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error leaving group:', error);
      return false;
    }
  };

  // Update user presence in a room
  const updateUserPresence = useCallback((roomId: string, onlineUserIds: string[]) => {
    setState(prevState => ({
      ...prevState,
      onlineUsers: {
        ...prevState.onlineUsers,
        [roomId]: onlineUserIds
      },
      rooms: prevState.rooms.map(room => {
        if (room.id === roomId) {
          return {
            ...room,
            members: room.members.map(member => ({
              ...member,
              isOnline: onlineUserIds.includes(member.id)
            }))
          };
        }
        return room;
      })
    }));
  }, []);

  // Add a new message to the state
  const addMessage = useCallback((message: ChatMessage) => {
    setState(prevState => {
      const roomMessages = prevState.messages[message.roomId] || [];
            const isDuplicate = roomMessages.some(msg => {
        if (msg.id === message.id) return true;
        const sameSender = msg.senderId === message.senderId;
        const sameContent = msg.content === message.content;
        const timeDiff = Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime());
        return sameSender && sameContent && timeDiff < 2000; // 2-second window
      });

      if (isDuplicate) {
        return prevState;
      }

      return {
        ...prevState,
        messages: {
          ...prevState.messages,
          [message.roomId]: [...roomMessages, message].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        },
        rooms: prevState.rooms.map(room => {
          if (room.id === message.roomId) {
            return {
              ...room,
              lastMessage: message.content,
              timestamp: message.timestamp,
            };
          }
          return room;
        })
      };
    });
  }, []);

  // Function to load chat groups and direct chats
  const loadChatGroups = async () => {
    try {
      setState(prevState => ({ ...prevState, isLoading: true, error: null }));

      // Load group chats
      const chatGroups = await fetchAllChatGroupsAndPages();

      // Load direct chats from user messages
      const { messages: userMessages } = await getUserMessages(1, 100); // Get more messages to find all conversations
      console.log('Loaded user messages for direct chats:', userMessages.length);

      const groupRooms = chatGroups && chatGroups.length > 0 ? chatGroups.map(mapChatGroupToRoom) : [];

      // Create direct chat rooms from user messages
      const directRooms: ChatRoom[] = [];
      const seenConversations = new Set<string>();

      userMessages.forEach(msg => {
        // Extract conversation participants from chat_id
        // Format: direct_student:userId1_student:userId2
        const chatIdMatch = msg.roomId.match(/direct_student:([^_]+)_student:([^_]+)/);
        if (chatIdMatch) {
          const [, userId1, userId2] = chatIdMatch;
          const otherUserId = userId1 === user?.id ? userId2 : userId1;
          const conversationKey = [user?.id, otherUserId].sort().join('_');

          if (!seenConversations.has(conversationKey)) {
            seenConversations.add(conversationKey);

            // Find the other participant's name from messages
            const otherUserMessage = userMessages.find(m => m.senderId === otherUserId);
            const otherUserName = otherUserMessage?.senderName || 'Unknown User';

            console.log('Creating direct room for:', {
              otherUserId,
              otherUserName,
              chatId: msg.roomId,
              currentUser: user?.id
            });

            directRooms.push({
              id: `direct-${otherUserId}`, // Use a simpler ID format for room identification
              name: otherUserName,
              description: 'Direct conversation',
              avatar: '/logo.png',
              lastMessage: msg.content,
              timestamp: msg.timestamp,
              unreadCount: 0,
              isGroup: false,
              memberCount: 2,
              members: [
                {
                  id: user?.id || '',
                  name: user?.name || 'You',
                  avatar: user?.avatar || '/logo.png',
                  isAdmin: false,
                  isOnline: false,
                  user_type: user?.type || 'student'
                },
                {
                  id: otherUserId,
                  name: otherUserName,
                  avatar: '/logo.png',
                  isAdmin: false,
                  isOnline: false,
                  user_type: otherUserMessage?.senderType || 'student'
                }
              ],
              // Store the original chat_id for message filtering
              originalChatId: msg.roomId
            });
          }
        }
      });

      // Combine group and direct rooms
      const allRooms = [...groupRooms, ...directRooms];
      console.log('Created direct rooms:', directRooms.length);
      console.log('Total rooms (group + direct):', allRooms.length);

      // Remove duplicates based on room ID
      const uniqueRooms = allRooms.filter((room, index, self) =>
        index === self.findIndex(r => r.id === room.id)
      );

      setState(prevState => ({
        ...prevState,
        rooms: uniqueRooms,
        isLoading: false,
        activeRoomId: null
      }));
    } catch (error) {
      console.error('Error loading chat groups:', error);
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        error: 'Failed to load chat groups'
      }));
    }
  };

  // Load chat groups on component mount
  useEffect(() => {
    loadChatGroups();
  }, []);

  // Core functions
  const setActiveRoom = async (roomId: string) => {
    try {
      // Set loading state
      setState(prevState => ({
        ...prevState,
    
        activeRoomId: roomId
      }));

      // Connect to the WebSocket for this room (group vs direct)
      const activeRoom = state.rooms.find(r => r.id === roomId);
      if (activeRoom) {
        if (activeRoom.isGroup) {
          await wsService.connectToGroupChat(roomId);
        } else {
          const currentUserId = user?.id || state.currentUser?.id;
          const recipient = activeRoom.members.find(m => m.id !== currentUserId);
          if (recipient) {
            console.log('Connecting to direct chat with recipient:', recipient.id, 'sender:', user?.name);
            try {
              await wsService.connectToDirectChat(recipient.id, recipient.user_type || 'student', user?.name);
            } catch (error) {
              console.error('Failed to connect to direct chat WebSocket:', error);
              // Continue without WebSocket connection for now
            }
          }
        }
      }
      
      // Fetch messages for this room
      let messagesResult;
      if (activeRoom?.isGroup) {
        messagesResult = await fetchChatGroupMessages(roomId);
      } else {
        // For direct chats, we'll rely on the DirectChatMessageArea to fetch messages
        // using getUserMessages, so we don't need to fetch here
        messagesResult = { messages: [], pagination: null };
      }
      const { messages } = messagesResult;
      
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        messages: {
          ...prevState.messages,
          [roomId]: messages || []
        },
        // Mark messages as read when switching to this room
        rooms: prevState.rooms.map(room => {
          if (room.id === roomId) {
            return { ...room };
          }
          return room;
        })
      }));
    } catch (error) {
      console.error('Failed to set active room:', error);
      // Update state with error
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        error: 'Failed to load messages',
        activeRoomId: roomId,
        messages: {
          ...prevState.messages,
          [roomId]: prevState.messages[roomId] || []
        }
      }));
    }
  };

  const searchChats = (query: string) => {
    if (!query) {
      // If no query, reload all chat groups
      loadChatGroups();
      return;
    }

    setState(prevState => {
      const lowerQuery = query.toLowerCase();
      const filteredRooms = prevState.rooms.filter(room =>
        room.name.toLowerCase().includes(lowerQuery) ||
        room.lastMessage.toLowerCase().includes(lowerQuery)
      );

      return {
        ...prevState,
        rooms: filteredRooms
      };
    });
  };

  // const refreshChatRooms = async () => {
  //   // refresh chat rooms
  //   await loadChatGroups();
  // };

  const fetchRoomMembers = async (roomId: string): Promise<ChatMember[]> => {
    try {
      return await fetchChatGroupMembers(roomId);
    } catch (error) {
      console.error('Error fetching room members:', error);
      return [];
    }
  };

  // Send a new message to the active chat room
  const sendMessage = async (content: string) => {
    if (!state.activeRoomId) {
      throw new Error('No active room selected');
    }

    try {
      const timestamp = new Date();
      
      // Send message via WebSocket
      const messageData = {
        type: 'chat_message',
        content,
        roomId: state.activeRoomId,
        timestamp: timestamp.toISOString(),
        messageType: 'text'
      };
      await wsService.sendMessage(messageData);
      
    

    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  };

  // Function to initiate a direct chat with a recipient
  const startDirectChat = async (recipient: { id: string; name: string; avatar?: string; user_type?: string }) => {
    try {
      const currentUserId = user?.id || state.currentUser?.id;
      if (!currentUserId) throw new Error('Current user not defined');

      // Attempt to find an existing direct room with this recipient
      let existingRoom = state.rooms.find(r =>
        !r.isGroup && r.members.some(m => m.id === recipient.id)
      );

      let roomId = existingRoom?.id;
      if (!existingRoom) {
        // Create a temporary local room id
        roomId = `direct-${[currentUserId, recipient.id].sort().join('-')}`;
        const newRoom: ChatRoom = {
          id: roomId,
          name: recipient.name,
          description: '',
          avatar: recipient.avatar || '/logo.png',
          lastMessage: '',
          timestamp: new Date(),
          unreadCount: 0,
          isGroup: false,
          memberCount: 2,
          members: [
            {
              id: currentUserId,
              name: user?.name || state.currentUser?.name || 'You',
              avatar: user?.avatar || state.currentUser?.avatar,
              isAdmin: false,
              isOnline: true,
              user_type: state.currentUser?.type || 'student'
            },
            {
              id: recipient.id,
              name: recipient.name,
              avatar: recipient.avatar || '/logo.png',
              isAdmin: false,
              isOnline: true,
              user_type: recipient.user_type || 'student'
            }
          ]
        };
        // Prepend to rooms list for quick access
        setState(prev => ({ ...prev, rooms: [newRoom, ...prev.rooms] }));
        existingRoom = newRoom;
      }

      // Connect websocket
      await wsService.connectToDirectChat(recipient.id, recipient.user_type || 'student');

      // Fetch historical messages
      const { messages } = await fetchDirectMessages(recipient.id, recipient.user_type);

      setState(prev => ({
        ...prev,
        activeRoomId: roomId!,
        messages: {
          ...prev.messages,
          [roomId!]: messages
        }
      }));
    } catch (error) {
      console.error('Failed to start direct chat:', error);
    }
  };

  // Context value
  const contextValue: ChatContextType = {
    state,
    setActiveRoom,
    searchChats: (query: string) => {
      // Implement search functionality if needed
      console.log('Searching for:', query);
    },
    refreshChatRooms: loadChatGroups,
    fetchRoomMembers,
    leaveGroup: handleLeaveGroup,
    sendMessage,
    addMessage,
    updateUserPresence,
    currentUser: user,
    startDirectChat
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook for using the chat context
export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};


export const mapChatGroupToRoom = (chatGroup: ChatGroup): ChatRoom => {
  // Format the last message preview
  let lastMessage = 'No messages yet';
  let lastMessageTime = new Date(chatGroup.created_at);
  
  if (chatGroup.last_message) {
    const messagePreview = chatGroup.last_message.content.length > 30 
      ? `${chatGroup.last_message.content.substring(0, 30)}...` 
      : chatGroup.last_message.content;
    lastMessage = messagePreview;
    lastMessageTime = new Date(chatGroup.last_message.timestamp);
  }
  
  return {
    id: chatGroup.id,
    name: chatGroup.name,
    description: chatGroup.description,
    avatar: '/logo.png',
    lastMessage,
    timestamp: lastMessageTime,
    unreadCount: 0, 
    isGroup: chatGroup.type !== 'page',
    memberCount: chatGroup.member_count || 0,
    members: chatGroup.members ? chatGroup.members.map((member: ChatGroupMember) => ({
      id: member.id,
      name: member.name,
      avatar: member.avatar || '/logo.png',
      isAdmin: member.is_admin || false,
      isOnline: member.isOnline || false,
      user_type: member.user_type
    })) : []
  };
};






