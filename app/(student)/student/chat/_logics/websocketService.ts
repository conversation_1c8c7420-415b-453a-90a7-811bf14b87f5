import Cookies from 'js-cookie';

// Function to get token from cookies
const getToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  const token = Cookies.get('access_token');
  console.log('Retrieved token from cookies:', token ? `${token.substring(0, 10)}...` : 'No token found');
  return token || null;
};

type MessageHandler = (message: any) => void;
interface ConnectionHandler {
  (status: string): void;
}

export class WebSocketService {
  private static instance: WebSocketService;
  private socket: WebSocket | null = null;
  private messageHandlers: Set<MessageHandler> = new Set();
  private connectionHandlers: Set<ConnectionHandler> = new Set();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 3000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionPromise: Promise<void> | null = null;
  private connectionResolve: (() => void) | null = null;
  private groupId: string | null = null;
  private directRecipientId: string | null = null;
  private recipientType: string | null = null;
  private senderName: string | null = null;
  private chatMode: 'group' | 'direct' | null = null;
  private baseUrl: string;
  private isConnected: boolean = false;
  isExplicitDisconnect: any;

  private constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_WS_BASE || '';
    // console.log('WebSocket base URL:', this.baseUrl);
  }

  private setupEventHandlers(resolve: () => void, reject: (reason?: any) => void) {
    if (!this.socket) {
      console.error('Cannot setup event handlers: WebSocket is null');
      reject(new Error('WebSocket initialization failed'));
      return;
    }

    this.socket.binaryType = 'arraybuffer';

    this.socket.onopen = (event) => {
      console.log('WebSocket connected successfully', {
        url: this.socket?.url?.replace(/token=[^&]*/, 'token=***REDACTED***'),
        readyState: this.socket?.readyState,
        chatMode: this.chatMode,
        recipientId: this.directRecipientId
      });
      
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.setupHeartbeat();
      
      // Send a system message for direct chat connections
      if (this.chatMode === 'direct' && this.directRecipientId) {
        const systemMessage = {
          type: 'system',
          content: 'Direct chat connected',
          timestamp: new Date().toISOString(),
          roomId: `direct-${this.directRecipientId}`,
          recipientId: this.directRecipientId,
          recipientType: this.recipientType
        };
        console.log('Sending direct chat system message:', systemMessage);
        this.messageHandlers.forEach(handler => {
          try {
            handler(systemMessage);
          } catch (error) {
            console.error('Error in message handler:', error);
          }
        });
      }
      
      if (this.connectionResolve) {
        this.connectionResolve();
        this.connectionResolve = null;
      }
      this.notifyConnectionStatus();
      resolve();
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        // Only log presence and system messages
        if (message.type === 'presence' || message.type === 'system') {
          console.log('WebSocket message received:', message);
        }
        
        // Still process all messages for the application
        this.messageHandlers.forEach(handler => handler(message));
      } catch (error) {
        // Only log parsing errors if they're not related to invalid message format
        if (!event.data.includes('"type":"error"') && !event.data.includes('"content":"Invalid message format"')) {
          console.error('Error parsing WebSocket message:', error);
        }
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket disconnected:', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        readyState: this.socket?.readyState,
        chatMode: this.chatMode,
        recipientId: this.directRecipientId
      });
      
      this.isConnected = false;
      this.cleanupHeartbeat();
      this.cleanup();
      this.notifyConnectionStatus();
      
      // Only attempt reconnect if this was an unexpected disconnect (not initiated by us)
      if (!this.isExplicitDisconnect) {
        this.attemptReconnect();
      } else {
        console.log('Clean disconnect, not attempting to reconnect');
        this.isExplicitDisconnect = false;
      }
    };

    this.socket.onerror = (event) => {
      console.error('WebSocket error event:', event);
      console.error('WebSocket readyState:', this.socket?.readyState);
      // console.error('WebSocket URL:', this.socket?.url?.replace(/token=[^&]*/, 'token=***REDACTED***'));
      
      this.isConnected = false;
      this.cleanup();
      this.notifyConnectionStatus();
      this.attemptReconnect();
      reject(new Error(`WebSocket connection failed with readyState ${this.socket?.readyState}`));
    };
  }

  private setupHeartbeat() {
    this.cleanupHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.socket?.readyState === WebSocket.OPEN) {
        try {
          this.socket.send(JSON.stringify({ type: 'ping' }));
        } catch (error) {
          console.error('Error sending heartbeat:', error);
        }
      }
    }, 30000);
  }

  private cleanupHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.socket) {
      console.log('Initiating WebSocket disconnect');
      this.isExplicitDisconnect = true;
      this.cleanupHeartbeat();
      const socket = this.socket;
      this.socket = null;
      
      try {
        // Use a promise to ensure we wait for the close event
        await new Promise<void>((resolve) => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.onclose = () => resolve();
            socket.close(1000, 'User initiated disconnect');
          } else {
            resolve();
          }
        });
      } catch (error) {
        console.error('Error during WebSocket disconnect:', error);
      } finally {
        this.cleanup();
        this.isConnected = false;
        this.notifyConnectionStatus();
      }
    }
  }

  private cleanup() {
    this.cleanupHeartbeat();
    
    if (this.socket) {
      this.socket.onopen = null;
      this.socket.onmessage = null;
      this.socket.onclose = null;
      this.socket.onerror = null;
      
      if (this.socket.readyState === WebSocket.OPEN) {
        this.socket.close();
      }
      
      this.socket = null;
    }
  }

  private async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    if (!this.groupId && !this.directRecipientId) {
      console.log('No chat identifier available for reconnection');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(async () => {
      try {
        if (this.chatMode === 'group' && this.groupId) {
          await this.connectToGroupChat(this.groupId);
        } else if (this.chatMode === 'direct' && this.directRecipientId && this.recipientType) {
          await this.connectToDirectChat(this.directRecipientId, this.recipientType, this.senderName || undefined);
        }
      } catch (error) {
        console.error('Reconnection attempt failed:', error);
      }
    }, delay);
  }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  public async connectToGroupChat(groupId: string): Promise<void> {
    if (this.socket && this.socket.readyState === WebSocket.OPEN && this.groupId === groupId) {
      console.log('WebSocket already connected to this group');
      return this.connectionPromise || Promise.resolve();
    }

    await this.disconnect();
    this.groupId = groupId;
    this.chatMode = 'group';
    
    return new Promise((resolve, reject) => {
      this.connectionPromise = new Promise((res) => {
        this.connectionResolve = res;
      });

      try {
        const token = getToken();
        if (!token) {
          const error = new Error('No authentication token available. Please log in again.');
          console.error(error.message);
          reject(error);
          return;
        }

        let wsUrl: string;
        
        if (this.baseUrl.includes('{group_id}')) {
          wsUrl = this.baseUrl.replace('{group_id}', groupId) + `?token=${encodeURIComponent(token)}`;
        } else {
          const separator = this.baseUrl.endsWith('/') ? '' : '/';
          wsUrl = `${this.baseUrl}${separator}${groupId}?token=${encodeURIComponent(token)}`;
        }
      
        try {
          this.socket = new WebSocket(wsUrl);
          this.setupEventHandlers(resolve, reject);
        } catch (error: any) {
          console.error('Failed to create WebSocket:', error);
          reject(new Error(`Failed to create WebSocket: ${error.message}`));
        }
      } catch (error) {
        console.error('WebSocket connection error:', error);
        reject(error);
      }
    });
  }

  public async connectToDirectChat(recipientId: string, recipientType: string, senderName?: string): Promise<void> {
    if (this.socket && this.socket.readyState === WebSocket.OPEN && this.directRecipientId === recipientId) {
      console.log('WebSocket already connected to this direct chat');
      return this.connectionPromise || Promise.resolve();
    }

    // Fully reset existing socket and state
    await this.disconnect();
    this.directRecipientId = recipientId;
    this.recipientType = recipientType;
    this.senderName = senderName || null;
    this.chatMode = 'direct';

    return new Promise((resolve, reject) => {
      this.connectionPromise = new Promise((res) => {
        this.connectionResolve = res;
      });

      try {
        const token = getToken();
        if (!token) {
          const error = new Error('No authentication token available. Please log in again.');
          console.error(error.message);
          reject(error);
          return;
        }
        let wsUrl: string;

        // Use the direct WebSocket base URL from environment
        const directBaseUrl = process.env.NEXT_DIRECT_WS_BASE || 'wss://localhost:8000/ws/direct/{recipient_id}';
        console.log('Direct WebSocket base URL:', directBaseUrl);

        // Replace {recipient_id} placeholder with actual recipient ID
        wsUrl = directBaseUrl.replace('{recipient_id}', recipientId);

        // Add query parameters
        const params = new URLSearchParams();
        params.append('token', token);
        if (senderName) {
          params.append('sender_name', senderName);
        }

        wsUrl += `?${params.toString()}`;

        console.log('Connecting to WebSocket URL:', wsUrl.replace(token, '***REDACTED***'));
        try {
          this.socket = new WebSocket(wsUrl);
          this.setupEventHandlers(resolve, reject);
        } catch (error: any) {
          console.error('Failed to create WebSocket:', error);
          reject(new Error(`Failed to create WebSocket: ${error.message}`));
        }
      } catch (error) {
        console.error('WebSocket connection error:', error);
        reject(error);
      }
    });
  }

  public sendMessage(message: any): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.error('Cannot send message - WebSocket is not connected');
    }
  }

  public addMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.add(handler);
  }

  public removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler);
  }

  public addConnectionHandler(handler: ConnectionHandler): void {
    this.connectionHandlers.add(handler);
  }

  public removeConnectionHandler(handler: ConnectionHandler): void {
    this.connectionHandlers.delete(handler);
  }

  private notifyConnectionStatus(): void {
    const status = this.getConnectionState();
    this.connectionHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error('Error in connection handler:', error);
      }
    });
  }

  public getConnectionState(): string {
    if (!this.socket) return 'disconnected';
    
    switch (this.socket.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'disconnected';
    }
  }
}