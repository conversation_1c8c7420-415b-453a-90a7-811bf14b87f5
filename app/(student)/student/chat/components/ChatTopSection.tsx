'use client';

import React, { useState, useCallback } from 'react';
import { GroupMember } from '../_logics/chat_types';
import { useChatContext } from '../_logics/chat_context';

interface ChatTopSectionProps {
  isDark: boolean;
  activeRoom: {
    id: string;
    name: string;
    avatar: string;
    isGroup: boolean;
    memberCount: number;
  };
  connectionStatus: string;
  onlineCount: number;
  isSearchActive: boolean;
  searchQuery: string;
  showInfoDrawer: boolean;
  setShowInfoDrawer: (show: boolean) => void;
  groupMembers: GroupMember[];
  loadingMembers: boolean;
  isCurrentUserAdmin: boolean;
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onToggleSearch: () => void;
  onToggleInfoDrawer: () => void;
  onLeaveGroup: (roomId: string) => Promise<boolean>;
  onInvite: () => void;
  onBulkInvite: () => void;
  onFetchGroupMembers: (roomId: string) => Promise<void>;
}

export const ChatTopSection: React.FC<ChatTopSectionProps> = ({
  isDark,
  activeRoom,
  connectionStatus,
  onlineCount,
  isSearchActive,
  searchQuery,
  showInfoDrawer,
  setShowInfoDrawer,
  groupMembers,
  loadingMembers,
  isCurrentUserAdmin,
  onSearch,
  onToggleSearch,
  onToggleInfoDrawer,
  onLeaveGroup,
  onInvite,
  onBulkInvite,
  onFetchGroupMembers
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const { startDirectChat, currentUser } = useChatContext();

  // Handle menu actions
  const handleMenuAction = useCallback((action: string) => {
    setDropdownOpen(false);
    switch(action) {
      case 'info':
        onToggleInfoDrawer();
        if (activeRoom.id) {
          onFetchGroupMembers(activeRoom.id);
        }
        break;
      case 'clear':
        console.log(`Clearing chat history for: ${activeRoom?.name}`);
        break;
      case 'close':
        console.log(`Closing the current chat: ${activeRoom?.name}`);
        break;
      case 'exit':
        console.log(`Exiting the ${activeRoom?.isGroup ? 'group' : 'page'}: ${activeRoom?.name}`);
        break;
    }
  }, [activeRoom, onToggleInfoDrawer, onFetchGroupMembers]);

  // Toggle dropdown menu
  const toggleDropdown = useCallback(() => setDropdownOpen(prev => !prev), []);
  
  // Handle menu selection
  const handleMenuSelect = useCallback((action: string) => {
    setDropdownOpen(false);
    handleMenuAction(action);
  }, [handleMenuAction]);

  // Get connection status styling
  const getConnectionStatusStyles = () => {
    const baseStyles = 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium';
    
    switch(connectionStatus) {
      case 'connected':
        return `${baseStyles} ${isDark ? 'bg-green-500 bg-opacity-20 text-green-300' : 'bg-green-100 text-green-800'}`;
      case 'connecting':
      case 'reconnecting':
        return `${baseStyles} ${isDark ? 'bg-yellow-500 bg-opacity-20 text-yellow-300' : 'bg-yellow-100 text-yellow-800'}`;
      default:
        return `${baseStyles} ${isDark ? 'bg-red-500 bg-opacity-20 text-red-300' : 'bg-red-100 text-red-800'}`;
    }
  };

  // Get connection status text
  const getConnectionStatusText = () => {
    const groupName = activeRoom?.name || 'chat group';
    switch(connectionStatus) {
      case 'connected': return `Connected to chat group: ${groupName}`;
      case 'connecting': return 'Connecting...';
      case 'reconnecting': return 'Reconnecting...';
      default: return 'Disconnected, check your network';
    }
  };

  // Get connection status dot color
  const getConnectionDotColor = () => {
    switch(connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting':
      case 'reconnecting': return 'bg-yellow-500';
      default: return 'bg-red-500';
    }
  };
  
  // Format member count
  const formatMemberCount = (count: number) => {
    if (count < 1000) return count.toString();
    return `${(count / 1000).toFixed(1)}k`;
  };

  const memberCount = activeRoom?.memberCount || 0;

  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showBulkInviteModal, setShowBulkInviteModal] = useState(false);

  return (
    <>
       {/* Header */}
       <div className={`${isDark ? 'bg-[#2a2f32] border-[#384058]' : 'bg-[#f0f2f5] border-gray-200'} border-b p-3 py-2 flex items-center justify-between sticky top-0 z-20`}>
        <div className="flex items-center">
          <img 
            src={activeRoom.avatar} 
            alt={activeRoom.name} 
            className="w-10 h-10 rounded-full object-cover shadow-md border border-gray-200"
            onError={(e) => {
              e.currentTarget.src = '/logo.png';
            }}
          />
          <div className="ml-3">
            <div className="flex items-center">
              <h2 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {activeRoom.name}
              </h2>
              <span 
                className={`${getConnectionStatusStyles()} ml-2`}
                title={`Connection status: ${connectionStatus}`}
              >
                <span className={`w-2 h-2 rounded-full mr-1 ${getConnectionDotColor()}`}></span>
                {getConnectionStatusText()}
              </span>
            </div>
            <div className={`flex items-center text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              <span className="inline-block w-2 h-2 bg-[#006060] rounded-full mr-1"></span>
              Online
              <span className="ml-2">• {onlineCount} of {memberCount} online</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="p-1.5 rounded-full text-teal-500 hover:bg-teal-500/10 transition-colors"
            onClick={() => setShowInviteModal(true)}
            aria-label="Invite to group"
            title="Invite to group"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
          </button>
          {/* Bulk invite button */}
          <button
            className="p-1.5 rounded-full text-teal-500 hover:bg-teal-500/10 transition-colors"
            onClick={() => setShowBulkInviteModal(true)}
            aria-label="Bulk invite users"
            title="Bulk invite users"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </button>


          <div className="relative dropdown-toggle">
            <button
              className={`${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'} p-1.5 rounded-full ${dropdownOpen ? 'bg-[#006060]/10' : ''}`}
              onClick={toggleDropdown}
              aria-label="Menu"
              title="Menu"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
            {dropdownOpen && (
              <div className={`absolute right-0 mt-2 w-52 ${isDark ? 'bg-[#2d3445] text-gray-200' : 'bg-white text-gray-700'} rounded-md shadow-lg z-10 border ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
                <div className="py-1">
                  <button 
                    onClick={() => handleMenuSelect('info')} 
                    className={`block w-full text-left px-4 py-2 text-sm ${isDark ? 'hover:bg-[#1e232e]' : 'hover:bg-gray-100'}`}
                  >
                    {activeRoom.isGroup ? 'Group information' : 'Page information'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

                                                  
                                          
      {/* Group Info Drawer - Inline with chat */}
      {showInfoDrawer && (
        <div className="fixed inset-y-0 right-0 w-56 top-12 bg-white border-l border-gray-200 shadow-lg flex flex-col h-[calc(100%-3rem)] z-50">
          <div className="p-2 border-b border-gray-200 flex justify-between items-center bg-gray-50">
            <h2 className="text-sm font-semibold text-gray-800">Group Info</h2>
            <button 
              onClick={() => setShowInfoDrawer(false)}
              className="p-1 rounded-full hover:bg-gray-200 transition-colors"
              aria-label="Close group info"
            >
              <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {loadingMembers ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#006060]"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Group Header */}
                <div className="text-center">
                  <div className="mx-auto h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center mb-3 overflow-hidden">
                    {activeRoom?.avatar ? (
                      <img 
                        src={activeRoom.avatar} 
                        alt={activeRoom.name} 
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                    ) : (
                      <span className="text-2xl font-bold text-gray-600">
                        {activeRoom?.name?.charAt(0)?.toUpperCase() || 'G'}
                      </span>
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{activeRoom?.name || 'Group Chat'}</h3>
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-xs text-gray-500">{formatMemberCount(groupMembers.length || activeRoom?.memberCount || 0)} members</span>
                    <span className="px-2 py-0.5 text-xs font-medium bg-[#006060]/10 text-[#006060] rounded-full">
                      {activeRoom?.isGroup ? 'Group' : 'Direct'}
                    </span>
                  </div>
                </div>

                {/* Members Section */}
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Members</h4>
                  <div className="space-y-1.5">
                    {groupMembers.length > 0 ? (
                      groupMembers.map(member => (
                        <div
                          key={member.id}
                          className="flex items-center py-2 px-3 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-transparent transition-colors"
                          onClick={async () => {
                            // Prevent starting chat with self
                            if (member.id === currentUser?.id) return;
                            await startDirectChat({
                              id: member.id,
                              name: member.name,
                              avatar: member.avatar,
                              user_type: member.userType
                            });
                            setShowInfoDrawer(false);
                          }}
                        >
                          <div className="relative mr-3">
                            <img 
                              src={member.avatar || '/logo.png'} 
                              alt={member.name} 
                              className="w-8 h-8 rounded-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = '/logo.png';
                              }}
                            />
                            <div 
                              className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 ${isDark ? 'border-[#252B42]' : 'border-white'} ${
                                member.isOnline ? 'bg-green-500' : 'bg-gray-400'
                              }`}
                              title={member.isOnline ? 'Online' : 'Offline'}
                            />
                            {member.directChat && (
                              <div 
                                className={`absolute -bottom-1.5 -right-3.5 w-2 h-2 rounded-full border-2 ${isDark ? 'border-[#252B42]' : 'border-white'} bg-red-500`}
                                title="Direct chat"
                              />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-1">
                              <p className={`text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>
                                {member.name}
                              </p>
                              <span className="ml-1.5 text-gray-400 group-hover:text-blue-500 transition-colors" title="Send direct message">
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                              </span>
                            </div>
                            <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                              {member.userType === 'teacher' ? 'Teacher' : 'Student'}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <p className="mt-2 text-sm text-gray-500">No members found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <button 
              onClick={() => setShowLeaveConfirm(true)}
              className="w-full py-2 px-4 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors flex items-center justify-center space-x-2"
              disabled={isCurrentUserAdmin}
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span>Leave Group</span>
            </button>
            {showLeaveConfirm && (
              <div className="fixed inset-0 flex items-center justify-center z-50">
                <div className="absolute inset-0 bg-black opacity-75"></div>
                <div className={`relative w-full max-w-md p-6 rounded-lg shadow-lg ${isDark ? 'bg-[#1e232e]' : 'bg-white'}`}>
                  <h3 className="text-lg font-medium mb-4">Leave Group</h3>
                  <p className="mb-6">Are you sure you want to leave this group? You won't be able to rejoin unless invited back by an admin.</p>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setShowLeaveConfirm(false)}
                      className={`px-4 py-2 rounded-md ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={async () => {
                        if (activeRoom?.id) {
                          const success = await onLeaveGroup(activeRoom.id);
                          if (success) {
                            setShowInfoDrawer(false);
                            setShowLeaveConfirm(false);
                          }
                        }
                      }}
                      className="px-4 py-2 rounded-md bg-red-500 hover:bg-red-600 text-white"
                    >
                      Leave Group
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};
