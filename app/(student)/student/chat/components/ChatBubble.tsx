import React from 'react';
import { Check, CheckCheck } from 'lucide-react';

interface ChatBubbleProps {
  isCurrentUser: boolean;
  isDark: boolean;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
  isSingleMessage: boolean;
  showHeader: boolean;
  senderName?: string;
  content: string;
  timestamp?: string | number | Date;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  children?: React.ReactNode;
}

export const ChatBubble: React.FC<ChatBubbleProps> = ({
  isCurrentUser,
  isDark,
  isFirstInGroup,
  isLastInGroup,
  isSingleMessage,
  showHeader,
  senderName,
  content,
  timestamp,
  status,
  children
}) => {
  // WhatsApp-style bubble radius logic
  const getBubbleStyle = () => {
    const baseRadius = '10px';
    const smallRadius = '4px';
    
    let borderRadius;
    if (isSingleMessage) {
      borderRadius = isCurrentUser 
        ? `${baseRadius} ${smallRadius} ${baseRadius} ${baseRadius}`
        : `${smallRadius} ${baseRadius} ${baseRadius} ${baseRadius}`;
    } else if (isFirstInGroup) {
      borderRadius = isCurrentUser
        ? `${baseRadius} ${smallRadius} ${baseRadius} ${baseRadius}`
        : `${smallRadius} ${baseRadius} ${baseRadius} ${baseRadius}`;
    } else if (isLastInGroup) {
      borderRadius = isCurrentUser
        ? `${baseRadius} ${baseRadius} ${smallRadius} ${baseRadius}`
        : `${baseRadius} ${baseRadius} ${baseRadius} ${smallRadius}`;
    } else {
      borderRadius = isCurrentUser
        ? `${baseRadius} ${baseRadius} ${smallRadius} ${baseRadius}`
        : `${baseRadius} ${baseRadius} ${baseRadius} ${smallRadius}`;
    }

    return {
      borderRadius,
      backgroundColor: isCurrentUser 
        ? '#dcf8c6' 
        : isDark ? '#374151' : '#ffffff',
      color: isCurrentUser 
        ? '#000000' 
        : isDark ? '#ffffff' : '#000000',
      boxShadow: isDark 
        ? '0 1px 0.5px rgba(0,0,0,0.13)' 
        : '0 1px 0.5px rgba(0,0,0,0.13)',
      position: 'relative' as const,
      maxWidth: '65%',
      minWidth: '48px',
      padding: '6px 7px 18px 9px',
      marginTop: isFirstInGroup ? '8px' : '2px',
      marginRight: '0',
      marginBottom: isFirstInGroup ? '2px' : '0',
      marginLeft: '10px',
      wordWrap: 'break-word' as const,
      wordBreak: 'break-word' as const,
    };
  };

  return (
    <div style={getBubbleStyle()}>
      {/* Sender name inside bubble (only for others and at start of group) */}
      {!isCurrentUser && showHeader && (
        <div 
          style={{
            fontSize: '12px',
            fontWeight: '600',
            color: isCurrentUser 
              ? (isDark ? '#d1d5db' : '#374151') 
              : (isDark ? '#9ca3af' : '#4b5563'),
            marginBottom: '4px',
            marginTop: '-2px',
            marginLeft: '2px',
            lineHeight: '1.2'
          }}
        >
          {senderName?.split(' ')[0]}
        </div>
      )}
      
      {/* Message content */}
      <div style={{ 
        marginBottom: '2px',
        paddingRight: '50px',
        position: 'relative',
        wordBreak: 'break-word'
      }}>
        <span style={{ 
          fontSize: '14px',
          lineHeight: '19px',
          whiteSpace: 'pre-wrap',
          display: 'inline-block',
          width: '100%',
          paddingRight: '4px',
        }}>
          {content}
        </span>
      </div>
      
      {/* Message info (time and status) */}
      <div 
        style={{
          position: 'absolute',
          bottom: '4px',
          right: isCurrentUser ? '28px' : '12px',
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px',
          fontSize: '11px',
          color: isDark ? 'rgba(255,255,255,0.55)' : 'rgba(0,0,0,0.45)',
          marginTop: '2px',
          height: '15px',
          whiteSpace: 'nowrap', 
          float: 'right',
          clear: 'both',
          marginLeft: '6px',
          marginBottom: '-4px',
          verticalAlign: 'bottom'
        }}
      >
        {timestamp 
          ? new Date(timestamp).toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: false 
            })
          : 'now'}
        {isCurrentUser && (
          <span style={{ display: 'inline-flex', alignItems: 'center', marginLeft: '4px' }}>
            {status === 'sent' && <Check size={14} />}
            {status === 'delivered' && <CheckCheck size={14} />}
            {status === 'read' && <CheckCheck size={14} className="text-blue-500" />}
          </span>
        )}
      </div>
      
      {children}
    </div>
  );
};
