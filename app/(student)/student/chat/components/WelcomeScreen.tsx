import React from 'react';

interface WelcomeScreenProps {
  isDark: boolean;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ isDark }) => {
  return (
    <div className={`flex-1 flex flex-col items-center justify-center ${isDark ? 'bg-[#0b141a] text-white' : 'bg-[#efeae2] text-gray-800'}`}>
      <div className="text-center max-w-md mx-auto px-4">
        <div className="mb-6">
          <img 
            src="/logo.png" 
            alt="LearnKonnect" 
            className="w-20 h-20 mx-auto mb-4 opacity-50" 
          />
        </div>
        <h2 className={`text-2xl font-semibold mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>
          Welcome to LearnKonnect Chat
        </h2>
        <p className={`text-lg mb-6 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
          Select a chat group from the sidebar to start messaging
        </p>
        <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
          <p>• Join group conversations with your classmates</p>
          <p>• Share ideas and collaborate on projects</p>
          <p>• Stay connected with your learning community</p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
