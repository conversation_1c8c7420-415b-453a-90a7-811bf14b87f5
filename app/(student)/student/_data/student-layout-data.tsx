import {
  BellOutlined,
  BookOutlined,
  FileTextOutlined,
  HeartOutlined,
  HomeOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  ProfileOutlined,
  SearchOutlined,
  SettingOutlined,
  ShoppingCartOutlined,
  VerifiedOutlined,
  <PERSON><PERSON>tOutlined,
  ReadOutlined,
  FileMarkdownOutlined
} from '@ant-design/icons';
import { Badge } from 'antd';
import { useCartStore } from '@/store/cartStore';



export const getStudentMenuItems = ()=>

  {  const { cart } = useCartStore();


   return  [
    { icon: <HomeOutlined />, text: 'Home', path: '/student/notice-board' },
    { icon: <FileTextOutlined />, text: 'My Learning', path: '/student/my-learning' },
    { icon: <BookOutlined />, text: 'Courses', path: '/student/courses' },
    { icon: <VerifiedOutlined />, text: 'Assessment', path: '/student/assessment' },
    { icon: <FileTextOutlined />, text: 'Question Bank', path: '/student/questionbank' },
    { icon: <FileMarkdownOutlined />, text: 'Course Notes', path: '/student/note' },
    { icon: <HeartOutlined />, text: 'Wishlist', path: '/student/wishlist' },
    {
      icon: (
        <Badge count={cart?.item_count} size="small">
          <ShoppingCartOutlined className='text-white
          ' />
        </Badge>
      ),
      text: 'My Cart',
      path: '/student/cart'
    },
    { icon: <MessageOutlined />, text: 'Chat', path: '/student/chat' },
  ]
   };

export const studentBottomsItems = [
    { icon: <SettingOutlined />, text: 'Settings', path: '/student/profile' },
        {icon: <ProfileOutlined />, text: "Mentor", path: "/student/mentor",  },

    { icon: <LogoutOutlined />, text: 'Logout', path: '/logout' },
  ];