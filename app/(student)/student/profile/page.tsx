'use client'
import React, { useEffect, useState } from 'react';
import { App, ConfigProvider, Form, FormProps, Layout, Modal, Tabs, TabsProps, theme } from 'antd';
import PlatformPage from './platform/platform';
import ProfileSettingsPage from './profile-settings/profile-settings';
import AccountSettingsPage from './account-settings/account-settings';
import FAQsPage from './faqs/faqs';
import RewardsPage from './rewards/rewards';
import { div } from 'framer-motion/client';
import InputTemplate from '@/components/ui/input-template';
import { ApartmentOutlined, FundProjectionScreenOutlined, MailOutlined } from '@ant-design/icons';
import SelectTemplate from '@/components/ui/select-template';
import UploadTemplate from '@/components/ui/upload-template';
import { useUserStore } from '@/store/userStore';
import { useStudentStore } from '@/store/studentStore';
import ButtonTemplate from '@/components/ui/button-template';
import { EducationLevelList } from '@/utils/levels';
import { useFetch } from '@/hooks/useFetch';
import { SchoolProgram } from '@/types';
import { ProfileLogics } from '@/logics/profile';
import Purchases from './purchases/purchases';
import { useStudentProfile } from '@/logics/useStudentProfile';

const { Content } = Layout;

export default function Page() {
  const [openModal, setOpenModal] = useState(false);
  const [schoolPrograms, setSchoolPrograms] = useState<SchoolProgram[]>([])
  const { student, setStudent } = useStudentStore();
  const { user } = useUserStore();
  const { data } = useFetch(`/program/school/${user && user?.school_id}`)
  const { studentForm, updateStudentProfile } = ProfileLogics()


  useEffect(() => {
    console.log('school data', data)
    setSchoolPrograms(data?.data?.programs)
  }, [data])

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  const { modal } = App.useApp();





  const onChange = (key: string) => {
    console.log(key);
  };
  const handleCancel = (key: any) => {
    console.log(key);
    setOpenModal(false)
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Platform Profile',
      children:  <PlatformPage />,
    },
    {
      key: '2',
      label: 'Profile Settings',
      children: <ProfileSettingsPage />,
    },
    {
      key: '3',
      label: 'Account Settings',
      children: <AccountSettingsPage />,
    },
    {
      key: '4',
      label: 'Rewards',
      children: <RewardsPage />,
    },
    {
      key: '5',
      label: 'FAQs',
      children: <FAQsPage />,
    },
    // {
    //   key: '6',
    //   label: 'Purchases',
    //   children: <Purchases />,
    // },
  ];


  useEffect(() => {
    const timer = setTimeout(() => {
      console.log('studed', student);
      if (student) {
        setOpenModal(false);
      } else {
        setOpenModal(true);
      }
    }, 2000);
  
    // Cleanup: Cancel the previous timeout if student changes
    return () => clearTimeout(timer);
  }, [student]);

  const onFinish: FormProps<any>['onFinish'] = async (info) => {
    const studentProfileInfo = {
      name:`${user?.first_name} ${user?.last_name}`,
      email: user?.email,
      user_id: user?.id,
      school_id: user?.school_id,
      program_id: info?.program_id,
      level: info?.level,
      phone: user?.phone,
      profile_image: info?.profile_image[0],
      cover_image: info?.cover_image[0],
    }
    // Update Student
    console.log('info', studentProfileInfo)
  const res:any = await updateStudentProfile(studentProfileInfo);
  if(res!= null ){
    setStudent(res);
    setOpenModal(false);
  }
  };


  return (
    <>
      <Modal

        title="User Form"
        open={openModal}
        onCancel={handleCancel}
        footer={null} // Hide default footer buttons
      >
        <Form name="profileForm"
          onFinish={onFinish}
          form={studentForm}
        >
          <UploadTemplate listType='picture-circle' label={'Profile Image'} fieldName={'profile_image'} />
          <UploadTemplate listType='picture-card' label={'Cover Image'} fieldName={'cover_image'} />
          <SelectTemplate
            fieldName="program_id"

            fieldNames={{ label: 'name', value: 'id' }}
            options={schoolPrograms}
            label={"Program"} className="" placeHolder={'Electrical Engineering'} prefix={<FundProjectionScreenOutlined />} />
          <div className='my-8'></div>
          <SelectTemplate
            fieldName="level"
            fieldNames={{ label: 'name', value: 'id' }}
            options={EducationLevelList}
            label={"Level"} className="" placeHolder={'Undergraduate'} prefix={<ApartmentOutlined />} />
          <div className='flex justify-end w-full mt-8'>
            <ButtonTemplate 
             htmlType="submit"
                  
            label={'Save'} />
          </div>

        </Form>
      </Modal>

      <Content
        style={{
          margin: '0px 0px',
          padding: '0px 24px',
          background: colorBgContainer,
          borderRadius: borderRadiusLG,
        }}
      >
        <ConfigProvider

          theme={{
            components: {
              Tabs: {
                /* here is your component tokens */
                inkBarColor: "#000000",
                itemActiveColor: "#000000",
                itemColor: "#6e6d6b",
                itemHoverColor: "#000000",
                itemSelectedColor: "#000000"
              },
            },
          }}
        >

          <Tabs defaultActiveKey="1" items={items} onChange={onChange} />

        </ConfigProvider>
      </Content>
    </>
  );
};
