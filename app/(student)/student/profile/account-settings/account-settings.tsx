"use client";
import InputTemplate from "@/components/ui/input-template";
import {
  FacebookFilled,
  LinkedinFilled,
  UserOutlined,
} from "@ant-design/icons";
import React, { useState } from "react";
import ButtonTemplate from "@/components/ui/button-template";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { Modal } from "antd";
import { useStudentStore } from "@/store/studentStore";
import { ProfileLogics } from "@/logics/profile";
import { useUserStore } from "@/store/userStore";

export default function AccountSettingsPage() {
  const { deleteStudentProfile, deactivateStudentProfile } = ProfileLogics();
  const { student, setStudent, clearStudent } = useStudentStore();
  const { user } = useUserStore();
  const router = useRouter();

  const [socialMediaLinks, setSocialMediaLinks] = useState([
    {
      icon: <FacebookFilled />,
      name: "Facebook",
      link: "www.facebook/johnjennefir.com",
    },
    {
      icon: <LinkedinFilled />,
      name: "LinkedIn",
      link: "www.linkedIn/johnjennefir.com",
    },
  ]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };
  const showDeactivateModal = () => {
    setIsDeactivateModalOpen(true);
  };

  const handleOk = async () => {
    setIsModalOpen(false);
    const studentRes = await deleteStudentProfile(
      student!.id.toString(),
      user!.id.toString()
    );
    console.log("this is student res", studentRes);
    if (studentRes) {
      console.log("Deleted student profile");
      setStudent(studentRes);
      setTimeout(() => {
        Cookies.remove("access_token"); // Remove the access token cookie
        window.location.href = "/auth/login"; // Redirect to login page
      }, 2000); // Delay of 2000ms (2 seconds)
    }
  };
  const handleDeactivateOk = async () => {
    setIsModalOpen(false);
    const studentRes = await deactivateStudentProfile(student!.id.toString());
    if (studentRes) {
      console.log("deactivate student profile", studentRes);
      setTimeout(() => {
        Cookies.remove("access_token"); // Remove the access token cookie
        window.location.href = "/auth/login"; // Redirect to login page
      }, 2000); // Delay of 2000ms (2 seconds)
      // setStudent(studentRes)
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const handleDeactivateCancel = () => {
    setIsDeactivateModalOpen(false);
  };

  return (
    <>
      <Modal
        title="Delete Account"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="Confirm"
        cancelButtonProps={{ color: "red" }}
      >
        <p className="font-bold">
          Are you sure you want to delete your account?
        </p>
        <p className="text-xs text-red-500">
          Deleted accounts cannot be recovered!
        </p>
      </Modal>
      <Modal
        title="Deactivate Account"
        open={isDeactivateModalOpen}
        onOk={handleDeactivateOk}
        onCancel={handleDeactivateCancel}
        okText="Confirm"
        cancelButtonProps={{ color: "red" }}
      >
        <p className="font-bold">
          Are you sure you want to deactivate your account?
        </p>
      </Modal>
      <div className="overflow-y-scroll h-[80dvh] ">
        <div className="rounded-md border">
          <div className="relative">
            {/* Cover Image with better preview handling */}
            <div className="relative w-full h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden">
              {student?.cover_image_path ? (
                <img
                  src={String(student.cover_image_path)}
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : student?.cover_image ? (
                <img
                  src={
                    student.cover_image.startsWith("data:")
                      ? student.cover_image
                      : `data:image/jpeg;base64,${student.cover_image}`
                  }
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : student?.cover_image_path_server ? (
                <img
                  src={String(student.cover_image_path_server)}
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : (
                /* Default gradient background when no image is available */
                <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-800 rounded-md"></div>
              )}
            </div>
            {/* Profile Image with better preview handling */}
            <div className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-gray-100">
              {student?.profile_image_path ? (
                <img
                  src={String(student.profile_image_path)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : student?.profile_image ? (
                <img
                  src={
                    student.profile_image.startsWith("data:")
                      ? student.profile_image
                      : `data:image/jpeg;base64,${student.profile_image}`
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : student?.profile_image_path_server ? (
                <img
                  src={String(student.profile_image_path_server)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                /* Default user icon when no image is available */
                <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                  <UserOutlined style={{ fontSize: "24px" }} />
                </div>
              )}
            </div>
          </div>
          <div className="flex mt-8 justify-between">
            <div className="p-6 text-start">
              <h2 className="font-semibold text-sm">{`${user?.first_name} ${user?.last_name}`}</h2>
              <p className="text-gray-500 text-xs">{`${user?.email}`}</p>
            </div>

            <div className="flex justify-center space-x-4 mb-4">
              <div className="p-6 text-center">
                <h2 className="font-medium text-xs">Social Media Links</h2>
                <div className="flex justify-end text-xl gap-1 mt-1">
                  <FacebookFilled />
                  <LinkedinFilled />
                </div>
              </div>
            </div>
          </div>

          <div className="px-6 pb-6">
            <h3 className="text-sm font-semibold mb-2">Account Security</h3>
            <div className="flex gap-4 my-4 mb-6 w-full">
              <InputTemplate
                fieldName="email"
                className="w-[20rem]"
                label={"Email Address"}
              />
            </div>
            <ButtonTemplate
              handleClick={() => {
                Cookies.remove("access_token");
                window.location.href = "/auth/change-password";
              }}
              label="Change Password"
            />
          </div>
        </div>

        {/* DEACTIVATE ACCOUNT */}
        <div className="px-6 pb-6 border rounded-md mt-4">
          <h3 className="text-sm font-semibold mb-2 mt-6">
            Want to deactivate your account?
          </h3>

          <div className="  mt-4 text-stone-500 text-xs">
            Once your profile is deactivated you can easily come back and resume
            your course from the point you left.
          </div>

          <div className="flex  mt-4 gap-2">
            <ButtonTemplate
              handleClick={showDeactivateModal}
              label="Deactivate"
              className="border-red-400"
            />
          </div>
        </div>

        {/* DELETE ACCOUNT */}
        <div className="px-6 pb-6 border rounded-md mt-4">
          <h3 className="text-sm font-semibold mb-2 mt-6">
            Want to delete your account?
          </h3>

          <div className="  mt-4 text-stone-500 text-xs">
            Once your account is deleted you are unable to join the current
            enrolled courses and no progress will be shown.
          </div>

          <div className="flex  mt-4 gap-2">
            <ButtonTemplate
              handleClick={showModal}
              label="Delete"
              className="border-red-400"
            />
          </div>
        </div>
      </div>
    </>
  );
}
