"use client";
import React, { useState, useEffect } from "react";
import { Form, Spin, Tooltip, Button } from "antd";
import {
  PlusOutlined,
  MinusOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  FacebookFilled,
  LinkedinFilled,
  UserOutlined,
  <PERSON>UploadOutlined,
  CameraOutlined,
} from "@ant-design/icons";
import { motion, AnimatePresence } from "framer-motion";
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import InputTemplate from "@/components/ui/input-template";
import { useRouter } from "next/navigation";
import { useStudentStore } from "@/store/studentStore";
import { useUserStore } from "@/store/userStore";
import { useStudentProfile } from "@/logics/useStudentProfile"; // updated import statement
import TagTemplate from "@/components/ui/tag-template";
import ButtonTemplate from "@/components/ui/button-template";

// FAQ Item Component
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden">
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-300"
      >
        <div className="flex items-center space-x-3">
          <QuestionCircleOutlined className="text-teal-700 text-xl" />
          <span className="font-medium text-gray-800">{question}</span>
        </div>
        {isOpen ? (
          <MinusOutlined className="text-teal-700" />
        ) : (
          <PlusOutlined className="text-teal-700" />
        )}
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{
              opacity: 1,
              height: "auto",
              transition: {
                duration: 0.3,
                ease: "easeInOut",
              },
            }}
            exit={{
              opacity: 0,
              height: 0,
              transition: {
                duration: 0.2,
                ease: "easeInOut",
              },
            }}
            className="overflow-hidden"
          >
            <div className="p-4 bg-gray-50 text-gray-600 text-sm border-t">
              {answer}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Contact Form Component
const ContactUsForm = () => {
  const [form] = Form.useForm();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [remainingChars, setRemainingChars] = useState(500);
  const [isExceededLimit, setIsExceededLimit] = useState(false);
  const { user } = useUserStore();

  // fill form with user data
  useEffect(() => {
    if (user) {
      // Split name into first and last name
      const nameParts = user.name ? user.name.trim().split(" ") : [];
      form.setFieldsValue({
        firstName: user.first_name || nameParts[0] || "",
        lastName: user.last_name || nameParts.slice(1).join(" ") || "",
        email: user.email || "",
      });
    }
  }, [user, form]);

  // Handle textarea character count
  const handleTextChange = (
    e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
  ) => {
    if (e.target instanceof HTMLTextAreaElement) {
      const currentValue = e.target.value;
      const currentLength = currentValue.length;

      setRemainingChars(500 - currentLength);

      // Check if exceeded limit
      if (currentLength > 500) {
        setIsExceededLimit(true);
        form.setFieldsValue({
          message: currentValue.slice(0, 500),
        });
      } else {
        setIsExceededLimit(false);
      }
    }
  };

  // Submit contact form
  const handleSubmit = async (values: any) => {
    if (isExceededLimit) {
      showNotification(
        "error",
        "Validation Error",
        "Message exceeds 500 character limit",
        false,
        <ExclamationCircleOutlined />
      );
      return;
    }

    try {
      setLoading(true);
      showNotification(
        "info",
        "Sending Message",
        "Submitting your message...",
        true,
        <Spin />
      );

      const fullName = `${values.firstName} ${values.lastName}`.trim();

      const response = await request(
        "POST",
        "/unprotected/contact_us",
        JSON.stringify({
          name: fullName,
          email: user?.email || values.email,
          message: values.message,
        }),
        "application/json"
      );

      destroyNotifications();

      if (response?.status === 200) {
        showNotification(
          "success",
          "Success",
          "Your message has been sent successfully"
        );
        form.setFieldsValue({ message: "" });
        setRemainingChars(500);
        setIsExceededLimit(false);
      } else {
        showNotification(
          "error",
          "Error",
          response?.data?.detail || "Failed to send message"
        );
      }
    } catch (error) {
      showNotification("error", "Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      layout="vertical"
      className="w-full"
    >
      <div className="grid grid-cols-2 gap-4">
        <InputTemplate
          label="First Name"
          fieldName="firstName"
          disabled
          placeHolder="Enter your first name"
          rules={[{ required: true, message: "Please enter your first name" }]}
        />
        <InputTemplate
          label="Last Name"
          fieldName="lastName"
          disabled
          placeHolder="Enter your last name"
          rules={[{ required: true, message: "Please enter your last name" }]}
        />
      </div>

      {!user?.email && (
        <InputTemplate
          label="Email"
          fieldName="email"
          placeHolder="Enter your email address"
          rules={[
            { required: true, message: "Please enter your email" },
            {
              inputType: "email" as const,
              message: "Please enter a valid email address",
            },
          ]}
        />
      )}

      <div className="relative">
        <InputTemplate
          label="Message"
          fieldName="message"
          textarea
          textareaRows={6}
          placeHolder="Type your message here (500 characters max)"
          onChange={handleTextChange}
          rules={[{ required: true, message: "Please enter your message" }]}
        />
        <div
          className={`absolute bottom-2 right-4 text-xs ${
            isExceededLimit ? "text-red-500" : "text-gray-500"
          }`}
        >
          {isExceededLimit
            ? "Maximum character limit exceeded!"
            : `${remainingChars} characters remaining`}
        </div>
      </div>

      <button
        type="submit"
        disabled={loading || isExceededLimit}
        className={`w-full py-2 rounded transition-colors 
          ${
            loading || isExceededLimit
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-teal-600 text-white hover:bg-teal-700"
          }`}
      >
        {loading ? <Spin size="small" /> : "Send Message"}
      </button>
    </Form>
  );
};

// Support Page Component
export default function FaqsPage() {
  const [activeTab, setActiveTab] = useState<string>("faq");
  const [faqs, setFaqs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { student } = useStudentStore();
  const { user: currentUser } = useUserStore();
  const {
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    fetchStudentProfile,
    loading: profileLoading,
  } = useStudentProfile();

  // Load student profile on component mount
  useEffect(() => {
    fetchStudentProfile();
  }, []);

  // Load FAQs
  useEffect(() => {
    // Mock FAQs data - replace with actual API call
    setFaqs([
      {
        question: "How do I reset my password?",
        answer:
          "You can reset your password by clicking on the 'Forgot Password' link on the login page.",
      },
      {
        question: "How do I enroll in a course?",
        answer:
          "Browse available courses and click on the 'Enroll' button on the course details page.",
      },
      {
        question: "Can I download course materials for offline viewing?",
        answer:
          "Yes, most course materials can be downloaded for offline viewing from the course resources section.",
      },
      {
        question: "How do I contact my instructor?",
        answer:
          "You can message your instructor directly from the course page or through the messaging system.",
      },
      {
        question: "How do I track my progress?",
        answer:
          "Your course progress is automatically tracked and displayed on your dashboard and on each course page.",
      },
    ]);
  }, []);

  // Handle profile image upload
  const handleProfileImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadProfileImage(file);
    }
  };

  // Handle cover image upload
  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadCoverImage(file);
    }
  };
  // Use the faqs state instead of redeclaring
  useEffect(() => {
    setFaqs([
      {
        question: "How can I maximize my learning potential on this platform?",
        answer:
          "Our platform offers personalized learning paths, adaptive assessments, and AI-driven recommendations. Engage with interactive content, participate in peer study groups, and utilize our progress tracking tools to optimize your learning journey.",
      },
      {
        question:
          "What makes our learning approach different from traditional education?",
        answer:
          "We blend cutting-edge technology with pedagogical expertise. Our adaptive learning algorithms personalize content difficulty, provide real-time feedback, and create a dynamic learning experience that adapts to your individual learning style and pace.",
      },
      {
        question:
          "How do we support students with different learning backgrounds?",
        answer:
          "We offer comprehensive support through multilingual content, accessibility features, varied learning formats (video, text, interactive), and dedicated mentorship programs. Our goal is to create an inclusive learning environment that empowers every student.",
      },
      {
        question: "Can I collaborate with other learners?",
        answer:
          "Absolutely! Our platform features community forums, peer study groups, collaborative project spaces, and mentorship connections. These tools help you network, share knowledge, and learn collaboratively with students from around the world.",
      },
      {
        question: "How do we ensure the quality of our educational content?",
        answer:
          "Our content is curated by industry experts, academics, and professionals. We continuously update materials, perform rigorous quality checks, and incorporate feedback from learners and subject matter experts to maintain the highest educational standards.",
      },
      {
        question: "What career support do you provide?",
        answer:
          "Beyond learning, we offer career development resources including skill assessments, industry-aligned skills, portfolio building tools, interview preparation workshops, and direct connections with potential employers and recruiters through our Teachers.",
      },
    ]);
  }, []);

  return (
    <div className="overflow-y-scroll w-full overflow-clip h-[80dvh] ">
      <div className="rounded-md border">
        {/* Cover Image and Profile Section */}
        <div className="relative">
          {/* Cover Image with better preview handling */}
          <div className="relative w-full h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden">
            {student?.cover_image_path ? (
              <img
                src={String(student.cover_image_path)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image ? (
              <img
                src={
                  student.cover_image.startsWith("data:")
                    ? student.cover_image
                    : `data:image/jpeg;base64,${student.cover_image}`
                }
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image_path_server ? (
              <img
                src={String(student.cover_image_path_server)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              /* Default gradient background when no image is available */
              <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-800 rounded-md"></div>
            )}
          </div>
          {/* Profile Image with better preview handling */}
          <div className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-gray-100">
            {student?.profile_image_path ? (
              <img
                src={String(student.profile_image_path)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image ? (
              <img
                src={
                  student.profile_image.startsWith("data:")
                    ? student.profile_image
                    : `data:image/jpeg;base64,${student.profile_image}`
                }
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image_path_server ? (
              <img
                src={String(student.profile_image_path_server)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              /* Default user icon when no image is available */
              <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                <UserOutlined style={{ fontSize: "24px" }} />
              </div>
            )}
          </div>
        </div>
        {/* Profile and Social Media Info */}
        <div className="flex mt-8 justify-between px-6">
          <div className="text-start">
            <br />
            <h2 className="font-semibold text-sm">
              {currentUser?.first_name && currentUser?.last_name
                ? `${currentUser.first_name} ${currentUser.last_name}`
                : ""}
            </h2>
            <p className="text-gray-500 text-xs">{currentUser?.email || ""}</p>
          </div>

          <div className="flex justify-center space-x-4">
            <div className="text-center">
              <h2 className="font-medium text-xs">Social Media Links</h2>
              <div className="flex justify-end text-xl gap-1 mt-1">
                <FacebookFilled />
                <LinkedinFilled />
              </div>
            </div>
          </div>
        </div>

        {/* Existing Content */}
        <div className="px-6 pb-6">
          <h3 className="text-sm font-semibold mb-2 mt-4">Account Security</h3>
          <div className="flex">
            <TagTemplate
              handleClick={() => setActiveTab("faq")}
              className={
                activeTab === "faq"
                  ? "text-black bg-teal-700 shadow-none"
                  : "bg-white !text-black border-none"
              }
              value={"FAQ's"}
            />
            <TagTemplate
              handleClick={() => setActiveTab("contact-us")}
              className={
                activeTab === "contact-us"
                  ? "text-black bg-teal-700 shadow-none"
                  : "bg-white !text-black border-none"
              }
              value={"Contact us"}
            />
          </div>

          <div className="mt-4 grid grid-cols-12 gap-6">
            {activeTab === "faq" ? (
              <div className="col-span-12">
                {faqs.map((faq, index) => (
                  <FAQItem
                    key={index}
                    question={faq.question}
                    answer={faq.answer}
                  />
                ))}
                {/* <div className='flex justify-center mt-8'>
                                    <ButtonTemplate label='View All' />
                                </div> */}
              </div>
            ) : (
              <>
                <div className="col-span-5">
                  <ContactUsForm />
                </div>
                <div className="col-span-7 bg-gray-50 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4">
                    Get in Touch
                  </h4>
                  <p className="text-gray-600 mb-4">
                    Have a question or need assistance? We're here to help! Fill
                    out the form and our support team will get back to you as
                    soon as possible.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <svg
                        className="w-6 h-6 text-teal-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      <span className="text-gray-700">+233 (555) 123-4567</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <svg
                        className="w-6 h-6 text-teal-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      <span className="text-gray-700">
                        <EMAIL>
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
