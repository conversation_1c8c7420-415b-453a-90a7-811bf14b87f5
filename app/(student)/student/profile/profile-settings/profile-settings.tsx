"use client";
import InputTemplate from "@/components/ui/input-template";
import {
  ApartmentOutlined,
  CameraOutlined,
  CheckCircleOutlined,
  CloudUploadOutlined,
  FacebookFilled,
  FundProjectionScreenOutlined,
  LinkedinFilled,
  MobileOutlined,
  UserOutlined,
} from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import { Divider, Form, FormProps, Modal } from "antd";
import ButtonTemplate from "@/components/ui/button-template";
import SwitchTemplate from "@/components/ui/switch-template";
import { useUserStore } from "@/store/userStore";
import SelectTemplate from "@/components/ui/select-template";
// import { schoolsList } from '@/app/(auth)/auth/_data/auth-data';
import { useFetch } from "@/hooks/useFetch";
import UploadTemplate from "@/components/ui/upload-template";
import { useStudentStore } from "@/store/studentStore";
import { ProfileLogics } from "@/logics/profile";
import { SchoolProgram, SchoolType } from "@/types";
import { EducationLevelList } from "@/utils/levels";
import Cookies from "js-cookie"; // Import cookies library
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useStudentImageUpload } from "../_logics/image-upload-logics";
import { Tooltip } from "antd";

export default function ProfileSettingsPage() {
  const [socialMediaLinks, setSocialMediaLinks] = useState([
    {
      icon: <FacebookFilled />,
      name: "Facebook",
      link: "www.facebook/johnjennefir.com",
    },
    {
      icon: <LinkedinFilled />,
      name: "LinkedIn",
      link: "www.linkedIn/johnjennefir.com",
    },
  ]);

  const { user, setUser, clearUser } = useUserStore();
  const { data, loading, error } = useFetch(`/student/${user?.id}`);
  console.log("student profile data", data);

  const { data: schoolsData, loading: fetchingSchools } = useFetch(
    "/unprotected/school/all"
  );
  const [openModal, setOpenModal] = useState(false);
  const [schoolPrograms, setSchoolPrograms] = useState<SchoolProgram[]>([]);
  const [schoolList, setSchoolList] = useState<SchoolType[]>([]);
  const { student, setStudent } = useStudentStore();

  const {
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    isUploading,
  } = useStudentImageUpload();

  const {
    new_course_subscription,
    new_trending_course_subscription,
    setNewCourseSubscription,
    setNewTrendingCourseSubscription,
  } = useSubscriptionStore();

  const handleProfileImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadProfileImage(file);
    }
  };

  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadCoverImage(file);
    }
  };
  const [newUserSchool, setNewUserSchool] = useState(user && user?.school_id);
  const {
    data: programSchool,
    loading: programSchoolLoading,
    error: programSchoolError,
  } = useFetch(`/program/school/${newUserSchool && newUserSchool}`);
  const {
    studentUploadForm,
    updateStudentProfileDetails,
    studentLogoutForm,
    subscriptionForm,
    updateNewCourse,
    updateTrendingCourse,
  } = ProfileLogics();

  useEffect(() => {
    console.log("studed", student);
    if (student) {
      setOpenModal(false);
      studentUploadForm.setFieldValue("phone", student.phone);
      studentUploadForm.setFieldValue("school_id", student.school_id);
      studentUploadForm.setFieldValue("program_id", student.program_id);
      studentUploadForm.setFieldValue("level", student.level);
    } else {
      setOpenModal(true);
    }
    // check for autologout
    const autoLogout = Cookies.get("auto-logout");
    studentLogoutForm.setFieldValue("autoLogout", autoLogout === "true");
  }, [student]);

  useEffect(() => {
    setSchoolList(schoolsData?.data?.schools);
  }, [schoolsData]);
  useEffect(() => {
    subscriptionForm.setFieldValue(
      "new_course_subscription",
      new_course_subscription?.is_active
    );
    subscriptionForm.setFieldValue(
      "new_trending_course_subscription",
      new_trending_course_subscription?.is_active
    );
  }, [new_course_subscription, new_trending_course_subscription]);

  const onFinish: FormProps<any>["onFinish"] = async (info) => {
    const studentProfileInfo = {
      name: `${user?.first_name} ${user?.last_name}`,
      email: user?.email,
      user_id: user?.id,
      school_id: info?.school_id,
      program_id: info?.program_id,
      level: info?.level,
      phone: info?.phone,
    };
    // Update Student
    console.log("info", studentProfileInfo);
    const studentRes = await updateStudentProfileDetails(
      studentProfileInfo,
      `${student?.id}`
    );
    if (studentRes) {
      console.log("updated student done");
      setStudent(studentRes);
    }
  };
  const onChange = (key: string) => {
    console.log(key);
  };
  const handleCancel = (key: any) => {
    console.log(key);
    setOpenModal(false);
  };
  useEffect(() => {
    console.log("school data", programSchool);
    setSchoolPrograms(programSchool?.data?.programs);
  }, [programSchool]);
  return (
    <>
      <div className="overflow-y-scroll h-[80dvh] ">
        <div className="rounded-md border">
          <div className="relative">
            {/* Cover Image with better preview handling */}
            <div className="relative w-full h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden">
              {student?.cover_image_path ? (
                <img
                  src={String(student.cover_image_path)}
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : student?.cover_image ? (
                <img
                  src={
                    student.cover_image.startsWith("data:")
                      ? student.cover_image
                      : `data:image/jpeg;base64,${student.cover_image}`
                  }
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : student?.cover_image_path_server ? (
                <img
                  src={String(student.cover_image_path_server)}
                  alt="Cover"
                  className="w-full h-full object-cover rounded-md"
                />
              ) : (
                /* Default gradient background when no image is available */
                <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-800 rounded-md"></div>
              )}
            </div>
            {/* Profile Image with better preview handling */}
            <div className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-gray-100">
              {student?.profile_image_path ? (
                <img
                  src={String(student.profile_image_path)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : student?.profile_image ? (
                <img
                  src={
                    student.profile_image.startsWith("data:")
                      ? student.profile_image
                      : `data:image/jpeg;base64,${student.profile_image}`
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : student?.profile_image_path_server ? (
                <img
                  src={String(student.profile_image_path_server)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                /* Default user icon when no image is available */
                <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                  <UserOutlined style={{ fontSize: "24px" }} />
                </div>
              )}
            </div>
          </div>
          <div className="flex mt-8 justify-between">
            <div className="p-6 text-start">
              <h2 className="font-semibold text-sm">{`${user?.first_name} ${user?.last_name}`}</h2>
              <p className="text-gray-500 text-xs">{`${user?.email}`}</p>
            </div>

            <div className="flex justify-center space-x-4 mb-4">
              <div className="p-6 text-center">
                <h2 className="font-medium text-xs">Social Media Links</h2>
                <div className="flex justify-end text-xl gap-1 mt-1">
                  <FacebookFilled />
                  <LinkedinFilled />
                </div>
              </div>
            </div>
          </div>

          <div className="px-6 pb-6">
            <h3 className="text-sm font-semibold mb-2">Edit Profile</h3>
            <Form
              name="profileForm"
              onFinish={onFinish}
              form={studentUploadForm}
            >
              <div className="flex gap-4 mt-4 w-full">
                <InputTemplate
                  required={false}
                  disabled={true}
                  prefix={<UserOutlined />}
                  fieldName="firstName"
                  className="w-[20rem]"
                  label={"First Name"}
                  placeHolder={user?.first_name}
                />
                <InputTemplate
                  required={false}
                  disabled={true}
                  prefix={<UserOutlined />}
                  fieldName="lastName"
                  className="w-[20rem]"
                  label={"Last Name"}
                  placeHolder={user?.last_name}
                />
              </div>
              <div className="flex gap-4 mt-2 w-full flex-wrap">
                <InputTemplate
                  disabled={!openModal}
                  outerClassName="!mb-2"
                  prefix={<MobileOutlined />}
                  fieldName="phone"
                  placeHolder={user?.phone}
                  className="w-[20rem] !mb-0"
                  label={"Phone Number"}
                />
                <SelectTemplate
                  disabled={!openModal}
                  fieldName="school_id"
                  className="!w-[20rem]"
                  fieldNames={{ label: "name", value: "id" }}
                  options={schoolList}
                  onChange={(val) => {
                    setNewUserSchool(val);
                    setSchoolPrograms([]);
                    studentUploadForm.setFieldValue("program_id", "");
                  }}
                  label={"School"}
                  placeHolder={student?.school_id}
                  prefix={<FundProjectionScreenOutlined />}
                />

                <SelectTemplate
                  disabled={!openModal}
                  fieldName="program_id"
                  className="!w-[20rem]"
                  fieldNames={{ label: "name", value: "id" }}
                  options={schoolPrograms}
                  label={"Program"}
                  placeHolder={"Electrical Engineering"}
                  prefix={<FundProjectionScreenOutlined />}
                />
                <SelectTemplate
                  disabled={!openModal}
                  fieldName="level"
                  className="!w-[20rem]"
                  fieldNames={{ label: "name", value: "id" }}
                  options={EducationLevelList}
                  label={"Level"}
                  placeHolder={"Undergraduate"}
                  prefix={<ApartmentOutlined />}
                />
              </div>
              <div className="flex justify-end mt-4 gap-2">
                {openModal && (
                  <ButtonTemplate
                    handleClick={() => {
                      setOpenModal(false);
                    }}
                    label="Cancel"
                  />
                )}
                {openModal && (
                  <ButtonTemplate
                    htmlType="submit"
                    handleClick={() => {
                      if (!openModal) {
                        setOpenModal(true);
                      } else {
                      }
                    }}
                    label={openModal ? "Save" : "Edit"}
                  />
                )}
                {!openModal && (
                  <ButtonTemplate
                    handleClick={() => {
                      setOpenModal(true);
                    }}
                    label={"Edit"}
                  />
                )}
              </div>
            </Form>
          </div>
        </div>

        {/* PRIVACY */}
        <div className="px-6 pb-6 border rounded-md mt-4">
          <h3 className="text-sm font-semibold my-6">Privacy Settings</h3>

          <div className="  mt-4 ">
            <div>
              <div className="flex items-center justify-between">
                <div className="  ">{"Auto Logout"}</div>
                <Form
                  name="autoLogoutForm"
                  onFinish={(onFinish) => {}}
                  form={studentLogoutForm}
                >
                  <SwitchTemplate
                    onChange={(val) => {
                      Cookies.set("auto-logout", val.toString());
                      console.log("val", val);
                    }}
                    fieldName="autoLogout"
                  />
                </Form>
              </div>
              <Divider className="mx-2  bg-stone-100" />
            </div>
          </div>

          <h3 className="text-sm font-semibold my-6">Notification Settings</h3>

          <div className="  mt-4 ">
            <Form
              name="subscriptionForm"
              onFinish={onFinish}
              form={subscriptionForm}
            >
              <div>
                <div className="flex items-center justify-between">
                  <div className="  ">
                    {"Get notification on newly published courses"}
                  </div>
                  <SwitchTemplate
                    onChange={(val) => {
                      console.log("new", user?.id);
                      updateNewCourse(
                        new_course_subscription === null
                          ? {
                              user_id: user?.id,
                              name: `${user?.first_name} ${user?.last_name}`,
                              email: student?.email,
                              phone: student?.phone,
                              is_active: val,
                              notification_preference: "email",
                            }
                          : { ...new_course_subscription, is_active: val },
                        new_course_subscription === null
                          ? ""
                          : new_course_subscription.id,
                        new_course_subscription === null ? true : false
                      );
                      console.log("new_course_subscription", val);
                    }}
                    fieldName="new_course_subscription"
                  />
                </div>
                <Divider className="mx-2  bg-stone-100" />
              </div>
              <div>
                <div className="flex items-center justify-between">
                  <div className="  ">
                    {"Get notification about new trending courses"}
                  </div>
                  <SwitchTemplate
                    onChange={(val) => {
                      updateTrendingCourse(
                        new_trending_course_subscription === null
                          ? {
                              user_id: user?.id,
                              name: `${user?.first_name} ${user?.last_name}`,
                              email: student?.email,
                              phone: student?.phone,
                              is_active: val,
                              notification_preference: "email",
                            }
                          : {
                              ...new_trending_course_subscription,
                              is_active: val,
                            },
                        new_trending_course_subscription === null
                          ? ""
                          : new_trending_course_subscription.id,
                        new_trending_course_subscription === null ? true : false
                      );

                      // updateTrendingCourse({
                      //     ...new_trending_course_subscription, is_active:val
                      // }, new_trending_course_subscription.id, new_trending_course_subscription === null? true: false)
                      // console.log('new_course_subscription', val)
                    }}
                    fieldName="new_trending_course_subscription"
                  />
                </div>
                {/* <Divider className='mx-2  bg-stone-100' /> */}
              </div>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
