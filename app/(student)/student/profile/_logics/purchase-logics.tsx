import { ColumnsType, TableProps } from "antd/es/table"



export const PurchaseLogics = () => {
// Get visible columns based on screen size
const columns: TableProps<any>['columns'] = [
  {
    title: 'Course',
    dataIndex: 'course',
    key: 'course',
  
  },
  
  {
    title: 'Payment Method',
    dataIndex: 'payment_method',
    key: 'payment_method',
  },
  {
    title: 'Date',
    dataIndex: 'date',
    key: 'date',
  },
 
];

  // Get the columns based on current screen size
  return {columns}

}