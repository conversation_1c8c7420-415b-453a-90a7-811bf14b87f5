'use client';

import { useState } from "react";
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Spin } from "antd";
import { useStudentStore } from "@/store/studentStore";
import { useUserStore } from "@/store/userStore";

export const useStudentImageUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);
  // Adding an abort controller to prevent multiple simultaneous requests
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const { student, setStudent } = useStudentStore();
  const { user } = useUserStore();

  /**
   * Upload a profile image for the student
   * @param file The image file to upload
   * @returns The updated student data or null if there was an error
   */
  const uploadProfileImage = async (file: File) => {
    try {
      // Cancel any ongoing upload requests to prevent multiple simultaneous requests
      if (abortController) {
        abortController.abort();
      }
      
      // Create a new abort controller for this request
      const controller = new AbortController();
      setAbortController(controller);
      
      setIsUploading(true);
      const studentId = student?.id || user?.id;

      if (!studentId) {
        showNotification('error', 'Error', 'Student ID not found');
        setIsUploading(false);
        setAbortController(null);
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your profile image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      // Note: We can't use AbortController directly with the current API hook
      // But we'll still use our state to track and prevent rapid consecutive uploads
      const response = await request(
        "POST",
        `/student/${studentId}/upload-profile-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the student state with the new image
        const updatedStudent = { ...student };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedStudent.profile_image_path = e.target.result as string;

            // Update the student state with the server response data
            if (response.data && response.data.data) {
              if (response.data.data.profile_image) {
                updatedStudent.profile_image = response.data.data.profile_image;
              } else if (response.data.data.profile_image_path) {
                updatedStudent.profile_image_path_server = response.data.data.profile_image_path;
              }
            }

            setStudent({...updatedStudent});
          }
        };

        setStudent(updatedStudent);
        showNotification('success', 'Success', 'Profile image updated successfully');
        setIsUploading(false);
        setAbortController(null);
        return updatedStudent;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        setIsUploading(false);
        setAbortController(null);
        return null;
      } else {
        // Show user-friendly error messages instead of technical backend errors
        let errorMessage = 'Failed to upload profile image. Please try again later.';
        // Log the actual error for debugging but don't show to user
        console.error('Profile image upload error:', response?.data?.detail);
        
        showNotification('error', 'Upload Failed', errorMessage);
        setIsUploading(false);
        setAbortController(null);
        return null;
      }
    } catch (error: any) {
      // Don't show error for aborted requests
      if (error.name !== 'AbortError') {
        destroyNotifications();
        
        // Show user-friendly error message instead of technical backend errors
        const userFriendlyMessage = 'Failed to upload profile image. Please try again later.';
        
        // Log the actual error for debugging
        console.error('Profile image upload error:', error?.response?.data?.detail || error.message);
        
        showNotification('error', 'Upload Failed', userFriendlyMessage);
      }
      setIsUploading(false);
      setAbortController(null);
      return null;
    }
  };

  /**
   * Upload a cover image for the student
   * @param file The image file to upload
   * @returns The updated student data or null if there was an error
   */
  const uploadCoverImage = async (file: File) => {
    try {
      // Cancel any ongoing upload requests to prevent multiple simultaneous requests
      if (abortController) {
        abortController.abort();
      }
      
      // Create a new abort controller for this request
      const controller = new AbortController();
      setAbortController(controller);
      
      setIsUploading(true);
      const studentId = student?.id || user?.id;

      if (!studentId) {
        showNotification('error', 'Error', 'Student ID not found');
        setIsUploading(false);
        setAbortController(null);
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your cover image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      // Note: We can't use AbortController directly with the current API hook
      // But we'll still use our state to track and prevent rapid consecutive uploads
      const response = await request(
        "POST",
        `/student/${studentId}/upload-cover-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the student state with the new image
        const updatedStudent = { ...student };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedStudent.cover_image_path = e.target.result as string;

            // Update the student state with the server response data
            if (response.data && response.data.data) {
              if (response.data.data.cover_image) {
                updatedStudent.cover_image = response.data.data.cover_image;
              } else if (response.data.data.cover_image_path) {
                updatedStudent.cover_image_path_server = response.data.data.cover_image_path;
              }
            }

            setStudent({...updatedStudent});
          }
        };

        setStudent(updatedStudent);
        showNotification('success', 'Success', 'Cover image updated successfully');
        setIsUploading(false);
        setAbortController(null);
        return updatedStudent;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        setIsUploading(false);
        setAbortController(null);
        return null;
      } else {
        // Show user-friendly error messages instead of technical backend errors
        let errorMessage = 'Failed to upload cover image. Please try again later.';
        // Log the actual error for debugging but don't show to user
        console.error('Cover image upload error:', response?.data?.detail);
        
        showNotification('error', 'Upload Failed', errorMessage);
        setIsUploading(false);
        setAbortController(null);
        return null;
      }
    } catch (error: any) {
      // Don't show error for aborted requests
      if (error.name !== 'AbortError') {
        destroyNotifications();
        
        // Show user-friendly error message instead of technical backend errors
        const userFriendlyMessage = 'Failed to upload cover image. Please try again later.';
        
        // Log the actual error for debugging
        console.error('Cover image upload error:', error?.response?.data?.detail || error.message);
        
        showNotification('error', 'Upload Failed', userFriendlyMessage);
      }
      setIsUploading(false);
      setAbortController(null);
      return null;
    }
  };

  /**
   * Get the profile image URL for the student
   * @returns The profile image URL or an empty string if no image is found
   */
  const getProfileImageUrl = () => {
    if (student?.profile_image_path) {
      return student.profile_image_path;
    } else if (student?.profile_image) {
      return student.profile_image;
    } else if (student?.profile_image_path_server) {
      return student.profile_image_path_server;
    }
    return '';
  };

  /**
   * Get the cover image URL for the student
   * @returns The cover image URL or an empty string if no image is found
   */
  const getCoverImageUrl = () => {
    if (student?.cover_image_path) {
      return student.cover_image_path;
    } else if (student?.cover_image) {
      return student.cover_image;
    } else if (student?.cover_image_path_server) {
      return student.cover_image_path_server;
    }
    return '';
  };

  return {
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    isUploading,
    tokenExpired
  };
};
