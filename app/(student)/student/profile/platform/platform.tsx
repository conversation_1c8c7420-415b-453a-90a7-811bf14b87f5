"use client";
import InputTemplate from "@/components/ui/input-template";
import TagTemplate from "@/components/ui/tag-template";
import {
  CameraOutlined,
  CloudUploadOutlined,
  FacebookFilled,
  FileOutlined,
  LinkedinFilled,
  MailOutlined,
  PhoneOutlined,
  SearchOutlined,
  UserOutlined,
} from "@ant-design/icons";
import React, { useState } from "react";
import Image from "next/image";
import TableTemplate from "@/components/ui/table-template";
import { useUserStore } from "@/store/userStore";
import { Tooltip } from "antd";
import { useStudentImageUpload } from "../_logics/image-upload-logics";
import { useStudentStore } from "@/store/studentStore";
import {
  getProfileImageUrl as getImageUrl,
  getCoverImageUrl as getCoverUrl,
} from "@/utils/image-helpers";

export default function PlatformPage() {
  const { user } = useUserStore();
  const { student } = useStudentStore();
  const {
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    isUploading,
  } = useStudentImageUpload();

  const [interests, setInterests] = useState([
    "Design System",
    "Python",
    "Nursing",
    "UI/UX",
    "Motion Graphics",
    "Wireframing",
  ]);

  const handleProfileImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadProfileImage(file);
    }
  };

  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadCoverImage(file);
    }
  };

  return (
    <div className="overflow-y-scroll w-full overflow-clip h-[80dvh] ">
      <div className="rounded-md border w-full">
        <div className="relative">
          {/* Cover Image with better preview handling */}
          <div className="relative w-full h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden">
            {student?.cover_image_path ? (
              <img
                src={String(student.cover_image_path)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image ? (
              <img
                src={
                  student.cover_image.startsWith("data:")
                    ? student.cover_image
                    : `data:image/jpeg;base64,${student.cover_image}`
                }
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image_path_server ? (
              <img
                src={String(student.cover_image_path_server)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              /* Default gradient background when no image is available */
              <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-800 rounded-md"></div>
            )}
            <label
              htmlFor="cover-image-upload"
              className="absolute top-3 right-3 bg-white rounded-full p-2 cursor-pointer shadow-md hover:bg-gray-100 transition-all"
            >
              <Tooltip title="Change cover image">
                <CloudUploadOutlined className="text-xl" />
              </Tooltip>
              <input
                id="cover-image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleCoverImageUpload}
                disabled={isUploading}
              />
            </label>
          </div>
          {/* Profile Image with better preview handling */}
          <div className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-gray-100">
            {student?.profile_image_path ? (
              <img
                src={String(student.profile_image_path)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image ? (
              <img
                src={
                  student.profile_image.startsWith("data:")
                    ? student.profile_image
                    : `data:image/jpeg;base64,${student.profile_image}`
                }
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image_path_server ? (
              <img
                src={String(student.profile_image_path_server)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              /* Default user icon when no image is available */
              <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                <UserOutlined style={{ fontSize: "24px" }} />
              </div>
            )}
            <label
              htmlFor="profile-image-upload"
              className="absolute bottom-0 right-0 bg-white rounded-full p-1 cursor-pointer shadow-md hover:bg-gray-100 transition-all"
            >
              <Tooltip title="Change profile picture">
                <CameraOutlined className="text-sm" />
              </Tooltip>
              <input
                id="profile-image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleProfileImageUpload}
                disabled={isUploading}
              />
            </label>
          </div>
        </div>

        {/*  */}
        <div className="flex flex-wrap mt-8 justify-between items-center">
          <div className="p-6 text-start">
            <h2 className="font-semibold text-sm">{`${user?.first_name} ${user?.last_name}`}</h2>
            <p className="text-gray-500 text-xs">{`${user?.email}`}</p>
          </div>

          <div className="flex !flex-col sm:flex-row justify-center items-start   p-6 text-center">
            <h2 className="font-medium text-xs">Social Media Links</h2>
            <div className="flex justify-center text-xl gap-2 mt-1">
              <FacebookFilled />
              <LinkedinFilled />
            </div>
          </div>
        </div>

        <div className="px-6 pb-6">
          <h3 className="text-sm font-semibold mb-2">Examination undertaken</h3>
          <div className="flex flex-wrap  gap-4">
            <div className="bg-white border rounded-lg text-center md:w-[10rem] w-full flex flex-col justify-between">
              <div className="relative p-1 h-[4.5rem]">
                <img
                  src="https://plus.unsplash.com/premium_photo-1681422570054-9ae5b8b03e46?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8c3Vuc2V0fGVufDB8fDB8fHww"
                  className="w-full h-16 object-cover rounded-md"
                  alt="Cover Image"
                />
                <img
                  src="https://images.pexels.com/photos/2743754/pexels-photo-2743754.jpeg?auto=compress&cs=tinysrgb&w=800"
                  className="absolute bottom-0 left-3 transform translate-y-1/2 size-10 rounded-full border-4 border-white object-cover"
                  alt="Profile Picture"
                />
              </div>
              <p className="mb-4 mt-5 font-normal text-xs text-left p-1 h-[3rem] line-clamp-2 ">
                UI/UX beginner course
              </p>
              <div className="flex justify-between bg-stone-200 px-4 py-1.5 rounded-b-md">
                <div>
                  <FileOutlined />
                </div>
                <div className="font-semibold"> View Insights</div>
              </div>
            </div>
            <div className="bg-white border rounded-lg text-center md:w-[10rem] w-full flex flex-col justify-between">
              <div className="relative p-1 h-[4.5rem]">
                <img
                  src="https://images.pexels.com/photos/1089438/pexels-photo-1089438.jpeg?auto=compress&cs=tinysrgb&w=800"
                  className="w-full h-16 object-cover rounded-md"
                  alt="Cover Image"
                />
                <img
                  src="https://images.pexels.com/photos/2743754/pexels-photo-2743754.jpeg?auto=compress&cs=tinysrgb&w=800"
                  className="absolute bottom-0 left-3 transform translate-y-1/2 size-10 rounded-full border-4 border-white object-cover"
                  alt="Profile Picture"
                />
              </div>
              <p className="mb-4 mt-5 font-normal text-xs text-left p-1 h-[3rem] line-clamp-2 ">
                Introduction to Python Programming
              </p>
              <div className="flex justify-between bg-stone-200 px-4 py-1.5 rounded-b-md">
                <div>
                  <FileOutlined />
                </div>
                <div className="font-semibold"> View Insights</div>
              </div>
            </div>
            <div className="bg-white border rounded-lg text-center md:w-[10rem] w-full flex flex-col justify-between">
              <div className="relative p-1 h-[4.5rem]">
                <img
                  src="https://plus.unsplash.com/premium_photo-1681422570054-9ae5b8b03e46?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8c3Vuc2V0fGVufDB8fDB8fHww"
                  className="w-full h-16 object-cover rounded-md"
                  alt="Cover Image"
                />
                <img
                  src="https://images.pexels.com/photos/2743754/pexels-photo-2743754.jpeg?auto=compress&cs=tinysrgb&w=800"
                  className="absolute bottom-0 left-3 transform translate-y-1/2 size-10 rounded-full border-4 border-white object-cover"
                  alt="Profile Picture"
                />
              </div>
              <p className="mb-4 mt-5 font-normal text-xs text-left p-1 h-[3rem] line-clamp-2 ">
                UI/UX beginner course
              </p>
              <div className="flex justify-between bg-stone-200 px-4 py-1.5 rounded-b-md">
                <div>
                  <FileOutlined />
                </div>
                <div className="font-semibold"> View Insights</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* INTERESTS */}
      <div className="px-6 pb-6 border rounded-md mt-4">
        <h3 className="text-sm font-semibold mb-2 mt-6">Interests</h3>
        <InputTemplate
          fieldName="search"
          label={""}
          className="!bg-[#EFEFEF] border-none !rounded-2xl md:w-[30rem] w-full !focus:border-none !hover:border-none"
          placeHolder="Search"
          prefix={<SearchOutlined />}
        />
        <div className="flex flex-wrap gap-2 mt-4 ">
          {interests &&
            interests.map((interest, index) => (
              <TagTemplate key={index} value={interest} closeIcon={true} />
            ))}
        </div>
      </div>

      {/* SUPPORT */}
      <div className="flex-wrap px-6 pb-6 border bg-primaryColor pt-6 rounded-md mt-4 flex items-center justify-between">
        <div>
          <div className="text-2xl font-semibold text-white">
            24*7 Customer Support
          </div>
          <div className="text-sm my-2 font-light text-white">
            Have a query? Don’t worry we are here.
          </div>
          <div className="flex gap-4 my-4 flex-wrap space-y-2">
            <div className="border rounded-2xl border-white p-2 w-max text-white">
              <PhoneOutlined className="rotate-90 mr-2" />
              <span>Call us on +************</span>
            </div>
            <div className="border rounded-2xl border-white p-2 px-4 w-max text-white">
              <MailOutlined className="mr-2" />
              <span>Write to us</span>
            </div>
          </div>
        </div>
        <div>
          <Image src={"/clock.png"} alt="logo" width={350} height={1} />
        </div>
      </div>

      {/* PAYMENTS HISTORY */}
      <div className="px-6 pb-6 border rounded-md mt-4  w-full overflow-x-scroll">
        <h3 className="text-sm font-semibold mb-2 mt-6">Payment History</h3>
        <div className=" w-full  overflow-x-scroll flex justify-center">
          <TableTemplate columns={[]} data={[]} />
        </div>
      </div>
    </div>
  );
}
