"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import {
  UserOutlined,
  CameraOutlined,
  CloudUploadOutlined,
  TrophyOutlined,
  BookOutlined,
  ThunderboltOutlined,
  FacebookFilled,
  LinkedinFilled,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useStudentStore } from "@/store/studentStore";
import { useUserStore } from "@/store/userStore";
import { useStudentProfile } from "@/logics/useStudentProfile";

// Offer Card Component
interface OfferCardProps {
  amount: string;
  value: string;
  date: string;
}

const OfferCard: React.FC<OfferCardProps> = ({ amount, value, date }) => {
  return (
    <div className=" flex-1 bg-white rounded-lg shadow overflow-hidden border border-gray-100 hover:shadow-md transition-shadow duration-300">
      <div className="p-5 bg-teal-700 text-white">
        <p className="text-xl font-bold">{amount}</p>
      </div>
      <div className="p-5">
        <h4 className="text-lg font-semibold text-gray-800">{value}</h4>
        <p className="text-sm text-gray-500 mt-2">{date}</p>
      </div>
    </div>
  );
};

export default function RewardsPage() {
  const [offers] = useState([
    {
      amount: "GHS 100 off",
      value: "Design Process",
      date: "Valid until 03 March 2025",
    },
    {
      amount: "30% off",
      value: "Wireframing Courses",
      date: "Valid until 05 April 2025",
    },
    {
      amount: "GHS 200 off",
      value: "Course Bundle",
      date: "Valid until 15 January 2025",
    },
    {
      amount: "20% off",
      value: "Nursing Courses",
      date: "Valid until 09 September 2025",
    },
    {
      amount: "20% off",
      value: "Cloud Computing",
      date: "Valid until 09 September 2025",
    },
  ]);

  const router = useRouter();
  const { student } = useStudentStore();
  const { user } = useUserStore();
  const {
    uploadProfileImage,
    uploadCoverImage,
    getProfileImageUrl,
    getCoverImageUrl,
    fetchStudentProfile,
    loading: profileLoading,
  } = useStudentProfile();

  // Load student profile on component mount
  useEffect(() => {
    fetchStudentProfile();
  }, []);

  // Handle profile image upload
  const handleProfileImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadProfileImage(file);
    }
  };

  // Handle cover image upload
  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadCoverImage(file);
    }
  };
  // Offers already declared above

  return (
    <div className="overflow-y-scroll h-[80dvh] ">
      <div className="rounded-md border">
        <div className="relative">
          {/* Cover Image with better preview handling */}
          <div className="relative w-full h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden">
            {student?.cover_image_path ? (
              <img
                src={String(student.cover_image_path)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image ? (
              <img
                src={
                  student.cover_image.startsWith("data:")
                    ? student.cover_image
                    : `data:image/jpeg;base64,${student.cover_image}`
                }
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : student?.cover_image_path_server ? (
              <img
                src={String(student.cover_image_path_server)}
                alt="Cover"
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              /* Default gradient background when no image is available */
              <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-800 rounded-md"></div>
            )}
          </div>
          {/* Profile Image with better preview handling */}
          <div className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-gray-100">
            {student?.profile_image_path ? (
              <img
                src={String(student.profile_image_path)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image ? (
              <img
                src={
                  student.profile_image.startsWith("data:")
                    ? student.profile_image
                    : `data:image/jpeg;base64,${student.profile_image}`
                }
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : student?.profile_image_path_server ? (
              <img
                src={String(student.profile_image_path_server)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              /* Default user icon when no image is available */
              <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                <UserOutlined style={{ fontSize: "24px" }} />
              </div>
            )}
          </div>
        </div>
        <div className="flex mt-8 justify-between">
          <div className="p-6 text-start">
            <h2 className="font-semibold text-sm">
              {user?.first_name && user?.last_name
                ? `${user.first_name} ${user.last_name}`
                : ""}
            </h2>
            <p className="text-gray-500 text-xs">{user?.email || ""}</p>
          </div>

          <div className="flex justify-center space-x-4 mb-4">
            <div className="p-6 text-center">
              <h2 className="font-medium text-xs">Social Media Links</h2>
              <div className="flex justify-end text-xl gap-1 mt-1">
                <FacebookFilled />
                <LinkedinFilled />
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 pb-6">
          <h3 className="text-sm font-semibold mb-2">Offers only for you</h3>

          <div className="mt-4">
           <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-y-auto">
  {offers.map((offer, index) => (
    <OfferCard
      key={index}
      amount={offer.amount}
      value={offer.value}
      date={offer.date}
    />
  ))}
</div>

          </div>
        </div>
        <div className="px-6 py-2">
          <div className="bg-white flex rounded-lg  p-6 w-full border border-primaryColor">
            <div className="w-1/2">
              <img
                src="https://images.pexels.com/photos/236910/pexels-photo-236910.jpeg?auto=compress&cs=tinysrgb&w=1200"
                alt="Shopping Cart"
                className="w-full h-auto rounded-lg"
                onError={(e) => {
                  // Fallback to a colored background if image fails to load
                  e.currentTarget.style.display = "none";
                  e.currentTarget.parentElement?.classList.add("bg-teal-100");
                }}
              />
            </div>
            <div className="w-1/2 flex flex-col justify-center p-6">
              <h2 className="text-2xl font-semibold">
                Your favorite courses made affordable for you
              </h2>
              <p className="text-gray-600 mt-2">
                Become a part of our courses and begin your successful journey.
              </p>
              <Button type="primary" className="mt-4 w-full bg-teal-700">
                Go to Cart
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
