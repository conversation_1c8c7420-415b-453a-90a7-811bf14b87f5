"use client";
import React, { useEffect, useRef, useState } from 'react';
import { SorterResult } from 'antd/es/table/interface';
import dynamic from 'next/dynamic';
import { ArrowRightOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import courseFrame from '@/public/course-frame.png'
import Image from 'next/image'
import TagTemplate from '@/components/ui/tag-template';
import ButtonTemplate from '@/components/ui/button-template';
import { Divider } from 'antd';
import InputTemplate from '@/components/ui/input-template';
import { CartLogics } from './_logics/cart_logics';
import { useCartStore } from '@/store/cartStore';
import { div } from 'framer-motion/client';
import { EducationLevelList } from '@/utils/levels';
import { useUserStore } from '@/store/userStore';

const MotionDiv = dynamic(() => import('framer-motion').then(mod => mod.motion.div), { ssr: false });

interface UINotification {
    timestamp: any;
    key: string;
    sender: string;
    type: string;
    content: string;
    date: string;
    time: string;
    status: 'viewed' | 'unviewed';
}

export default function CartPage() {
    const { checkoutCart, removeFromCart } = CartLogics()
    const { cart } = useCartStore()
    const { user } = useUserStore()

    useEffect(() => {
        //   getCart();
    }, []);




    return (
        <div className="w-full p-6 h-full overflow-y-scroll">
            <MotionDiv
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="relative w-full"
            >
                <img src="/notification.png" alt="Notifications Header" className="w-full rounded-lg" />
                <div className="absolute inset-0 flex items-center justify-center">
                    <h1 className="text-2xl md:text-3xl font-bold text-white">My Cart</h1>
                </div>
                <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 bg-white p-3 rounded-full shadow-lg">
                    <ShoppingCartOutlined className="text-2xl text-gray-600" />
                </div>
            </MotionDiv>

            <div className="mt-20 text-black">

                {cart?.items.map((cartItem, index) => {
                    return <div key={index}>
                        <div className='flex items-center justify-between '>
                            <div className='flex items-center gap-x-2'>
                                <div>
                                    <Image src={`${cartItem.course?.cover_image && cartItem.course?.cover_image.startsWith('iVBOR')? 'data:image/png;base64': 'data:image/jpeg;base64'},${cartItem.course.cover_image}`} height={100} width={200} alt='course frame' />
                                </div>
                                <div className='font-semibold'>{cartItem.course.name}</div>
                            </div>
                            <div className='flex items-center gap-x-4'>
                                <TagTemplate className='rounded-md !bg-lime-200 !text-black' value={`${EducationLevelList.find(level => level.id === cartItem.course.level)?.name}`} />
                                <div className='text-primaryColor font-semibold'>GHS {cartItem.price}</div>
                                <div className='gap-y-2 flex flex-col'>
                                    <div><ButtonTemplate handleClick={()=>{
                                        checkoutCart(
                                            '',user?.id!, [cartItem.course_id], [cartItem.course.teacher_id],false, 'pending','string', 
                                            Date.now().toString(),cartItem.cart_id,
                                            user?.email!,cartItem.price )
                                    }} className='bg-primaryColor text-white' label={"Buy Now"} /></div>
                                    <div><ButtonTemplate handleClick={()=>{
                                        removeFromCart(cartItem.course_id)
                                    }} label={"Remove"} /></div>
                                </div>
                            </div>
                        </div>
                        <Divider className='bg-[#BCBBBB]' />
                    </div>
                })}

            

            
                <div className='flex justify-between'>
                    <InputTemplate label={'Coupon'} fieldName={'coupon'} placeHolder='Enter Coupon Code' />
                    <div>
                        <div>
                            <div className='flex gap-x-6 mb-3'>
                                <div className='text-gray-500 font-semibold'>Total</div>
                                <div className='text-primaryColor font-semibold'>GHS {cart?.items.reduce((total, item)=> total+ item?.price, 0)}</div>
                             </div>
                            <div className='gap-y-2 flex flex-col'>
                                <div className='flex bg-black rounded-2xl items-center' ><div className='text-white  px-4'>Buy Now</div> <ButtonTemplate className='bg-primaryColor !w-[1rem] !rounded-full text-white' label={<ArrowRightOutlined />} /></div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    
    );
}