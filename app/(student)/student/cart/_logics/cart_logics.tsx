import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form, Spin } from "antd";
import React, { useEffect } from "react";
import { useState } from "react"
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useCartStore } from '@/store/cartStore';
import { UserOverviewLogics } from "@/logics/student-overview";

export const CartLogics = () => {

    const router = useRouter();
    const { request } = useApi();
    const { showNotification, destroyNotifications } = useNotification();
    const [loading, setLoading] = useState(false);
    const [authForm] = Form.useForm();
    const { cart } = useCartStore();
    const { setUpMe } = UserOverviewLogics()




    //Get Cart
    async function getCart() {
        try {
            // showNotification('success', 'Signing In', 'Signing into your account...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("GET", "/cart/user", null, "multipart/form-data",

            );
            setLoading(false);
            // destroyNotifications();
            if (requestResponse && requestResponse?.status === 200) {
                console.log('response', requestResponse?.data?.data?.courses)
                return requestResponse?.data?.data?.courses;

            } else {
                showNotification('error', 'Error searching courses', requestResponse.data.detail);

            }



        } catch (error) {

        }
    }
    async function getCartFromME() {
        try {
            // showNotification('success', 'Signing In', 'Signing into your account...', true, <Spin />);
            console.log('getting cart details')
            setLoading(true);
            const requestResponse: any = await request("GET", "/protected/me", null, "multipart/form-data",

            );
            setLoading(false);
            // destroyNotifications();
            if (requestResponse && requestResponse?.status === 200) {
                console.log('got from me', requestResponse?.data?.data?.cart)
                setUpMe(requestResponse, false)
                // return requestResponse?.data?.data?.courses;

            } else {
                showNotification('error', 'Error searching courses', requestResponse.data.detail);

            }



        } catch (error) {
            console.log('got not me', error)


        }
    }
    async function addToCart(courseID: string) {
        try {
            showNotification('success', 'Adding to Cart', 'Adding item to your cart...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("POST", `/cart/courses/${courseID}`, null, "multipart/form-data",

            );
            console.log('request', requestResponse)

            await getCartFromME();
            setLoading(false);
            console.log('destroyed notif', requestResponse)

            destroyNotifications();

            if (requestResponse && requestResponse?.status === 200) {
                console.log('ugh cart id', courseID);


                console.log('here is response', requestResponse?.data?.data?.courses)
                showNotification('success', 'Added to Cart', 'Successfully added Course to cart');

                return requestResponse?.data?.data?.courses;

            } else {
                showNotification('error', 'Error adding to cart', requestResponse.data.detail);

            }



        } catch (error) {
            console.log('here is error resp=', error)

        }
    }

    async function removeFromCart(courseID: string) {
        try {
            console.log('courseID', courseID);
            showNotification('success', 'Removing from Cart', 'Removing item from your cart...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("DELETE", `/cart/courses/${courseID}`, null, "multipart/form-data",


            );
            setLoading(false);
            destroyNotifications();
            if (requestResponse && requestResponse?.status === 200) {
                getCartFromME();

                console.log('response', requestResponse?.data?.data?.courses)
                showNotification('success', 'Removed from Cart', 'Successfully removed Course to cart');

                return requestResponse?.data?.data?.courses;

            } else {
                showNotification('error', 'Error adding to cart', requestResponse.data.detail);

            }



        } catch (error) {

        }
    }

    // Checkout
    async function checkoutCart(reference: string, userID: string, courses: string[], teachersIDs: string[], teacher_payment_processed: boolean,
        status: string, payment_method: string, transaction_date: string, cart_id: string, userEmail: string, amount: number) {
        try {
            showNotification('success', 'Check out', 'Checking out item...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("POST", `/checkout/checkout-url`, {
                "email": userEmail,
                "amount": amount * 100,
            }, "",


            );
            setLoading(false);
            console.log('destroyed notif', requestResponse)

            // destroyNotifications();

            if (requestResponse && requestResponse?.status === 200) {


                console.log('here is response A', requestResponse?.data?.data?.data?.authorization_url)

                // showNotification('success', 'Added to Cart', 'Successfully added Course to cart');

                // return requestResponse?.data?.data?.courses;
                checkoutComplete(
                    requestResponse?.data?.data?.data?.reference, amount, userID, courses, teachersIDs, teacher_payment_processed, status, payment_method, transaction_date, cart_id)
                // if (requestResponse?.data?.data?.data?.authorization_url) {
                //     window.open(requestResponse?.data?.data?.data?.authorization_url, '_blank');
                // } else {
                //     alert('Failed to initiate payment');
                // }

            } else {
                destroyNotifications();
                showNotification('error', 'Error adding to cart', requestResponse.data.detail);

            }



        } catch (error) {
            console.log('here is error resp=', error)

        }
    }
    async function checkoutComplete(reference: string, amount: number, userID: string, courses: string[], teachersIDs: string[], teacher_payment_processed: boolean, status: string, payment_method: string, transaction_date: string, cart_id: string) {
        try {
            showNotification('success', 'Check out', 'Checking out item...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("POST", `/checkout`,
                {
                    "reference": reference,
                    "amount": amount,
                    "user_id": userID,
                    "courses": courses,
                    "teacher_ids": teachersIDs,
                    "teacher_payment_processed": teacher_payment_processed,
                    "status": status,
                    "payment_method": payment_method,
                    "transaction_date": transaction_date,
                    "cart_id": cart_id,


                },

                "",


            );
            setLoading(false);
            console.log('destroyed notif', requestResponse)

            destroyNotifications();

            if (requestResponse && requestResponse?.status === 200) {


                console.log('here is response', requestResponse?.data?.data)

                showNotification('success', 'Checked Out', 'Checkout payment created successfully');
                getCartFromME();
                // return requestResponse?.data?.data?.courses;

                // if (requestResponse?.data?.data?.data?.authorization_url) {
                //     window.open(requestResponse?.data?.data?.data?.authorization_url, '_blank');
                // } else {
                //     alert('Failed to initiate payment');
                // }

            } else {
                showNotification('error', 'Error adding to cart', requestResponse.data.detail);

            }



        } catch (error) {
            console.log('here is error resp=', error)

        }
    }


    
    async function addToWishlist(courseID: string) {
        try {
            showNotification('success', 'Wishlist', 'Adding to wishlist...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("POST", `/wishlist`, {
                "item_id": courseID,
                "item_type": "course",
                "note": ""

            }, "",


            );
            setLoading(false);
            console.log('destroyed notif', requestResponse)

            // destroyNotifications();
            destroyNotifications();


            if (requestResponse && requestResponse?.status === 200) {
                
                showNotification('success', 'Wishlist', 'Added to wishlist successfully');


            } else {
                showNotification('error', 'Error adding to wishlist', requestResponse.data.detail);

            }


        } catch (error) {
            destroyNotifications();
            showNotification('error', 'Could not add to wishlist');

               
         }
    }
    async function removeFromWishlist(wishListID: string) {
        try {
            showNotification('success', 'Wishlist', 'Removing from wishlist...', true, <Spin />);

            setLoading(true);
            const requestResponse: any = await request("DELETE", `/wishlist/${wishListID}`, {
               

            }, "",


            );
            setLoading(false);
            console.log('destroyed notif', requestResponse)

            // destroyNotifications();
            destroyNotifications();


            if (requestResponse && requestResponse?.status === 200) {
                
                showNotification('success', 'Wishlist', 'Removed from wishlist successfully');


            } else {
                showNotification('error', 'Error removing from wishlist', requestResponse.data.detail);

            }


        } catch (error) {
            destroyNotifications();
            showNotification('error', 'Could not remove to wishlist');

               
         }
    }




    return {
        loading, getCart, addToCart, removeFromCart,
        checkoutCart, addToWishlist, removeFromWishlist

    }
}