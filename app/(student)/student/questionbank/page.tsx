'use client';
import { useState, useEffect } from 'react';
import { FileTextOutlined, FilterOutlined, PlusOutlined, MinusOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { motion, AnimatePresence } from "framer-motion";
import { Spin, Select, ConfigProvider } from "antd";
import {
  useQuestionBankService,
  type PreviewQuestion
} from "@/app/(student)/student/questionbank/_logics/questionBank";

const QuestionBank = () => {
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);
  const [expandedQuestion, setExpandedQuestion] = useState<string | null>(null);

  const {
    fetchQuestions,
    fetchFilterOptions,
    applyFilters,
    resetFilters,
    handleFilterChange,
    filters,
    filterOptions,
    questionGroups,
    loading,
    filtersLoading,
    apiLoading,
    apiError,
    filterError,
    hasAppliedFilters,
    setFilterError
  } = useQuestionBankService();

  useEffect(() => {
    fetchFilterOptions();
  }, [fetchFilterOptions]);

  const toggleGroup = (groupKey: string) => {
    setExpandedGroup(prev => prev === groupKey ? null : groupKey);
    setExpandedQuestion(null);
  };

  const toggleQuestion = (questionId: string) => {
    setExpandedQuestion(prev => prev === questionId ? null : questionId);
  };

  //check if at least two filters are selected
  const handleApplyFilters = () => {
    // Count the number of active filters
    const activeFiltersCount = Object.values(filters).filter(value => value !== "").length;

    // Check if at least two filters are selected
    if (activeFiltersCount < 2) {
      setFilterError("Please select at least two filters before applying.");
      return;
    }

    // If validation passes, apply the filters
    applyFilters();
  };

  // Function to check if Apply button should be disabled
  const isApplyDisabled = () => {
    const activeFiltersCount = Object.values(filters).filter(value => value !== "").length;
    return activeFiltersCount < 2 || loading || apiLoading || filtersLoading;
  };

  const renderOptions = (options?: Record<string, string>) => {
    if (!options) return null;
    return (
      <div className="mt-4 space-y-2">
        {Object.entries(options).map(([key, value]) => (
          <div key={key} className="flex items-start">
            <span className="font-bold mr-2">{key}:</span>
            <span className="text-teal-700">{value}</span>
          </div>
        ))}
      </div>
    );
  };

  const renderQuestionContent = (question: PreviewQuestion) => {
    if (question.question_type === 'multiple_choice') {
      return (
        <div className="mt-4 text-teal-700">
          <h4 className="font-medium mb-2">Question-type: {question.question_type}</h4>
          {renderOptions(question.options)}
        </div>
      );
    } else {
      // For short answers
      if (question.title === 'nlll') {
        return (
          <div className="mt-4 text-teal-700">
            <h4 className="font-medium mb-2">Question-type:</h4>
          </div>
        );
      } else {
        return (
          <div className="mt-4 text-teal-700">
             <h4 className="font-medium mb-2">Question-type: {question.question_type}</h4>
          </div>
        );
      }
    }
  };

  return (
    // Center align everything and make the entire component content scrollable
    <div className="flex justify-center w-full h-full overflow-y-auto">
      <div className="flex flex-col items-center w-full  text-teal-700 px-4">
        {/* Header section */}
        <div className="w-full mt-8">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative w-full"
          >
            <div className="relative w-full flex justify-center">
            <img src="/notification.png" alt="Notifications Header" className="w-full rounded-lg" />
              <div className="absolute inset-0 flex items-center justify-center">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white">Question Bank</h1>
              </div>
              <motion.div
                className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-white p-3 sm:p-4 rounded-full shadow-lg"
                animate={{
                  y: [0, -10, 0],
                  boxShadow: [
                    "0 4px 6px rgba(0, 0, 0, 0.1)",
                    "0 15px 25px rgba(0, 0, 0, 0.2)",
                    "0 4px 6px rgba(0, 0, 0, 0.1)"
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <FileTextOutlined className="text-2xl sm:text-3xl text-teal-500" />
              </motion.div>
            </div>
          </motion.div>

          <div className="mt-16 sm:mt-20 text-center w-full">
            <motion.h2
              className="text-xl sm:text-2xl font-bold text-teal-700 uppercase"
              animate={{
                textShadow: ["0 0 0px rgba(15, 118, 110, 0.3)", "0 0 10px rgba(15, 118, 110, 0.5)", "0 0 0px rgba(15, 118, 110, 0.3)"]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              LEARN KONNECT QUESTION BANK
            </motion.h2>
            <p className="text-sm text-teal-600 mt-2">Made with Love by LearnKonnect ❤️</p>
          </div>
        </div>

        {/* Static Filters section - Now centered with max-width */}
        <div className="mt-10 w-full">
          <div className="p-4 bg-white rounded-lg shadow-md">
            <div className="flex items-center justify-center mb-4">
              <FilterOutlined className="mr-2 text-teal-500" />
              <span className="font-bold text-teal-700">Filters:</span>
            </div>

            {/* Filter grid layout - responsive and accessible */}
            <ConfigProvider
              theme={{
                components: {
                  Select: {
                    colorPrimary: '#14b8a6',
                    colorBorder: '#5EEAD4',
                    colorPrimaryHover: '#0d9488',
                    colorText: '#0f766e',
                    controlHeight: 40,
                    borderRadius: 6,
                    colorBgContainer: 'white',
                    colorTextPlaceholder: '#94a3b8',
                  },
                },
              }}
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-teal-700 mb-1">Level</label>
                  <Select
                    className="w-full"
                    value={filters.level}
                    onChange={(value) => handleFilterChange('level', value)}
                    disabled={filtersLoading}
                    showSearch
                    placeholder="All Levels"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={[
                      { value: '', label: 'All Levels' },
                      ...filterOptions.level.map(level => ({
                        value: level,
                        label: level
                      }))
                    ]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-teal-700 mb-1">Program</label>
                  <Select
                    className="w-full"
                    value={filters.program}
                    onChange={(value) => handleFilterChange('program', value)}
                    disabled={filtersLoading}
                    showSearch
                    placeholder="All Programs"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={[
                      { value: '', label: 'All Programs' },
                      ...filterOptions.program.map(program => ({
                        value: program,
                        label: program
                      }))
                    ]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-teal-700 mb-1">School</label>
                  <Select
                    className="w-full"
                    value={filters.school}
                    onChange={(value) => handleFilterChange('school', value)}
                    disabled={filtersLoading}
                    showSearch
                    placeholder="All Schools"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={[
                      { value: '', label: 'All Schools' },
                      ...filterOptions.school.map(school => ({
                        value: school,
                        label: school
                      }))
                    ]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-teal-700 mb-1">Year</label>
                  <Select
                    className="w-full"
                    value={filters.year}
                    onChange={(value) => handleFilterChange('year', value)}
                    disabled={filtersLoading}
                    showSearch
                    placeholder="All Years"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={[
                      { value: '', label: 'All Years' },
                      ...filterOptions.year.map(year => ({
                        value: year,
                        label: year
                      }))
                    ]}
                  />
                </div>
              </div>
            </ConfigProvider>

            {/* Buttons section - centered */}
            <div className="mt-4 flex flex-wrap gap-3 justify-center">
              <motion.button
                className={`px-6 py-2 ${isApplyDisabled() ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'} text-white rounded-md transition-colors`}
                onClick={handleApplyFilters}
                disabled={isApplyDisabled()}
                whileHover={{ scale: isApplyDisabled() ? 1 : 1.05 }}
                whileTap={{ scale: isApplyDisabled() ? 1 : 0.95 }}
              >
                {(loading || apiLoading) ? 'Applying...' : 'Apply Filters'}
              </motion.button>
              <motion.button
                className="px-6 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 transition-colors"
                onClick={resetFilters}
                disabled={loading || apiLoading || filtersLoading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Reset Filters
              </motion.button>
            </div>

            {filterError && (
              <motion.div
                className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <ExclamationCircleOutlined className="mr-2" />
                <p>{filterError}</p>
              </motion.div>
            )}
          </div>
        </div>

        {/* Content section - centered with max-width */}
        <div className="mt-4 w-full pb-8">
          {!hasAppliedFilters && !loading && !apiLoading && (
            <motion.div
              className="p-4 sm:p-8 bg-white rounded-lg shadow-md text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <FilterOutlined className="text-3xl sm:text-4xl text-teal-500 mb-4" />
              <h3 className="text-lg sm:text-xl font-semibold text-teal-700 mb-2">Select Filters to View Questions</h3>
              <p className="text-teal-600 mb-4">Apply at least two filters to view matching questions.</p>
            </motion.div>
          )}

          {(loading || apiLoading) && (
            <motion.div
              className="p-8 bg-white rounded-lg shadow-md flex justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <Spin size="large" />
            </motion.div>
          )}

          {!loading && !apiLoading && hasAppliedFilters && (
            <motion.div
              className="space-y-4 pb-6 w-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              {questionGroups.length > 0 ? (
                questionGroups.map(group => {
                  const groupKey = `${group.program}-${group.school}-${group.level}-${group.term}-${group.course_code}`;
                  return (
                    <motion.div
                      key={groupKey}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white rounded-lg shadow-md border-l-4 border-teal-500"
                    >
                      <motion.div
                        className="p-3 sm:p-4 flex justify-between items-center cursor-pointer hover:bg-teal-50"
                        onClick={() => toggleGroup(groupKey)}
                      >
                        <div className="flex-1 overflow-hidden">
                          <h3 className="text-base sm:text-lg font-semibold text-teal-700 truncate">
                            {group.course_code} - {group.course_name}
                          </h3>
                          <div className="flex flex-wrap gap-1 sm:gap-2 mt-2 text-xs sm:text-sm text-teal-600">
                            <span className="bg-yellow-100 px-2 py-1 rounded-full">
                              {group.school}
                            </span>
                            <span className='bg-teal-100 px-2 py-1 rounded-full'>
                              {group.program}
                            </span>
                            <span className="bg-green-100 px-2 py-1 rounded-full">
                              Level {group.level}
                            </span>
                            <span className="bg-purple-100 px-2 py-1 rounded-full">
                              {group.term}
                            </span>
                            <span className="bg-pink-100 px-2 py-1 rounded-full">
                              {group.questions.length} Q
                            </span>
                          </div>
                        </div>
                        <motion.div
                          className="flex-none ml-2 flex items-center justify-center w-8 h-8 bg-teal-100 rounded-full text-teal-700"
                          animate={{ rotate: expandedGroup === groupKey ? 180 : 0 }}
                        >
                          {expandedGroup === groupKey ? <MinusOutlined /> : <PlusOutlined />}
                        </motion.div>
                      </motion.div>

                      <AnimatePresence>
                        {expandedGroup === groupKey && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="overflow-hidden"
                          >
                            <div className="p-2 sm:p-4 pt-0 border-t border-teal-100 space-y-2 sm:space-y-4">
                              {group.questions.filter(question => question.title !== 'null' && question.title !== 'Untitled Question').map((question, index) => (
                                <div
                                  key={question.id}
                                  className="border rounded-lg overflow-hidden"
                                >
                                  <motion.div
                                    className="p-3 sm:p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
                                    onClick={() => toggleQuestion(question.id)}
                                  >
                                    <div className="flex items-center gap-2 overflow-hidden">
                                      <span className="flex-none text-teal-500">Q{index + 1}</span>
                                      <span className="font-medium truncate">
                                        {question.title === 'null' || question.title === 'Untitled Question' ? (
                                          <span className="text-red-500">No Question Found, Scroll to the next question</span>
                                        ) : (
                                          question.title
                                        )}
                                      </span>
                                    </div>
                                    <motion.div
                                      className="flex-none ml-2 flex items-center justify-center w-8 h-8 bg-teal-100 rounded-full text-teal-700"
                                      animate={{ rotate: expandedQuestion === question.id ? 180 : 0 }}
                                    >
                                      {expandedQuestion === question.id ? <MinusOutlined /> : <PlusOutlined />}
                                    </motion.div>
                                  </motion.div>

                                  <AnimatePresence>
                                    {expandedQuestion === question.id && (
                                      <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="overflow-hidden"
                                      >
                                        <div className="p-3 sm:p-4 pt-0 border-t">
                                          {renderQuestionContent(question)}
                                          <div className="mt-4 pt-4 border-t">
                                            <h4 className="font-medium mb-2">Answer:</h4>
                                            <p className="text-teal-700 break-words">{question.answer}</p>
                                            <p className="text-teal-700 mt-2">Points: {question.point !== null ? question.point : 'N/A'}</p>

                                            {question.question_type !== 'multiple_choice' && (
                                              <>
                                                <h4 className="font-medium mb-2 mt-2">Explanation:
                                                   {question.explanation || 'No explanation provided'}
                                                </h4>
                                              </>
                                            )}
                                          </div>
                                        </div>
                                      </motion.div>
                                    )}
                                  </AnimatePresence>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  );
                })
              ) : (
                <motion.div
                  className="p-8 bg-gray-50 rounded-lg text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <ExclamationCircleOutlined className="text-3xl text-red-500 mb-4" />
                  <p className="text-red-500">{filterError || "No questions available for the selected filters"}</p>
                  <motion.button
                    className="mt-4 px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 transition-colors"
                    onClick={resetFilters}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Reset Filters
                  </motion.button>
                </motion.div>
              )}
            </motion.div>
          )}

          {apiError && !loading && !apiLoading && hasAppliedFilters && (
            <motion.div
              className="p-8 bg-red-50 rounded-lg text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <p className="text-red-500">No questions available for the chosen filters. Consider relaxing your filter options to see more results.</p>
              <motion.button
                className="mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                onClick={fetchQuestions}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Retry
              </motion.button>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionBank;