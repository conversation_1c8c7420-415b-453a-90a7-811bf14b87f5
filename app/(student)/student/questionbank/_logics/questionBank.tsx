import { use<PERSON>pi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Spin } from "antd";
import { useState, useCallback, useRef } from 'react';

// Question type definitions
export interface Question {
  question_id: string;
  question_type: string;
  year: number;
  subject: string;
  question: string;
  options?: Record<string, string>;
  answer: string;
  point: number;
  explanation: string;
  tags: string[];
  status: string;
  question_text?: string;
}

export interface QuestionMetadata {
  school: string;
  program: string;
  year: number;
  level: number;
  term: string;
  course_code: string;
  course_name: string;
  questions: Question[];
  created_at: string;
  updated_at: string;
}

export interface Filters {
  level: string;
  program: string;
  school: string;
  year: string;
}

export interface FilterOptions {
  level: string[];
  program: string[];
  school: string[];
  year: string[];
}

export interface PreviewQuestion {
  id: string;
  title: string;
  explanation: string;
  level: string;
  program: string;
  school: string;
  term: string;
  year: string;
  course_code: string;
  course_name: string;
  answer: string;
  options?: Record<string, string>;
  question_type: string;
  point: number;
}

interface QuestionGroup {
  program: string;
  school: string;
  level: string;
  term: string;
  course_code: string;
  course_name: string;
  questions: PreviewQuestion[];
  point: number;
}

/**
 * Hook to handle question bank data fetching and processing
 * Provides methods for fetching questions and filter options
 */
export const useQuestionBankService = () => {
  const { request, loading: apiLoading, error: apiError } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filterError, setFilterError] = useState("");
  const [filters, setFilters] = useState({
    level: "",
    program: "",
    school: "",
    year: ""
  });
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    level: [],
    program: [],
    school: [],
    year: []
  });
  const [questionGroups, setQuestionGroups] = useState<QuestionGroup[]>([]);
  const [hasAppliedFilters, setHasAppliedFilters] = useState(false);
  
  // Add refs to track fetch status
  const hasLoadedFilters = useRef(false);
  const isFiltersFetching = useRef(false);
  const isQuestionsFetching = useRef(false);

  /**
   * Map API response to the format expected by the UI
   * Handles various response formats from the API
   */
  const mapResponseToQuestions = useCallback((data: { questions: any[]; data: any; flatMap: (arg0: (this: undefined, item: any) => any) => any; }) => {
    if (!data) return [];
    
    // Handle the case where we have an array of questions inside the "questions" property
    if (data.questions && Array.isArray(data.questions)) {
      return data.questions.flatMap((item: { questions: any[]; level: any; program: any; school: any; term: any; course_code: any; course_name: any; year: any; question_id: any; id: any; question: any; question_text: any; explanation: any; answer: any; options: any; question_type: any; point: any; }) => {
        // If this item has its own nested "questions" array, process those
        if (item.questions && Array.isArray(item.questions)) {
          return item.questions.map((q: { question_id: any; question: any; question_text: any; explanation: any; answer: any; options: any; question_type: any; point: any; }) => ({
            id: q.question_id || `question-${Math.random()}`,
            title: q.question || q.question_text || 'null',
            content: q.explanation || 'No explanation provided',
            level: item.level ? String(item.level) : '',
            program: item.program || '',
            school: item.school || '',
            term: item.term || '',
            course_code: item.course_code || '',
            course_name: item.course_name || '',
            year: item.year ? String(item.year) : '',
            answer: q.answer || '',
            options: q.options,
            question_type: q.question_type || 'text',
            point: q.point,
            explanation: q.explanation || ''
          }));
        }
        
        // Handle the case where the item itself is a question
        return {
          id: item.question_id || item.id || `question-${Math.random()}`,
          title: item.question || item.question_text || 'null',
          content: item.explanation || 'No explanation provided',
          level: item.level ? String(item.level) : '',
          program: item.program || '',
          school: item.school || '',
          term: item.term || '',
          course_code: item.course_code || '',
          course_name: item.course_name || '',
          year: item.year ? String(item.year) : '',
          answer: item.answer || '',
          options: item.options,
          question_type: item.question_type || 'text',
          point: item.point,
          explanation: item.explanation || ''
        };
      });
    }
    
    // Handle the case where we have data in a nested structure
    if (data.data && typeof data.data === 'object') {
      return mapResponseToQuestions(data.data);
    }
    
    // Handle direct array format
    if (Array.isArray(data)) {
      return data.flatMap(item => {
        // Handle items with nested questions array
        if (item && item.questions && Array.isArray(item.questions)) {
          return item.questions.map((q: { question_id: any; question: any; question_text: any; explanation: any; answer: any; options: any; question_type: any; point: any; }) => ({
            id: q.question_id || `question-${Math.random()}`,
            title: q.question || q.question_text || 'null',
            content: q.explanation || 'No explanation provided',
            level: (item.level !== undefined) ? String(item.level) : '',
            program: item.program || '',
            school: item.school || '',
            term: item.term || '',
            course_code: item.course_code || '',
            course_name: item.course_name || '',
            year: (item.year !== undefined) ? String(item.year) : '',
            answer: q.answer || '',
            options: q.options,
            question_type: q.question_type || 'text',
            point: q.point,
            explanation: q.explanation || ''
          }));
        }
        
        // Handle direct question items
        return {
          id: item.question_id || item.id || `question-${Math.random()}`,
          title: item.question || item.question_text || 'null',
          content: item.explanation || 'No explanation provided',
          level: item.level ? String(item.level) : '',
          program: item.program || '',
          school: item.school || '',
          term: item.term || '',
          course_code: item.course_code || '',
          course_name: item.course_name || '',
          year: item.year ? String(item.year) : '',
          answer: item.answer || '',
          options: item.options,
          question_type: item.question_type || 'text',
          point: item.point,
          explanation: item.explanation || ''
        };
      });
    }
    
    console.error("Unexpected API response format:", data);
    return [];
  }, []);

  /**
   * Group questions by program, school, level, term, and course
   */
  const groupQuestions = useCallback((questions: any[]) => {
    const groupsMap = new Map();
    
    questions.forEach((question: { program: any; school: any; level: any; term: any; course_code: any; course_name: any; }) => {
      const groupKey = `${question.program}-${question.school}-${question.level}-${question.term}-${question.course_code}`;
      
      if (!groupsMap.has(groupKey)) {
        groupsMap.set(groupKey, {
          program: question.program,
          school: question.school,
          level: question.level,
          term: question.term,
          course_code: question.course_code,
          course_name: question.course_name,
          questions: []
        });
      }
      groupsMap.get(groupKey)?.questions.push(question);
    });
    
    return Array.from(groupsMap.values()).filter(group => group.questions.length > 0);
  }, []);

  /**
   * Extract unique filter options from the data
   */
  const extractFilterOptions = useCallback((questions: any[]) => {
    const options: FilterOptions = {
      level: [],
      program: [],
      school: [],
      year: []
    };
    
    questions.forEach((q: { level: any; program: any; school: any; year: any; }) => {
      if (q.level && !options.level.includes(q.level)) options.level.push(q.level);
      if (q.program && !options.program.includes(q.program)) options.program.push(q.program);
      if (q.school && !options.school.includes(q.school)) options.school.push(q.school);
      if (q.year && !options.year.includes(q.year)) options.year.push(q.year);
    });
    
    // Sort the options
    options.level.sort();
    options.program.sort();
    options.school.sort();
    options.year.sort((a, b) => Number(b) - Number(a)); // Most recent years first
    
    return options;
  }, []);

  /**
   * Fetch all available filter options from the API
   */
  const fetchFilterOptions = useCallback(async () => {
    // Prevent duplicate fetches
    if (isFiltersFetching.current || 
        (hasLoadedFilters.current && 
         filterOptions.level.length > 0)) {
      return;
    }
    
    try {
      isFiltersFetching.current = true;
      setFiltersLoading(true);
      const response = await request("GET", "/question_bank");
      
      if (response && response.status === 200) {
        const mappedQuestions = mapResponseToQuestions(response.data);
        const options = extractFilterOptions(mappedQuestions);
        setFilterOptions(options);
        hasLoadedFilters.current = true;
      } else {
        throw new Error("Failed to fetch filter options");
      }
    } catch (error) {
      console.error("Error loading filter options:", error);
    } finally {
      setFiltersLoading(false);
      isFiltersFetching.current = false;
    }
  }, [request, mapResponseToQuestions, extractFilterOptions]);

  /**
   * Fetch questions from API with applied filters
   */
  const fetchQuestions = useCallback(async () => {
    // Prevent duplicate fetches
    if (isQuestionsFetching.current) {
      return { success: false, error: "Request already in progress" };
    }
    
    try {
      isQuestionsFetching.current = true;
      setLoading(true);
      
      // For GET requests, query parameters
      const queryParams = new URLSearchParams();
      if (filters.level) queryParams.append('level', filters.level);
      if (filters.program) queryParams.append('program', filters.program);
      if (filters.school) queryParams.append('school', filters.school);
      if (filters.year) queryParams.append('year', filters.year);
      
      const queryString = queryParams.toString();
      const endpoint = `/question_bank${queryString ? `?${queryString}` : ''}`;
      
      showNotification('info', 'Loading', 'Fetching questions...', true, <Spin />);
    
      const response = await request("GET", endpoint);
      destroyNotifications();
      
      if (response && response.status === 200) {
        const mappedQuestions = mapResponseToQuestions(response.data);
        const grouped = groupQuestions(mappedQuestions);
        setQuestionGroups(grouped);
        
        if (grouped.length === 0) {
          setFilterError("No matching questions found for your selected filters.");
        } else {
          setFilterError("");
        }
        return { success: true, data: mappedQuestions };
      } else {
        throw new Error(apiError || 'Unknown error');
      }
    } catch (error) {
      console.error("Error loading questions:", error);
      setFilterError("");
      return { success: false, error: String(error) };
    } finally {
      setLoading(false);
      isQuestionsFetching.current = false;
    }
  }, [request, filters, apiError, showNotification, destroyNotifications, mapResponseToQuestions, groupQuestions]);

  const handleFilterChange = useCallback((filterType: any, value: any) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    if (filterError) setFilterError("");
  }, [filterError]);

  const resetFilters = useCallback(() => {
    setFilters({ level: "", program: "", school: "", year: "" });
    setQuestionGroups([]);
    setHasAppliedFilters(false);
    setFilterError("");
  }, []);

  const applyFilters = useCallback(() => {
    // Add this check to prevent unnecessary fetches
    if (isQuestionsFetching.current) return;
    
    setFilterError("");
    setHasAppliedFilters(true);
    fetchQuestions();
  }, [fetchQuestions]);

  return {
    fetchQuestions,
    fetchFilterOptions,
    applyFilters,
    resetFilters,
    handleFilterChange,
    filters,
    filterOptions,
    questionGroups,
    loading,
    filtersLoading,
    apiLoading,
    apiError,
    filterError,
    hasAppliedFilters,
    setFilterError,
    setHasAppliedFilters
  };
};