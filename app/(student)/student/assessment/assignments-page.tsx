import React, { useEffect, useState } from 'react'
import { Button, Spin, Table, Tag } from "antd";
import { ExamData } from './data/exam-data'
import { useRouter } from 'next/navigation';
import { QuizData } from './data/quiz-data';
import { useApi } from '@/hooks/useRequest';
import { assignmentColumns, AssignmentData } from './data/assignment-data';
import TableTemplate from '@/components/ui/table-template';

export default function AssignmentsPage() {
  

  const router = useRouter();

  const [assignments, setAssignments] = useState<AssignmentData[]>([])
  const [loading, setLoading] = useState(true)
  const { request } = useApi()
  
       

  useEffect(() => {
    // Fetch ongoing quizzes from the API
    const fetchQuizzes = async () => {
      try {
        const response = await request("GET", `/assignment/user`)
         if(response.data.data.assignments){
          setAssignments(response.data.data.assignments)
        }
      } catch (error) {
        console.error('Error fetching exams:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchQuizzes()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
      <div className="bg-white rounded-lg shadow overflow-x-scroll w-full">
      <TableTemplate
      handleRowClick={(data: any)=> {
         router.push(`/student/active-assessment/assignment/${data.assignment_document_id}`)
      }}
      data={assignments} columns={assignmentColumns}  />
        </div>
  )
}
