import React, { useEffect, useState } from 'react'
import { Button, Spin, Table } from "antd";
import { TestData, testColumns } from './data/test-data';
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useRequest';
import { useStudentStore } from '@/store/studentStore';
import TableTemplate from '@/components/ui/table-template';

export default function TestsPage() {
  const router = useRouter();

  const [tests, setTests] = useState<TestData[]>([])
  const [loading, setLoading] = useState(true)
  const { request } = useApi()
  const { student, setStudent, clearStudent } = useStudentStore();

  useEffect(() => {
    // Fetch ongoing tests from the API
    const fetchTests = async () => {
      try {
        const response = await request("GET", `/test/user`)
        console.log('response', response.data.data.tests)
        if (response.data.data.tests) {
          setTests(response.data.data.tests)
        }
      } catch (error) {
        console.error('Error fetching tests:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTests()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (

      <div className="bg-white rounded-lg shadow overflow-x-scroll w-full">
      <TableTemplate 
      handleRowClick={(data: any)=> {
        console.log('test data', data)
         router.push(`/student/active-assessment/test/${data.content_document_id}`)
      }}
      
      data={tests} columns={testColumns}  />
       
     
    </div>
  )
}