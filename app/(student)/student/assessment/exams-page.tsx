import CourseCard from '@/components/ui/course-template'
import React, { useEffect, useState } from 'react'
import LearningCard from './widgets/learning-card'
import { AppstoreOutlined } from '@ant-design/icons'
import TableTemplate from '@/components/ui/table-template'
import { examsColumns, resultsColumns } from './data/exam-data'
import { useApi } from '@/hooks/useRequest'
import { Spin } from 'antd'
import { ExamData } from './data/exam-data'
import { useRouter } from 'next/navigation'

export default function ExamsPage() {
  const [exams, setExams] = useState<ExamData[]>([])
  const [loading, setLoading] = useState(true)
  const { request } = useApi()
    const router = useRouter();

  useEffect(() => {
    const fetchExams = async () => {
      try {
        const response = await request("GET", "/exam/active-exams")
        console.log('respee',response.data.data.exams)
        if(response.data.data.exams){
          setExams(response.data.data.exams)
        }
      } catch (error) {
        console.error('Error fetching exams:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchExams()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
        <div className='bg-white rounded-lg shadow overflow-x-scroll w-full'>
           
            <TableTemplate handleRowClick={(data: any)=> {
         router.push(`/student/active-assessment/exam/${data.questions_document_id}`)
        console.log('assessment', data)
      }}
       data={exams} columns={examsColumns}  />
          </div>
  )
}
