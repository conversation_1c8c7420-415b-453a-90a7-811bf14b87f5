import React, { useEffect, useState } from 'react'
import { Button, Spin, Table } from "antd";
import { ExamData } from './data/exam-data'
import { useRouter } from 'next/navigation';
import { useApi } from '@/hooks/useRequest';
import { useStudentStore } from '@/store/studentStore';
import { quizColumns, QuizData } from './data/quiz-data';
import TableTemplate from '@/components/ui/table-template';

export default function QuizzesPage() {
  const router = useRouter();

  const [quizzes, setQuizzes] = useState<QuizData[]>([])
  const [loading, setLoading] = useState(true)
  const { request } = useApi()
     const { student, setStudent, clearStudent } = useStudentStore();
       

  useEffect(() => {
    // Fetch ongoing quizzes from the API
    const fetchQuizzes = async () => {
      try {
        const response = await request("GET", `/quiz/student/${student?.id}/quizzes`)
        console.log('respee',response.data.data.quizes)
        if(response.data.data.quizes){
          setQuizzes(response.data.data.quizes)
        }
      } catch (error) {
        console.error('Error fetching exams:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchQuizzes()
  }, [])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
     <div className="bg-white rounded-lg shadow overflow-x-scroll w-full">
      <TableTemplate
      handleRowClick={(data: any)=> {
         router.push(`/student/active-assessment/quiz/${data.id}`)
      }}
      
      data={quizzes} columns={quizColumns}  />
     </div>
  )
}
