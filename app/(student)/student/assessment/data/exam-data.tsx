import { ColumnsType } from "antd/es/table";

export interface ExamData {
    id: string;
    name: string;
    course_id: string;
    teacher_id: string;
    total_marks: number;
    duration: number;
    pass_marks: number;
    status: string;
    start_datetime: string;
    end_datetime: string;
    completed: boolean;
    is_published: boolean;
    published: boolean;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}



export const examsColumns: ColumnsType<ExamData> = [
    {
        title: 'Exam Name',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                status === 'ongoing' ? 'bg-green-100 text-green-800' :
                'bg-gray-100 text-gray-800'
            }`}>
                {status}
            </span>
        )
    },
    {
        title: 'Duration (mins)',
        dataIndex: 'duration',
        key: 'duration',
    },
    {
        title: 'Total Marks',
        dataIndex: 'total_marks',
        key: 'total_marks',
    },
    {
        title: 'Pass Marks',
        dataIndex: 'pass_marks',
        key: 'pass_marks',
    },
    {
        title: 'Start Time',
        dataIndex: 'start_datetime',
        key: 'start_datetime',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },
    {
        title: 'End Time',
        dataIndex: 'end_datetime',
        key: 'end_datetime',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    }
];

export const resultsColumns: ColumnsType<ExamData> = [
    {
        title: 'Exam Name',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                status === 'completed' ? 'bg-green-100 text-green-800' :
                status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
            }`}>
                {status}
            </span>
        )
    },
    {
        title: 'Score',
        dataIndex: 'total_marks',
        key: 'total_marks',
        render: (marks: number) => `${marks}/100`
    },
    {
        title: 'Pass/Fail',
        dataIndex: 'pass_marks',
        key: 'pass_marks',
        render: (passMarks: number, record: ExamData) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                record.total_marks >= passMarks ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
                {record.total_marks >= passMarks ? 'Pass' : 'Fail'}
            </span>
        )
    },
    {
        title: 'Date Taken',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (date: string) => new Date(date).toLocaleString()
    }
];

// Generate random exam data
const generateRandomExam = (index: number): ExamData => {
    const statuses = ['upcoming', 'ongoing', 'completed'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const now = new Date();
    const startDate = new Date(now.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000); // Random date within next 7 days
    const endDate = new Date(startDate.getTime() + 120 * 60 * 1000); // 120 minutes after start

    return {
        id: `exam-${index}`,
        name: `Exam ${index + 1}`,
        course_id: `course-${Math.floor(Math.random() * 5)}`,
        teacher_id: `teacher-${Math.floor(Math.random() * 3)}`,
        total_marks: Math.floor(Math.random() * 100),
        duration: 120,
        pass_marks: 50,
        status: randomStatus,
        start_datetime: startDate.toISOString(),
        end_datetime: endDate.toISOString(),
        completed: randomStatus === 'completed',
        is_published: Math.random() > 0.5,
        published: Math.random() > 0.5,
        created_at: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 30 days
        updated_at: new Date().toISOString(),
        deleted_at: null
    };
};

export const examsData: ExamData[] = Array.from({ length: 10 }, (_, i) => generateRandomExam(i));