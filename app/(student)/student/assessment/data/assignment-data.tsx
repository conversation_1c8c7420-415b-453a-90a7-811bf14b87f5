import { ColumnsType } from "antd/es/table";

export interface AssignmentData {
    id: string;
    title: string;
    course_id: string;
    teacher_id: string;
    total_points: number;
    status: string;
    due_date: string;
    is_published: boolean;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    description: string;
    level: string;
    instructions: string | null;
    assignment_id: string | null;
    assignment_metadata: Record<string, any>;
}

export const assignmentColumns: ColumnsType<AssignmentData> = [
    {
        title: 'Assignment Title',
        dataIndex: 'title',
        key: 'title',
    },
    {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
    },
    {
        title: 'Level',
        dataIndex: 'level',
        key: 'level',
        render: (level: string) => (
            <span className={` capitalize`}>
                {level}
            </span>
        )
    },
    {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                status === 'completed' ? 'bg-green-100 text-green-800' :
                status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
            } capitalize`}>
                {status}
            </span>
        )
    },
    {
        title: 'Total Points',
        dataIndex: 'total_points',
        key: 'total_points',
    },
    {
        title: 'Due Date',
        dataIndex: 'due_date',
        key: 'due_date',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },
    {
        title: 'Created At',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    }
];