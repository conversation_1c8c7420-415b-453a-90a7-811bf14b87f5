import { ColumnsType } from "antd/es/table";


export interface QuizData {
    id: string;
    name: string;
    course_id: string;
    teacher_id: string;
    total_marks: number;
    duration: number;
    pass_marks: number;
    status: string;
    start_datetime: string;
    end_datetime: string;
    completed: boolean;
    is_published: boolean;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    description: string;
    level: string;
    questions_document_id: string;
}



export const quizColumns: ColumnsType<QuizData> = [
    {
        title: 'Quiz Name',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
    },
    {
        title: 'Level',
        dataIndex: 'level',
        key: 'level',
        render: (status: string) => (
            <span className={`capitalize`}>
                {status}
            </span>
        )
    },
    {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                status === 'completed' ? 'bg-green-100 text-green-800' :
                status === 'ongoing' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
            } capitalize`}>
                {status}
            </span>
        )
    },
    {
        title: 'Duration (mins)',
        dataIndex: 'duration',
        key: 'duration',
    },
    {
        title: 'Total Marks',
        dataIndex: 'total_marks',
        key: 'total_marks',
    },
    {
        title: 'Pass Marks',
        dataIndex: 'pass_marks',
        key: 'pass_marks',
    },
    {
        title: 'Start Time',
        dataIndex: 'start_datetime',
        key: 'start_datetime',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },
    {
        title: 'End Time',
        dataIndex: 'end_datetime',
        key: 'end_datetime',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },
  
];
