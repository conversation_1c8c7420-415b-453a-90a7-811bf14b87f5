import { ColumnsType } from "antd/es/table";

export interface TestData {
    id: string;
    title: string;
    course_id: string;
    teacher_id: string;
    duration_minutes: number;
    status: string;
    scheduled_date: string;
    due_date: string;
    is_published: boolean;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    description: string;
    level: string;
    content_document_id: string;
}

export const testColumns: ColumnsType<TestData> = [
    {
        title: 'Test Title',
        dataIndex: 'title',
        key: 'title',
    },
    {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
    },
    {
        title: 'Level',
        dataIndex: 'level',
        key: 'level',
    },
    {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                status === 'completed' ? 'bg-green-100 text-green-800' :
                status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
            } capitalize`}>
                {status}
            </span>
        )
    },
    {
        title: 'Duration (mins)',
        dataIndex: 'duration_minutes',
        key: 'duration_minutes',
    },
    {
        title: 'Scheduled Date',
        dataIndex: 'scheduled_date',
        key: 'scheduled_date',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },
    {
        title: 'Due Date',
        dataIndex: 'due_date',
        key: 'due_date',
        render: (date: string) => {
            const formattedDate = new Date(date);
            return formattedDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    }
];