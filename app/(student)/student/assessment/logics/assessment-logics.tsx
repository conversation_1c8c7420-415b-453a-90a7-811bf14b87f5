import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form, Spin } from "antd";
import React from "react";
import { useState } from "react"
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { UserOverviewLogics } from "@/logics/student-overview";

export const AssessmentLogics = () => {
    const router = useRouter();
    const { request } = useApi();
    const { showNotification, destroyNotifications } = useNotification();
    const [authForm] = Form.useForm();
    const [loading, setLoading] = useState(true)
    const [assessmentResults, setAssessmentResults] = useState<any>()


    //Create User
    async function submitAssessment(assessmentID: string, assessmentType: string, questions: any) {
        try {
            showNotification('success', 'Submitting', '', true, <Spin />);
            setLoading(true)
            const requestResponse = await request("POST", `/submission/submit?assessment_id=${assessmentID}&assessment_type=${assessmentType}`,
                JSON.stringify(
                    questions), "");
            console.log(requestResponse, 'mainData');
            destroyNotifications();
            if (requestResponse && requestResponse?.status <= 204) {
                showNotification('success', 'Success', requestResponse.data.message);
                setAssessmentResults(requestResponse.data);
                authForm.resetFields(); // Clears form when component unmounts
                setLoading(false)
            } else {
                showNotification('error', 'Error submitting assessment', requestResponse.data.detail);
                setLoading(false)
            }




        } catch (error) {

        }
    }




    return {
        submitAssessment, loading, assessmentResults


    }
}
