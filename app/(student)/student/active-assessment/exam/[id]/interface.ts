interface Option {
  key: string;
  text: string;
}

interface Question {
  question: string;
  options: Option[];
  answers: string[];
  point: number;
  question_type: 'multiple_choice' | 'short_answer' | string;
  explanation: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface Quiz {
  id: string;
  name: string;
  scheduled_datetime: string | null;
  level: 'beginner' | 'intermediate' | 'advanced' | string;
  is_published: boolean;
  title: string;
  total_marks: number;
  questions_document_id: string;
  pass_marks: number;
  created_at: string;
  updated_at: string;
  description: string;
  duration: number;
  course_id: string;
  status: 'upcoming' | 'active' | 'completed' | string;
  deleted_at: string | null;
  teacher_id: string;
  start_datetime: string;
  end_datetime: string;
  questions: Question[];
}
