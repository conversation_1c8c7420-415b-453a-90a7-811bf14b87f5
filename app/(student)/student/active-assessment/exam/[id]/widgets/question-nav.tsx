import { LeftCircleOutlined, RightCircleOutlined } from "@ant-design/icons";

export const QuestionNavigation = ({ onPrevious, onNext }:any) => (
  <div className="flex justify-center p-4 gap-4">
    <button onClick={onPrevious} className="btn bg-stone-500 p-1 w-[7rem] flex justify-between rounded-md text-white px-2"> <LeftCircleOutlined /> Previous </button>
   <button onClick={onNext} className="btn bg-primaryColor p-1 w-[7rem]  flex justify-between rounded-md text-white px-2 ">Next <RightCircleOutlined /></button>
  </div>
);
