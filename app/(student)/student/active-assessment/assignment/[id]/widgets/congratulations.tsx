import { AssessmentLogics } from "@/app/(student)/student/assessment/logics/assessment-logics";
import ButtonTemplate from "@/components/ui/button-template";
import { RightCircleOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { div } from "framer-motion/client";
import { useRouter } from "next/navigation";

const Confetti = () => (
    <div className="absolute inset-0 pointer-events-none z-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
            <motion.div
                key={i}
                className="absolute w-2 h-5 rounded bg-gradient-to-br from-purple-500 to-pink-400"
                initial={{ opacity: 1, x: Math.random() * 1400, y: -100, rotate: 0 }}
                animate={{ y: "100vh", rotate: 360 }}
                transition={{
                    duration: 2 + Math.random() * 2,
                    repeat: Infinity,
                    ease: "easeIn",
                    delay: i * 0.1,
                }}
            />
        ))}
    </div>
);

export default function ResultScreen({ courseName = "", description = "", total = 30 }) {

      const router = useRouter();
    return (
        <div className="relative h-screen w-full bg-white flex flex-col  items-center">
            <Confetti />
            <div className=" h-max my-8  mt-32 z-10 max-w-2xl text-center bg-[#E2EDED] p-10 rounded-xl shadow-xl border border-[#B6D8D9]">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                    ASSIGNMENT SUBMITTED
                </h1>
                {/* <p className="text-xl text-teal-600 font-bold mt-4">
                    YOU SCORED {score}/{total}
                </p> */}

            </div>
            <div className="flex flex-col items-center">
                <p className="text-gray-700 mb-2">
                   {courseName}
                </p>
                <p className="text-gray-600">
                    {description}
                </p>
                <ButtonTemplate onClick={()=>{
                     router.push('/student/assessment')
                }} label={"Close"} className="mt-4 bg-primaryColor text-white"  />
            </div>
        </div>
    );
}
