'use client';

import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "./widgets/header";
import { Question } from "./widgets/question";
import { QuestionNavigation } from "./widgets/question-nav";
import { Sidebar } from "./widgets/sidebar";
import ResultScreen from "./widgets/congratulations";
import { useApi } from "@/hooks/useRequest";
import { Spin } from "antd";
import { useParams } from "next/navigation";
import { AssessmentLogics } from "../../../assessment/logics/assessment-logics";

export default function App() {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<any>({});
  const [quiz, setQuiz] = useState<Quiz>();
  const [loading, setLoading] = useState(true);
  const { request } = useApi();
  const params = useParams();
  const id = params?.id;
  const {submitAssessment, loading:submittingAssessment,assessmentResults} = AssessmentLogics()

  useEffect(() => {
    const fetchQuiz = async () => {
      try {
        const response = await request("GET", `/quiz/${id}`);
        if (response.data.data?.questions) {
          setQuiz(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching quiz:", error);
      } finally {
        setLoading(false);
      }
    };

    if (id) fetchQuiz();
  }, [id]);

  const handleSelect = (option: any) => {
    const current = quiz?.questions[currentQuestion];
    const structuredAnswer = {
      question: current?.question,
      answers: current?.question_type !== "multiple_choice" ? [option] :
      
      option,
      question_type: current?.question_type,
    };

    setAnswers((prev: any) => ({
      ...prev,
      [currentQuestion]: structuredAnswer,
    }));
  };

  const formatAnswers = (answersObj: any) => {
  const formatted = Object.values(answersObj);
  return { answers: formatted };
};




  const [assessment, setAssessment] = useState("inProgress");
   
   
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      {assessment === "done" ? (
     submittingAssessment? <div className="text-black text-center flex flex-col items-center h-full justify-center mt-[10%]">Loading</div>:   <ResultScreen
     description={assessmentResults?.message}
     />
      ) : (
        <div className="flex min-h-screen gap-4 p-4">
          <div className="flex-1 bg-[#E2EDED] h-[40rem] pb-8 overflow-y-scroll">
            <Header />
            <Question
              index={quiz?.questions}
              question={quiz?.questions[currentQuestion]?.question}
              options={quiz?.questions[currentQuestion]?.options}
              selectedOption={answers[currentQuestion]}
              onSelect={handleSelect}
              type={quiz?.questions[currentQuestion]?.question_type}
            />
               {/* <pre className="!text-black">{JSON.stringify(answers, null, 2)}</pre> */}

            <QuestionNavigation
              onPrevious={() =>
                setCurrentQuestion((q) => Math.max(q - 1, 0))
              }
              onNext={() =>
                setCurrentQuestion((q) =>
                  Math.min(q + 1, (quiz?.questions.length ?? 1) - 1)
                )
              }
            />
          </div>
          <Sidebar
            completeAssessment={() => {
              const cleanedAnswers = formatAnswers(answers);
console.log(JSON.stringify(cleanedAnswers, null, 2));
              setAssessment("done");
            submitAssessment(quiz?.questions_document_id??'' ,'quiz', cleanedAnswers)
            }}
            totalQuestions={quiz?.questions.length}
            answeredQuestions={Object.keys(answers).map(Number)}
            onSelectQuestion={(q: number) => setCurrentQuestion(q-1)}
          />
        </div>
      )}
    </div>
  );
}
