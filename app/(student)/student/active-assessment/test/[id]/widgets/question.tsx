export const Question = ({
  question,
  options = [],
  selectedOption,
  onSelect,
  index,
  type = "single",
}: any) => {
  const currentIndex = index.findIndex(
    (item: any) => item.question === question
  ) + 1;

  const handleMultiSelect = (opt: string) => {
    const currentAnswers = selectedOption?.answers || [];
    if (currentAnswers.includes(opt)) {
      onSelect(currentAnswers.filter((item: string) => item !== opt));
    } else {
      onSelect([...currentAnswers, opt]);
    }
  };

  return (
    <div className="bg-[#E2EDED] text-black">
      <h3 className="text-xl p-3 bg-[#83B7B8] font-semibold mb-2">
        Question {currentIndex}/{index.length}
      </h3>
      <div className="pb-10 p-2">
        <h3 className="text-sm my-2">{question}</h3>

        {(type === "single_choice" || type === "multiple_choice") && (
          <ul className="space-y-2">
            {options.map((opt: any, idx: number) => (
              <li key={idx} className="flex items-center">
                <input
                  type={type === "single_choice" ? "radio" : "checkbox"}
                  name={`question-${currentIndex}`}
                  value={opt.text}
                  checked={
                    type === "single_choice"
                      ? selectedOption?.answers[0] === opt.text
                      :
                      selectedOption === undefined ? false :
                        selectedOption?.answers?.includes(opt.text)
                  }
                  onChange={() =>
                    type === "single_choice"
                      ? onSelect(opt.text)
                      : handleMultiSelect(opt.text)
                  }
                  className="mr-2"
                />
                <label>{opt.text}</label>
              </li>
            ))}
          </ul>
        )}

        {type === "short_answer" && (
          <textarea
            className="w-full mt-2 p-2 border border-gray-300 rounded"
            rows={4}
            value={selectedOption?.answers?.[0] || ""}
            onChange={(e) => onSelect(e.target.value)}
            placeholder="Type your answer here..."
          />
        )}
      </div>
    </div>
  );
};
