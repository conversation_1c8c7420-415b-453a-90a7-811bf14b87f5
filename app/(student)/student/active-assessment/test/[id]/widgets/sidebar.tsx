import { CheckCircleFilled, CheckCircleOutlined } from "@ant-design/icons";

export const Sidebar = ({ totalQuestions, answeredQuestions, onSelectQuestion, completeAssessment }: any) => (
  <div className="bg-teal-700 text-white p-4 h-[38rem] flex flex-col justify-between  w-1/4">
    <div>
      <h4 className="text-xl font-bold mb-4">Question Numbers</h4>
      <div className="grid grid-cols-5 gap-2">
        {Array.from({ length: totalQuestions }).map((_, i) => {
          const isAnswered = answeredQuestions.includes(i );
          return (
            <button
              key={i}
              onClick={() => onSelectQuestion(i + 1)}
              className={`p-2 rounded ${isAnswered ? "bg-white text-teal-700" : "bg-gray-800"
                }`}
            >
              {i + 1}
            </button>
          );
        })}
      </div>
    
    </div>
    {/* Legend */}
    <div>
        <div className="my-8">
        <p>Total answered question are: {answeredQuestions.length}/{totalQuestions}</p>
        <button onClick={()=>{completeAssessment()}} className="btn bg-white text-teal-800 p-1 rounded mt-2">Submit</button>
      </div>
    <div className="flex justify-between">
      <div className="flex items-center gap-1">
        <div className="h-8 w-8 bg-white rounded flex items-center justify-center">
          <CheckCircleOutlined className="text-black text-lg" />
        </div>
        <div>Answered</div>
      </div>
      <div className="flex items-center gap-1">
        <div className="h-8 w-8 bg-gray-800 rounded flex items-center justify-center">
          <CheckCircleOutlined className="text-white text-lg" />
        </div>
        <div>Not Answered</div>
      </div>
    </div>
    </div>
  </div>
);
