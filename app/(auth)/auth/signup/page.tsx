"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  MailOutlined,
  LockOutlined,
  UserOutlined,
  MobileOutlined,
  FundProjectionScreenOutlined
} from "@ant-design/icons";
import {
  Form,
  FormProps,
  ThemeConfig,
} from "antd";
import RadioTemplate from "@/components/ui/radio-template";
import InputTemplate from "@/components/ui/input-template";
import ButtonTemplate from "@/components/ui/button-template";
import SelectTemplate from "@/components/ui/select-template";
import { AuthLogics } from "../_logics/auth-logics";
import {  userTypeList } from "../_data/auth-data";
import { useFetch } from "@/hooks/useFetch";
import { SchoolType } from "@/types";
import { useEffect, useState } from "react";


const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};

function SignUpStudent() {
  const [schoolList, setSchoolList] = useState<SchoolType[]>([])
  const router = useRouter();
  const {createUser, loading, authForm} = AuthLogics()
  const {data, loading:fetchingSchools} = useFetch('/unprotected/school/all')
  useEffect(()=>{
    console.log('school', data?.data?.schools)
    setSchoolList(data?.data?.schools)
  }, [data])
  
   
  

  

  const onFinish: FormProps<any>['onFinish'] = (info) => {
    // Create A new User
    createUser(info);
   
    
  };

  

  return (
    <>
 
    <div className="bg-[#f4f8f7]  rounded-xl  ">
      <div className="flex flex-row    ">
        <div className="hidden md:block ">
          <Image className="mt-40" src="/signup.png" alt="Image1" width={400} height={10} />
        </div>

        <div className=" mr-3 flex flex-1 px-4 flex-col mt-12 ">
          <div className="flex justify-center">
            <Image className="shadow-xl rounded-full" src="/logo.png" alt="logo" width={70} height={70} />
          </div>
          <Form
            name="signUpForm"
            onFinish={onFinish}
            form={authForm}
           >
          <div className="flex justify-center my-12">
              <RadioTemplate defaultValue={userTypeList[0].value} fieldName="role" options={userTypeList} />
            </div>
          
            
            <div className="flex justify-center gap-x-10 px-20">

              <div className="w-1/2">
                <InputTemplate fieldName="first_name" prefix={<UserOutlined />} label={"First name"} placeHolder="John" className="" />
                <InputTemplate fieldName="last_name" prefix={<UserOutlined />} placeHolder="Doe" label={"Last name"} className="" />
                {/* <InputTemplate  label={"School"} className="" /> */}
                <SelectTemplate
                fieldName="school_id"
               fieldNames={{label:'name', value: 'id'}}
                  options={schoolList && schoolList}
                  label={"School"} className="" placeHolder={'University of Ghana'} prefix={<FundProjectionScreenOutlined />} />

              </div>
              <div className="w-1/2">
                <InputTemplate inputType="email" fieldName="email" prefix={<MailOutlined />} placeHolder="<EMAIL>" label={"Email"} className="" />
                <InputTemplate fieldName="phone" prefix={<MobileOutlined />} placeHolder="**********" label={"Phone"} className="" />
                <InputTemplate fieldName="password" inputType="password" prefix={<LockOutlined />} placeHolder="Enter your password" label={"Password"} className="" />
              </div>

            </div>


            <div className="flex justify-center mt-5">
            <Form.Item label={null}>
              <ButtonTemplate
                htmlType="submit"
                label={loading? 'Loading...':'Create Account'} className={`bg-transparent !text-base text-primaryColor font-semibold h-10 w-[16rem] ${loading? '!bg-primaryColor !text-white ': ''}:`} />
                </Form.Item>
            </div>
          </Form>
          <span className="flex text-xs font-semibold mt-5 justify-center"> <p className="flex justify-center  text-gray-400 mr-1">Already have an account?</p> <p onClick={() => { router.push('/auth/signin') }} className="text-primaryColor hover:underline hover:cursor-pointer ">Sign in</p></span>
          <p className="flex justify-center mt-2 text-gray-400 text-xs font-semibold">-or-</p>
          <div className="flex flex-row justify-center items-center mt-5">
            <Image
              src="/google.png"
              alt={"google"}
              width={30}
              height={5}
              className="mr-7"
            />
            <Image
              src="/faceb.png"
              alt={"facebook"}
              width={20}
              height={10}
              className="mr-7"
            />
            <Image
              src="/apple.png"
              alt={"apple"}
              width={30}
              height={5}
              className="mr-7"
            />
          </div>

        </div>
      </div>
    </div>
    </>
  );
}

export default SignUpStudent;