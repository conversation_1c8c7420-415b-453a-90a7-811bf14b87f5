"use client";
import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  LockOutlined} from "@ant-design/icons";
import {
  ThemeConfig,
} from "antd";
import InputTemplate from "@/components/ui/input-template";
import ButtonTemplate from "@/components/ui/button-template";


const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};

const allSchools = ["ucc", "atu", "vvu", "uew"];
function ForgotPassword() {
  const router = useRouter();



  




  return (
    <div className="bg-[#f4f8f7]  rounded-xl  ">
      <div className="flex flex-row    ">
        <div className="hidden md:block ">
          <Image className="mt-40" src="/signup.png" alt="Image1" width={400} height={10} />
        </div>

        <div className="mt-5 mr-3 flex flex-1 px-4 flex-col ">
          <div className="flex justify-center mt-12">
            <Image className="shadow-xl rounded-full" src="/logo.png" alt="logo" width={70} height={70} />
          </div>
          <div className="flex justify-center mt-12">
            <div className="font-bold text-2xl text-primaryColor">
             Create new password
            </div>
          </div>
          <div className="flex justify-center text-xs font-semibold text-black mt-4 mb-12">
          Your new password must be different from previously used passwords
          </div>
          <div className="flex justify-center gap-x-10 px-20">

            <div className="w-1/2">
                 <InputTemplate fieldName="current-password" inputType="password" prefix={<LockOutlined />} placeHolder="Enter your current password" label={"Current password"} className="" />
                 <InputTemplate  fieldName="new-password" inputType="password" prefix={<LockOutlined />} placeHolder="Enter your new password" label={"New password"} className="" />
                 <InputTemplate  fieldName="confirmed-password" inputType="password" prefix={<LockOutlined />} placeHolder="Enter your confirmed password" label={"Confirm password"} className="" />
                     
            </div>

          </div>
          <div className="justify-center flex px-20">
          </div>
          <div className="flex justify-center mt-10">
            <ButtonTemplate
             handleClick={()=>{ router.push('/auth/signin')
             }}
            label='Submit' className="bg-transparent !text-base text-primaryColor font-semibold h-10 w-[16rem]" />
          </div>
         

        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
