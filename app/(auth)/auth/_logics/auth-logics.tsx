import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form, Spin } from "antd";
import React from "react";
import { useState } from "react"
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { UserOverviewLogics } from "@/logics/student-overview";

export const AuthLogics = () => {
  const router = useRouter();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [authForm] = Form.useForm();
  const { setUpMe } = UserOverviewLogics()


  //Create User
  async function createUser(userData: any) {
    try {
      console.log('data', userData);
      showNotification('success', 'Registering User', 'Creating your account...', true, <Spin />);

      setLoading(true);
      const requestResponse = await request("POST", "/auth/register",
        JSON.stringify(
          {
            ...userData,
          }), "");
      console.log(requestResponse, 'mainData');
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status <= 204) {
        showNotification('success', 'Success', requestResponse.data.message);
        authForm.resetFields(); // Clears form when component unmounts
        router.push('/auth/signin')
      } else {
        showNotification('error', 'Error creating account', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }

  //Login User
  async function loginUser(userData: any) {
    try {
      console.log('data', userData);
      showNotification('success', 'Signing In', 'Signing into your account...', true, <Spin />);

      setLoading(true);
      const requestResponse: any = await request("POST", "/auth/login", {
        ...userData,
      }, "multipart/form-data");
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', 'Signed in successfully');

        Cookies.set("access_token", requestResponse.data.data.access_token);
        Cookies.set("refresh_token", requestResponse.data.data.refresh_token);
        // router.push("/student/profile");
        // window.location.href = "/student/profile"; // Redirect to login page

        getMe()


      } else {
        showNotification('error', 'Error signing in', requestResponse.data.detail);

      }



    } catch (error) {

    }
  }


  async function getMe() {
    try {
      // showNotification('success', 'Signing In', 'Signing into your account...', true, <Spin />);
      console.log('getting cart details')
      setLoading(true);
      const requestResponse: any = await request("GET", "/protected/me", null, "multipart/form-data",

      );
      setLoading(false);
      // destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        setUpMe(requestResponse, true)

        // return requestResponse?.data?.data?.courses;

      } else {
        showNotification('error', 'Error searching courses', requestResponse.data.detail);

      }



    } catch (error) {
      console.log('got not me', error)


    }
  }

  //Create User
  async function forgotPassword(userData: any) {
    try {
      console.log('data', userData);
      showNotification('success', 'Resetting Password', 'Submitting your email...', true, <Spin />);

      setLoading(true);
      const requestResponse = await request("POST", "/auth/forgot-password",
        JSON.stringify(
          {
            ...userData,
          }), "");
      console.log(requestResponse, 'mainData');
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
      } else {
        showNotification('error', 'Error resetting password', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }

  // Renew token
  async function refreshToken() {
    try {
      const refreshToken = Cookies.get('refresh_token')



      const requestResponse = await request("POST", "/auth/renew_token",
        JSON.stringify(
          {
            "refresh_token": refreshToken
          }), "");
      console.log(requestResponse, 'mainData');
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', 'Signed in successfully');

        Cookies.set("access_token", requestResponse.data.data.access_token);
        Cookies.set("refresh_token", requestResponse.data.data.refresh_token);
        // router.push("/student/profile");
        // window.location.href = "/student/profile"; // Redirect to login page

        getMe()


      }





    } catch (error) {

    }
  }



  return {
    createUser, loading, loginUser, forgotPassword, authForm, refreshToken

  }
}
