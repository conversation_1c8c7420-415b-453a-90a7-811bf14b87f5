'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Tabs, 
  Card, 
  Avatar, 
  Button, 
  Form, 
  Input, 
  Carousel, 
  Rate, 
  Tag,
  Select,
  message 
} from 'antd';
import { 
  UserOutlined, 
  BookOutlined, 
  FireOutlined,
  StarOutlined,
  MessageOutlined,
  ShareAltOutlined,
  SearchOutlined
} from '@ant-design/icons';
import Image from 'next/image';
import Layout from '@/components/general/indexPagelayout/layout';
const { TextArea } = Input;
const { Option } = Select;
import '../../styles/globals.css'; 
import InputTemplate from '@/components/ui/input-template';
// Comprehensive Interfaces
interface Course {
  id: number;
  title: string;
  instructor: string;
  rating: number;
  students: number;
  price: number;
  category: string;
  subCategory: string;
  coverImage: string;
  shortDescription: string;
  fullDescription: string;
  learningOutcomes: string[];
  prerequisites: string[];
  duration: string;
  certificateProvided: boolean;
}

interface CourseReview {
  id: number;
  userName: string;
  userAvatar: string;
  courseId: number;
  rating: number;
  content: string;
  timestamp: string;
}

interface CommunityFeature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface Testimonial {
  name: string;
  role: string;
  quote: string;
  avatar: string;
}

const LearnKonnectCommunity: React.FC = () => {
  const [reviews, setReviews] = useState<CourseReview[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [form] = Form.useForm();

  // Comprehensive Trending Courses
  const trendingCourses: Course[] = [
    {
      id: 1,
      title: "African Digital Entrepreneurship Masterclass",
      instructor: "Dr. Amina Diallo",
      rating: 4.7,
      students: 5234,
      price: 49.99,
      category: "Business",
      subCategory: "Digital Entrepreneurship",
      coverImage: "/entrepreneurship.jpg",
      shortDescription: "Learn cutting-edge digital strategies for African entrepreneurs",
      fullDescription: "A comprehensive program designed to equip aspiring entrepreneurs with digital skills, business strategy, and innovative thinking tailored to the African market landscape.",
      learningOutcomes: [
        "Develop a digital business strategy",
        "Create sustainable digital business models",
        "Understand digital marketing for African markets"
      ],
      prerequisites: [
        "Basic computer skills",
        "Passion for entrepreneurship",
        "No prior business experience required"
      ],
      duration: "6 weeks",
      certificateProvided: true
    },
    {
      id: 2,
      title: "Data Science for Social Impact",
      instructor: "Prof. Kwame Osei",
      rating: 4.9,
      students: 7123,
      price: 79.99,
      category: "Technology",
      subCategory: "Data Science",
      coverImage: "/data-science.jpeg",
      shortDescription: "Leverage data to solve critical challenges in African communities",
      fullDescription: "An intensive program teaching data science techniques specifically applied to solving social and economic challenges across African contexts.",
      learningOutcomes: [
        "Advanced data analysis techniques",
        "Create impactful data visualizations",
        "Apply machine learning to social problems"
      ],
      prerequisites: [
        "Basic programming knowledge",
        "High school mathematics",
        "Passion for social change"
      ],
      duration: "8 weeks",
      certificateProvided: true
    },
    {
      id: 3,
      title: "Sustainable Agriculture Innovation",
      instructor: "Dr. Fatima Zara",
      rating: 4.6,
      students: 4567,
      price: 59.99,
      category: "Agriculture",
      subCategory: "Sustainable Farming",
      coverImage: "/agriculture.jpeg",
      shortDescription: "Innovative approaches to sustainable farming in Africa",
      fullDescription: "A comprehensive course exploring modern agricultural technologies, sustainable farming practices, and innovative solutions for African agricultural challenges.",
      learningOutcomes: [
        "Understand sustainable farming techniques",
        "Learn agricultural technology applications",
        "Develop innovative agricultural solutions"
      ],
      prerequisites: [
        "Interest in agriculture",
        "No prior farming experience required",
        "Passion for sustainable development"
      ],
      duration: "5 weeks",
      certificateProvided: true
    }
  ];

  // Community Features
  const communityFeatures: CommunityFeature[] = [
    {
      icon: <BookOutlined className="text-4xl text-teal-700" />,
      title: "Diverse Learning Resources",
      description: "Access a wide range of courses tailored to African contexts and global standards."
    },
    {
      icon: <StarOutlined className="text-4xl text-teal-700" />,
      title: "Expert-Led Courses",
      description: "Learn from industry leaders and academic experts across various disciplines."
    },
    {
      icon: <MessageOutlined className="text-4xl text-teal-700" />,
      title: "Community Interaction",
      description: "Engage with peers, share experiences, and build a supportive learning network."
    },
    {
      icon: <FireOutlined className="text-4xl text-teal-700" />,
      title: "Career Advancement",
      description: "Gain practical skills that directly translate to professional growth and opportunities."
    }
  ];

  // Testimonials
  const testimonials: Testimonial[] = [
    {
      name: "Aisha Mustapha",
      role: "Software Engineer, Nigeria",
      quote: "LearnKonnect transformed my career. The Data Science course opened doors I never thought possible!",
      avatar: "/studimg.jpg"
    },
    {
      name: "Kofi Mensah",
      role: "Agricultural Innovator, Ghana",
      quote: "The Sustainable Agriculture course gave me practical insights that are changing farming in my community.",
      avatar: "/studimg.jpg"
    },
    {
      name: "Fatima Zara",
      role: "Digital Entrepreneur, Morocco",
      quote: "Digital Entrepreneurship course was exactly what I needed to launch my startup.",
      avatar: "/studimg.jpg"
    }
  ];

  // Filtered Courses
  const filteredCourses = useMemo(() => {
    return trendingCourses.filter(course => 
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.subCategory.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  // Review Submission Handler
  const handleReviewSubmit = (values: any) => {
    if (!selectedCourse) {
      message.error('Please select a course');
      return;
    }

    const newReview: CourseReview = {
      id: reviews.length + 1,
      userName: "Student User",
      userAvatar: "/student2.jpg",
      courseId: selectedCourse,
      rating: values.rating,
      content: values.reviewContent,
      timestamp: new Date().toLocaleString()
    };

    setReviews([newReview, ...reviews]);
    form.resetFields();
    message.success('Your review has been shared!');
  };

  // Render Course Details
  const renderCourseDetails = (course: Course) => (
    <Card className="mt-4 bg-teal-50">
      <h3 className="text-2xl font-bold text-teal-700 mb-4">{course.title} - Detailed Overview</h3>
      <p className="text-gray-700 mb-4">{course.fullDescription}</p>
      
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-teal-600 mb-2">Learning Outcomes:</h4>
          <ul className="list-disc pl-5 text-gray-700 space-y-1">
            {course.learningOutcomes.map((outcome, index) => (
              <li key={index}>{outcome}</li>
            ))}
          </ul>
        </div>
        <div>
          <h4 className="font-semibold text-teal-600 mb-2">Prerequisites:</h4>
          <ul className="list-disc pl-5 text-gray-700 space-y-1">
            {course.prerequisites.map((prereq, index) => (
              <li key={index}>{prereq}</li>
            ))}
          </ul>
        </div>
      </div>
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div>
          <p className="font-bold text-teal-700">Duration</p>
          <p className="text-gray-700">{course.duration}</p>
        </div>
        <div>
          <p className="font-bold text-teal-700">Certificate</p>
          <p className="text-gray-700">{course.certificateProvided ? 'Provided' : 'Not Provided'}</p>
        </div>
        <div>
          <p className="font-bold text-teal-700">Price</p>
          <p className="text-gray-700">${course.price}</p>
        </div>
      </div>
    </Card>
  );

  return (
    <Layout>
    <div className="min-h-screen bg-gradient-to-br from-teal-50 to-teal-100">
      {/* Hero Section */}
      <motion.div 
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-teal-700 text-white text-center py-16 px-4"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          LearnKonnect Community
        </h1>
        <p className="text-xl max-w-2xl mx-auto text-teal-100">
          Empowering African Learners Through Innovative Education
        </p>
        <Button
                size="large"
                className="mt-6 bg-teal-700 text-white hover:bg-teal-800 border-teal-700"
            >
                Explore Courses
            </Button>
        
      </motion.div>

      {/* Community Features */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-teal-700 text-center mb-12">
          Why Join LearnKonnect?
        </h2>
        <div className="grid md:grid-cols-4 gap-6">
          {communityFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
              className="bg-white p-6 rounded-xl shadow-md text-center hover:shadow-lg transition-all"
            >
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-teal-700 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Trending Courses Carousel */}
      <div className="container mx-auto px-4 py-16 bg-white rounded-xl shadow-lg">
        <h2 className="text-3xl font-bold text-teal-700 text-center mb-12">
          Trending Courses
        </h2>
        
        <div className="flex items-center mb-6">
          <InputTemplate 
              prefix={<SearchOutlined />}
              placeHolder="Search courses by title, category"
              className="mr-4"
              onChange={(e) => setSearchTerm(e.target.value)} label={''} fieldName={''}          />
        </div>

        <Carousel 
          autoplay 
          dots={true}
          slidesToShow={3}
          responsive={[
            {
              breakpoint: 1024,
              settings: { slidesToShow: 2 }
            },
            {
              breakpoint: 600,
              settings: { slidesToShow: 1 }
            }
          ]}
        >
          {filteredCourses.map((course) => (
            <div key={course.id} className="p-4">
              <Card
                hoverable
                cover={
                  <Image 
                    src={course.coverImage} 
                    alt={course.title}
                    width={300}
                    height={200}
                    className="object-cover"
                  />
                }
              >
                <Card.Meta
                  title={
                    <div className="flex justify-between items-center">
                      <span>{course.title}</span>
                      <Tag color="green">{course.category}</Tag>
                    </div>
                  }
                  description={course.shortDescription}
                />
                <div className="mt-4 flex justify-between items-center">
                  <div>
                    <Rate disabled defaultValue={course.rating} />
                    <span className="ml-2 text-gray-500">
                      ({course.students} students)
                    </span>
                  </div>
                  <Button 
                    type="primary" 
                    className="bg-teal-700 hover:bg-teal-800"
                  >
                    Enroll Now
                  </Button>
                </div>
              </Card>
            </div>
          ))}
        </Carousel>
      </div>

      {/* Community Reviews Section */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-teal-700 text-center mb-12">
          Community Reviews
        </h2>
        
        <Form
          form={form}
          onFinish={handleReviewSubmit}
          className="max-w-2xl mx-auto mb-12 bg-teal-50 p-6 rounded-xl"
        >
          <Form.Item 
            name="courseSelection" 
            label="Select Course" 
            rules={[{ required: true, message: 'Please select a course' }]}
          >
            <Select
              showSearch
              style={{ width: '100%' }}
              placeholder="Select a course to review"
              optionFilterProp="children"
              onChange={(value) => setSelectedCourse(Number(value))}
              filterOption={(input, option) =>
                String(option?.children ?? '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {filteredCourses.map((course) => (
                <Option key={course.id} value={course.id}>
                  {course.title}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {selectedCourse && 
            renderCourseDetails(
              trendingCourses.find(course => course.id === selectedCourse)!
            )
          }
          
          <Form.Item 
            name="rating" 
            label="Your Rating"
            rules={[{ required: true, message: 'Please provide a rating' }]}
          >
            <Rate />
          </Form.Item>
          
          <Form.Item 
            name="reviewContent"
            rules={[{ required: true, message: 'Please share your course experience' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="Share your learning experience, insights, and recommendations" 
            />
          </Form.Item>
          
          <Button 
            type="primary" 
            htmlType="submit" 
            className="w-full bg-teal-700 hover:bg-teal-800"
          >
            <ShareAltOutlined /> Share Your Review
          </Button>
        </Form>

        {/* Recent Reviews */}
        <div className="max-w-3xl mx-auto space-y-6">
          {reviews.map((review) => (
            <Card key={review.id} className="hover:shadow-md transition-all">
              <div className="flex items-center mb-4">
                <Avatar 
                  src={review.userAvatar} 
                  icon={<UserOutlined />} 
                  className="mr-4" 
                />
                <div>
                  <h4 className="font-semibold text-teal-700">
                    {review.userName}
                  </h4>
                  <Rate disabled defaultValue={review.rating} />
                </div>
              </div>
              <p className="text-gray-700">{review.content}</p>
              <div className="text-right text-gray-500 text-sm mt-2">
                {review.timestamp}
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Community Impact Section */}
      <div className="bg-teal-700 text-white py-16 text-center">
        <h2 className="text-4xl font-bold mb-6">
          Transform Your Learning Journey
        </h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto text-teal-100">
          Join thousands of African learners who are breaking barriers, 
          gaining skills, and creating impactful careers through LearnKonnect
        </p>
        <Button 
          type="primary" 
          size="large" 
          className="bg-white text-teal-700 hover:bg-teal-50"
        >
          Become a LearnKonnect Member
        </Button>
      </div>
    </div>
</Layout>
  );
};

export default LearnKonnectCommunity;