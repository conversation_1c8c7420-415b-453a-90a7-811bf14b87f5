"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {  LockOutlined, EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import { Input, Button, ConfigProvider, ThemeConfig } from "antd";
import InputTemplate from "@/components/ui/input-template";

const theme: ThemeConfig = {
    token: {
      colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF", 
    },
  };

function Login(){
  const router = useRouter();
  const [visible, setVisible] = useState(false); 

  const toggleVisibility = () => {
    setVisible(!visible); 
  };

    return(
        <div className="bg-[#f4f8f7] m-4 rounded-xl p-3">
        <div className="flex flex-row  ml-4 justify-between ">
          <div className="mt-5 mr-3">
            <div className="flex justify-center items-center ">
              <Image src={"/logo.png"} alt="logo" width={120} height={50} />
            </div>
            
  <h1 className=" text-4xl flex justify-center items-center text-[#008080] font-bold">Welcome Back!!</h1>
            <form className="mt-8">
                <ConfigProvider theme={theme}>
                {/* <Input
                size="large"
                placeholder="Email"
                prefix={<MailOutlined />}
                className="mr-2 mt-2 w-140"
              /> */}
              <InputTemplate fieldName="email" label="Email" className={"  hover:bg-transparent border-"} />
              <Input
                size="large"
                placeholder="Password"
                prefix={<LockOutlined />}
                className="mr-2 mt-4 w-144"
                type={visible ? "text" : "password"} // Toggle password visibility
                suffix={
                  // Toggle the visibility icon based on the `visible` state
                  visible ? (
                    <EyeInvisibleOutlined onClick={toggleVisibility} />
                  ) : (
                    <EyeOutlined onClick={toggleVisibility} />
                  )
                }
              />
                </ConfigProvider>
       
            </form>
            <p className="flex justify-end cursor-pointer" onClick={() => router.push("/resetPassword")}  >Forgot password?</p>
  
            <div className="flex justify-center mt-5">
            <ConfigProvider theme={theme}>
            <Button className="text-xl p-5 text-[#008080]"
                block
              >
                Login
              </Button>
            </ConfigProvider>
            </div>
            <p className="flex justify-center mt-5 text-[#008081]">-or-</p>
            <div className="flex flex-row justify-center items-center mt-5">
              <Image
                src="/google.png"
                alt={"google"}
                width={30}
                height={5}
                className="mr-7"
              />
              <Image
                src="/faceb.png"
                alt={"facebook"}
                width={20}
                height={10}
                className="mr-7"
              />
              <Image
                src="/apple.png"
                alt={"apple"}
                width={30}
                height={5}
                className="mr-7"
              />
            </div>
            <div className="flex justify-center mt-20">
            <p>
              Do not have an account?  
              <span
                className="text-[#008080] cursor-pointer font-semibold ml-1 hover:underline"
                onClick={() => router.push("/signupStudent")}  
              >
                Sign up
              </span>
            </p>
          </div>
          </div>
  
          <div className="hidden md:block ">
            <Image src="/stud2.jpg" alt="Image1" width={500} height={10} />
          </div>
        </div>
      </div>
    )
}

export default Login;