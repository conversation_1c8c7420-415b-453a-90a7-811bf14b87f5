'use client';
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  VideoCameraAddOutlined,
  StarOutlined,
  UserOutlined,
  BarChartOutlined,
  FileTextOutlined,
  BookOutlined,
  LeftOutlined,
  RightOutlined,
  MoreOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  BellOutlined,
  FireOutlined,
  ArrowUpOutlined,
  TrophyOutlined,
  TeamOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

// Import components
import Sidebar from '@/app/dashboard/sidebar';
import TopBar from '@/components/general/dashboard/sidebar/topbar';
// import ButtonTemplate from '@/components/ui/button-template';
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import Link from 'next/link';
import ButtonTemplate from '@/components/ui/button-template';

// Dashboard Component
const Dashboard = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showAmount, setShowAmount] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [activeCard, setActiveCard] = useState<string | null>(null);

  const { isDark } = useTeacherTheme();
  const themeStyles = getThemeStyles(isDark);


  // Check if screen is mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);


  // Calendar functions
  const daysInMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  ).getDate();

  const monthName = currentDate.toLocaleString('default', { month: 'short' }).toUpperCase();
  const year = currentDate.getFullYear();

  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() - 1)));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() + 1)));
  };

  const isToday = (day: number) => {
    const today = new Date();
    return (
      day === today.getDate() &&
      currentDate.getMonth() === today.getMonth() &&
      currentDate.getFullYear() === today.getFullYear()
    );
  };

  const attendanceData = {
    present: 305,
    onLeave: 12,
    absentees: 63,
  };

  const courseData = {
    completion: 70,
    coursesSold: 40,
    coursesWatched: 30,
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };


  const iconVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.2, rotate: 10, transition: { duration: 0.3 } }
  };

  // Greeting logic for the UI
  let greeting;
  const hour = new Date().getHours();
  if (hour < 12) {
    greeting = 'Good Morning';
  } else if (hour ==12) {
    greeting = 'Good Afternoon';
  } else {
    greeting = 'Good Evening';
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex flex-col h-screen overflow-hidden bg-gray-50">
      <TopBar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />

      <div className="flex flex-1 overflow-hidden">
        <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />

        <main className="flex-1 overflow-y-auto" style={{ backgroundColor: themeStyles.bgSecondary }}>
          {/* Secondary Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white shadow-sm px-4 md:px-6 py-4 sticky top-0 z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-2"
            // style={{ backgroundColor: themeStyles.bgHeader }}
         >
            <div className="flex items-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                className="mr-2 text-[#0A6B6A]"
              >
                <BellOutlined style={{ fontSize: '18px' }} />
              </motion.div>
              <h1 className="text-lg font-bold text-gray-800">
                {greeting}, <span className="text-[#008080]">learnKonnect</span> 👋
              </h1>
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ButtonTemplate
                className="!px-4 !py-2 text-sm font-semibold shadow-md hover:shadow-lg transition-all duration-300"
                label="New Session"
                icon={<VideoCameraAddOutlined className="text-base" />}
              />
            </motion.div>
          </motion.header>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-3 md:p-4">
            {/* Revenue Card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 relative hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('revenue')}
              onMouseLeave={() => setActiveCard(null)}
              style={{ maxWidth: '350px', maxHeight: '150px' }}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xs font-semibold text-gray-700 flex items-center">
                    <motion.span
                      variants={iconVariants}
                      animate={activeCard === 'revenue' ? 'hover' : 'initial'}
                      className="mr-1"
                    >
                      <BarChartOutlined className="text-[#0A6B6A] text-sm" />
                    </motion.span>
                    Total Revenue
                  </h2>
                </div>
                <div className="bg-[#E6F6F6] p-1.5 rounded-md">
                  <FireOutlined className="text-[#0A6B6A] text-sm" />
                </div>
              </div>
              <motion.p
                className="text-xl font-bold text-gray-800 mt-2"
                animate={{ scale: activeCard === 'revenue' ? 1.05 : 1 }}
                transition={{ duration: 0.2 }}
              >
                {showAmount ? '$168.2k' : '****'}
              </motion.p>
              <button
                className="absolute right-3 top-3 p-1 hover:bg-gray-100 rounded-full transition-colors duration-300"
                onClick={() => setShowAmount(!showAmount)}
              >
                {showAmount ? (
                  <EyeOutlined className="text-[#0A6B6A] text-sm" />
                ) : (
                  <EyeInvisibleOutlined className="text-[#0A6B6A] text-sm" />
                )}
              </button>
              <div className="mt-2 flex items-center text-xs font-medium bg-green-50 p-1 rounded-md w-fit">
                <ArrowUpOutlined className="text-green-500 mr-1" />
                <span className="text-green-500 font-bold">12%</span>
                <span className="text-gray-500 ml-1">vs last month</span>
              </div>
            </motion.div>

            {/* Rating card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              custom={1}
              transition={{ delay: 0.1 }}
              className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('rating')}
              onMouseLeave={() => setActiveCard(null)}
              style={{ maxWidth: '350px', maxHeight: '150px' }}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xs font-bold text-gray-700 flex items-center">
                    <motion.span
                      variants={iconVariants}
                      animate={activeCard === 'rating' ? 'hover' : 'initial'}
                      className="mr-1"
                    >
                      <StarOutlined className="text-[#0A6B6A] text-sm" />
                    </motion.span>
                    Average Rating
                  </h2>
                </div>
                <div className="bg-[#E6F6F6] p-1.5 rounded-md">
                  <img
                    src="/Rating.png"
                    alt="Rating trend"
                    className="w-5 h-5 object-contain"
                  />
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <motion.div
                  className="text-2xl font-bold text-gray-800"
                  animate={{ scale: activeCard === 'rating' ? 1.05 : 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src="/Rating1.png"
                    alt="Rating value"
                    className="w-16 h-10 object-contain"
                  />
                </motion.div>
                <motion.div
                  className="flex text-[#FFB800]"
                  animate={{
                    x: activeCard === 'rating' ? [0, 5, -5, 5, -5, 0] : 0
                  }}
                  transition={{ duration: 0.4 }}
                >
                  {/* Stars removed as per updated code */}
                </motion.div>
              </div>

              <div className="flex items-center justify-between mt-2">
                <div className="text-xs font-bold p-1 rounded-md w-fit">
                  <span className="text-gray-600">Total Rating</span>
                </div>
                <div className="mt-2">
                  <span className="text-yellow-600 font-bold" style={{ marginRight: '-0.1rem' }}>4.8/5</span>
                </div>
              </div>
            </motion.div>


            {/* Students Card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              custom={2}
              transition={{ delay: 0.2 }}
              className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('students')}
              onMouseLeave={() => setActiveCard(null)}
              style={{ maxWidth: '350px', maxHeight: '150px' }}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xs font-bold text-gray-700 flex items-center">
                    <motion.span
                      variants={iconVariants}
                      animate={activeCard === 'students' ? 'hover' : 'initial'}
                      className="mr-1"
                    >
                      <TeamOutlined className="text-[#0A6B6A] text-sm" />
                    </motion.span>
                    Total Students
                  </h2>
                </div>
                <div className="bg-[#E6F6F6] p-1.5 rounded-md">
                  <img
                    src="/student.png"
                    alt="Students icon"
                    className="w-5 h-5 object-contain"
                  />
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <motion.div
                  className="text-2xl font-bold text-[#0A6B6A]"
                  animate={{ scale: activeCard === 'students' ? 1.05 : 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src="/student1.png"
                    alt="Student count"
                    className="w-16 h-10 object-contain"
                  />
                </motion.div>
              </div>

              <div className="flex items-center justify-between mt-2">
                <div className="text-xs font-bold p-1 rounded-md w-fit">
                  <span className="text-gray-600">Total Students</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium bg-blue-50 p-1 rounded-md w-fit">
                  <ArrowUpOutlined className="text-blue-500 mr-1" />
                  <span className="text-blue-500 font-bold">5622%</span>
                </div>
              </div>
            </motion.div>

            {/* Calendar Card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              custom={3}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-lg p-3 border border-gray-100 shadow-sm hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('calendar')}
              onMouseLeave={() => setActiveCard(null)}
              style={{
                maxWidth: '350px',
                maxHeight: '150px',
                overflow: 'hidden', // Ensures nothing overflows
                background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
              }}
            >
              <div className="flex justify-between items-center mb-1">
                <h2 className="font-bold text-sm text-gray-700 flex items-center">
                  <motion.span
                    variants={iconVariants}
                    animate={activeCard === 'calendar' ? 'hover' : 'initial'}
                    className="mr-2"
                  >
                    <CalendarOutlined className="text-[#0A6B6A]" />
                  </motion.span>
                  {`${monthName} ${year}`}
                </h2>
                <div className="flex gap-1">
                  <motion.button
                    onClick={handlePrevMonth}
                    className="p-1 hover:bg-gray-100 rounded transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <LeftOutlined style={{ fontSize: '12px' }} />
                  </motion.button>
                  <motion.button
                    onClick={handleNextMonth}
                    className="p-1 hover:bg-gray-100 rounded transition-colors duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <RightOutlined style={{ fontSize: '12px' }} />
                  </motion.button>
                </div>
              </div>
              <div className="grid grid-cols-7 gap-0.5 text-center text-xs">
                {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, i) => (
                  <div key={i} className="text-gray-500 py-0.5 font-medium text-xs">{day}</div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-0.5">
                {Array.from({ length: daysInMonth }, (_, i) => i + 1).map((day) => (
                  <motion.div
                    key={day}
                    whileHover={{ scale: 1.05 }} // Reduced scale to prevent overflow
                    transition={{ duration: 0.2 }} // Faster transition
                    className={`p-0.5 text-center rounded-full text-xs ${isToday(day) ? 'bg-[#0A6B6A] text-white font-bold' : 'hover:bg-gray-100 transition-colors duration-300'
                      }`}
                    style={{
                      height: '18px', // Fixed height
                      width: '20px',  // Fixed width
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto',
                      fontSize: '0.65rem' // Smaller font size
                    }}
                  >
                    {day}
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Courses Card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              custom={4}
              transition={{ delay: 0.4 }}
              className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('courses')}
              onMouseLeave={() => setActiveCard(null)}
              style={{ maxWidth: '350px', maxHeight: '150px' }}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xs font-bold text-gray-700 flex items-center">
                    <motion.span
                      variants={iconVariants}
                      animate={activeCard === 'courses' ? 'hover' : 'initial'}
                      className="mr-1"
                    >
                      <BookOutlined className="text-[#0A6B6A] text-sm" />
                    </motion.span>
                    Total Courses
                  </h2>
                </div>
                <div className="bg-[#E6F6F6] p-1.5 rounded-md">
                  <img
                    src="/dontknow.png"
                    alt="Courses icon"
                    className="w-5 h-5 object-contain"
                  />
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <motion.div
                  className="text-2xl font-bold text-gray-800"
                  animate={{ scale: activeCard === 'courses' ? 1.05 : 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src="/student1.png"
                    alt="Courses count"
                    className="w-16 h-10 object-contain"
                  />
                </motion.div>
              </div>

              <div className="flex items-center justify-between mt-2">
                <div className="text-xs font-bold p-1 rounded-md w-fit">
                  <span className="text-gray-600">Total Courses</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium bg-green-50 p-1 rounded-md w-fit">
                  <ArrowUpOutlined className="text-green-500 mr-1" />
                  <span className="text-green-500 font-bold">10%</span>
                </div>
              </div>
            </motion.div>

            {/* Pending Quizzes Card */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              custom={5}
              transition={{ delay: 0.5 }}
              className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 hover:border-[#0A6B6A] transition-all duration-300"
              onMouseEnter={() => setActiveCard('quizzes')}
              onMouseLeave={() => setActiveCard(null)}
              style={{ maxWidth: '350px', maxHeight: '150px' }}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xs font-bold text-gray-700 flex items-center">
                    <motion.span
                      variants={iconVariants}
                      animate={activeCard === 'quizzes' ? 'hover' : 'initial'}
                      className="mr-1"
                    >
                      <FileTextOutlined className="text-[#0A6B6A] text-sm" />
                    </motion.span>
                    Pending Quizzes & Exams
                  </h2>
                </div>
                <div className="bg-[#E6F6F6] p-1.5 rounded-md">
                  <img
                    src="/growth.png"
                    alt="Quizzes icon"
                    className="w-5 h-5 object-contain"
                  />
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <motion.div
                  className="text-2xl font-bold text-gray-800"
                  animate={{ scale: activeCard === 'quizzes' ? 1.05 : 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src="/student1.png"
                    alt="Quizzes count"
                    className="w-16 h-10 object-contain"
                  />
                </motion.div>
              </div>

              <div className="flex items-center justify-between mt-2">
                <div className="text-xs font-bold p-1 rounded-md w-fit">
                  <span className="text-gray-600">Pending Quizzes</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium bg-purple-50 p-1 rounded-md w-fit">
                  <ArrowUpOutlined className="text-purple-500 mr-1" />
                  <span className="text-purple-500 font-bold">18%</span>
                </div>
              </div>
            </motion.div>

            {/* Top Courses Section */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover={{ boxShadow: "0 10px 15px rgba(0, 0, 0, 0.05)" }}
              custom={6}
              transition={{ delay: 0.6 }}
              className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 col-span-2 md:col-span-3 lg:col-span-2 hover:border-[#0A6B6A] transition-all duration-300"
            >
              <div className="flex justify-between items-center mb-3">
                <h2 className="font-bold text-sm flex items-center">
                  <motion.span
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                    className="mr-2 text-[#0A6B6A]"
                  >
                    <TrophyOutlined />
                  </motion.span>
                  Top Performing Courses
                </h2>
                <motion.button
                  className="text-[#0A6B6A] text-xs font-semibold px-2 py-1 rounded hover:bg-[#E6F6F6] transition-colors duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  View All →
                </motion.button>
              </div>
              <div className="space-y-2">
                {[
                  { name: 'Get Started with Figma', students: '2,440', color: 'bg-[#B8F0EF]', img: '/Figma.png' },
                  { name: 'Advanced Prototyping', students: '2,440', color: 'bg-[#FEEBC8]', img: '/prototyping.png' },
                  { name: 'Sketch 101 - UI Design', students: '2,440', color: 'bg-[#C8FACD]', img: '/logo.png' },
                ].map((course, i) => (
                  <motion.div
                    key={i}
                    className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg transition-colors duration-300"
                    whileHover={{ scale: 1.01, backgroundColor: "#F9FAFB" }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 + (i * 0.1) }}
                  >
                    <div className="flex items-center gap-3">
                      <motion.div
                        className={`w-10 h-10 rounded-lg ${course.color} flex items-center justify-center`}
                        whileHover={{ rotate: 10 }}
                      >
                        <img src={course.img} alt={course.name} className="w-6 h-6 rounded-full" />
                      </motion.div>
                      <div>
                        <p className="font-semibold text-sm">{course.name}</p>
                        <p className="text-xs text-gray-500 flex items-center">
                          <ClockCircleOutlined className="mr-1" /> 2 months ago
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600 text-xs mr-2 font-medium">{course.students}</span>
                      <motion.div
                        className="w-3 h-3 bg-[#0A6B6A] rounded-full"
                        whileHover={{ scale: 1.5 }}
                        transition={{ duration: 0.2 }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>



          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 px-3 pb-4">
           
           
            {/* Student Locations */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover={{ boxShadow: "0 10px 15px rgba(0, 0, 0, 0.05)" }}
              custom={7}
              transition={{ delay: 0.7 }}
              className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 lg:col-span-1 hover:border-[#0A6B6A] transition-all duration-300"
            >
              <h2 className="font-bold text-sm mb-3 flex items-center">
                <motion.span
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                  className="mr-2 text-[#0A6B6A]"
                >
                  <GlobalOutlined />
                </motion.span>
                Top Student Locations
              </h2>
              <div className="flex justify-center mb-3">
                <motion.div
                  className="relative w-32 h-32"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <svg viewBox="0 0 100 100" className="w-full h-full">
                    <circle cx="50" cy="50" r="40" fill="transparent" stroke="#F59E0B" strokeWidth="20"></circle>
                    <circle cx="50" cy="50" r="40" fill="transparent" stroke="#0A6B6A" strokeWidth="20" strokeDasharray="251.2" strokeDashoffset="62.8"></circle>
                    <circle cx="50" cy="50" r="40" fill="transparent" stroke="#3B82F6" strokeWidth="20" strokeDasharray="251.2" strokeDashoffset="200.96" transform="rotate(-120 50 50)"></circle>
                    <circle cx="50" cy="50" r="18" fill="white"></circle>
                    <text x="50" y="50" textAnchor="middle" dominantBaseline="middle" fontSize="10" fontWeight="bold">Ghana</text>
                  </svg>
                </motion.div>
              </div>
              <div className="space-y-2">
                {[
                  { country: 'Ghana', percentage: '75%', color: 'bg-[#0A6B6A]' },
                  { country: 'Nigeria', percentage: '20%', color: 'bg-[#F59E0B]' },
                  { country: 'Zimbabwe', percentage: '15%', color: 'bg-[#3B82F6]' }
                ].map((loc, i) => (
                  <motion.div
                    key={i}
                    className="flex justify-between items-center p-2 rounded-lg hover:bg-gray-50 transition-colors duration-300"
                    whileHover={{ scale: 1.01, backgroundColor: "#F9FAFB" }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + (i * 0.1) }}
                  >
                    <div className="flex items-center gap-2">
                      <motion.div
                        className={`w-3 h-3 rounded-full ${loc.color}`}
                        whileHover={{ scale: 1.5 }}
                        transition={{ duration: 0.2 }}
                      />
                      <span className="text-sm font-medium">{loc.country}</span>
                    </div>
                    <span className="text-sm text-gray-700 font-bold">{loc.percentage}</span>
                  </motion.div>
                ))}
              </div>
            <div className="mt-3 text-center">
              <Link href="/dashboard/topstudent" passHref>
                <motion.button 
                  className="text-[#0A6B6A] text-xs font-semibold px-3 py-1.5 rounded-lg hover:bg-[#E6F6F6] transition-colors duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  View More Locations
                </motion.button>
              </Link>
            </div>
            </motion.div>

                  {/* Course Stats Card */}
<motion.div
  className="w-full" // Maintain original width
  whileHover={{ scale: 1.02 }}
  transition={{ duration: 0.2 }}
  style={{ marginLeft: 'auto' }} // Push to the right
>
  <div
    className="rounded-lg p-3 border border-gray-100 shadow-sm hover:border-[#0A6B6A] transition-all duration-300"
    style={{
      background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
    }}
  >
    <div className="flex justify-between items-center mb-3">
      <div className="font-bold text-sm text-gray-700 flex items-center">
        <BookOutlined className="mr-2 text-[#0A6B6A]" />
        Course Stats
      </div>
      <motion.button
        className="p-1 rounded hover:bg-gray-100 transition-colors duration-300"
        whileHover={{ rotate: 180 }}
        transition={{ duration: 0.3 }}
      >
        <MoreOutlined style={{ fontSize: '12px' }} />
      </motion.button>
    </div>

    <div className="flex justify-center my-2">
      <div className="relative w-20 h-20">
        {/* Gray background circle */}
        <div className="absolute inset-0 rounded-full bg-gray-200"></div>

        {/* Progress circle */}
        <svg className="absolute inset-0" viewBox="0 0 100 100">
          <circle cx="50" cy="50" r="40" fill="transparent" stroke="#E6F6F6" strokeWidth="15"></circle>
          <motion.circle
            cx="50"
            cy="50"
            r="40"
            fill="transparent"
            stroke="#22C55E"
            strokeWidth="15"
            strokeDasharray="251.2"
            strokeDashoffset="251.2"
            animate={{ strokeDashoffset: 251.2 - (251.2 * courseData.completion / 100) }}
            transition={{ duration: 1.5, delay: 1 }}
            transform="rotate(-90 50 50)"
          ></motion.circle>
        </svg>

        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="text-lg font-bold"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2 }}
          >
            {courseData.completion}%
          </motion.div>
        </div>
      </div>
    </div>

    <div className="flex justify-center gap-4">
      <motion.div
        className="flex items-center"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1.3 }}
        whileHover={{ scale: 1.05 }}
      >
        <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
        <div>
          <div className="text-xs text-gray-500 font-medium">Course Sales</div>
          <div className="text-xs font-bold text-blue-500">{courseData.coursesSold}% over</div>
        </div>
      </motion.div>

      <motion.div
        className="flex items-center"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1.4 }}
        whileHover={{ scale: 1.05 }}
      >
        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
        <div>
          <div className="text-xs text-gray-500 font-medium">Course Watch</div>
          <div className="text-xs font-bold text-green-500">{courseData.coursesWatched}% over</div>
        </div>
      </motion.div>
    </div>
    <br />
    <br />

    {/* Attendance Card */}
    <motion.div
      className="w-full"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div
        className="rounded-lg p-3 border border-gray-100 shadow-sm hover:border-[#0A6B6A] transition-all duration-300"
        style={{
          background: 'linear-gradient(to bottom, #ffffff, #f9fafb)',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div className="flex justify-between items-center mb-3">
          <div className="font-bold text-sm text-gray-700 flex items-center">
            <TeamOutlined className="mr-2 text-[#0A6B6A]" />
            Attendance
          </div>
          <div className="flex items-center">
            <span className="text-xs text-gray-500 mr-1 font-medium">All</span>
            <motion.button
              className="p-1 rounded hover:bg-gray-100 transition-colors duration-300"
              whileHover={{ rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              <MoreOutlined style={{ fontSize: '12px' }} />
            </motion.button>
          </div>
        </div>

        <div className="flex justify-between">
          <motion.div
            className="text-center"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="relative mb-1">
              <motion.div
                className="text-base font-bold text-green-500"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.9 }}
              >
                {attendanceData.present}
              </motion.div>
              <motion.div
                className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <div className="text-xs text-gray-500 font-medium">Present</div>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="relative mb-1">
              <motion.div
                className="text-base font-bold text-yellow-500"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1 }}
              >
                {attendanceData.onLeave}
              </motion.div>
              <motion.div
                className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <div className="text-xs text-gray-500 font-medium">Course cancellation</div>
          </motion.div>

          <motion.div
            className="text-center"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="relative mb-1">
              <motion.div
                className="text-base font-bold text-red-500"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.1 }}
              >
                {attendanceData.absentees}
              </motion.div>
              <motion.div
                className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <div className="text-xs text-gray-500 font-medium">Absentees</div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  </div>
</motion.div>
          </div>

          {/* Exam History - Now taking full width at bottom */}
          <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover={{ boxShadow: "0 10px 15px rgba(0, 0, 0, 0.05)" }}
            custom={10}
            transition={{ delay: 1 }}
            className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 lg:col-span-3 hover:border-[#0A6B6A] transition-all duration-300"
          >
            <div className="flex justify-between items-center mb-3">
              <h2 className="font-bold text-sm flex items-center">
                <FileTextOutlined className="mr-2 text-[#0A6B6A]" />
                Exam & Quiz History
              </h2>
              <motion.button
                className="text-[#0A6B6A] text-xs font-semibold px-2 py-1 rounded hover:bg-[#E6F6F6] transition-colors duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View All →
              </motion.button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full min-w-[600px]">
                <thead className="text-left text-gray-500 text-xs border-b">
                  <tr>
                    <th className="pb-2 font-bold">Title</th>
                    <th className="pb-2 font-bold">Course name</th>
                    <th className="pb-2 font-bold">Participants</th>
                    <th className="pb-2 font-bold">Submitted</th>
                    <th className="pb-2 font-bold">Status</th>
                    <th className="pb-2 font-bold">Schedule</th>
                  </tr>
                </thead>
                <tbody className="text-xs">
                  {[
                    { type: 'Quiz', course: 'Project management', participants: '200', submitted: '150/200', status: 'Running', schedule: '14 Feb - 18 Jun, 2025' },
                    { type: 'Exam', course: 'Python programming', participants: '200', submitted: '200/200', status: 'Ended', schedule: '18 Feb, 2025- 18 Mar, 2025' },
                    { type: 'Quiz', course: 'Project management', participants: '200', submitted: '150/200', status: 'Running', schedule: '14 Feb - 18 Jun, 2025' },
                    { type: 'Exam', course: 'Python programming', participants: '200', submitted: '75/200', status: 'Running', schedule: '18 Feb, 2025- 18 Mar, 2025' }
                  ].map((item, i) => (
                    <motion.tr
                      key={i}
                      className="border-b hover:bg-gray-50 transition-colors duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1.1 + (i * 0.1) }}
                      whileHover={{ backgroundColor: "#F9FAFB" }}
                    >
                      <td className="py-2">
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 bg-[#E6F6F6] text-[#0A6B6A] rounded-full flex items-center justify-center text-xs font-bold">
                            {item.type === 'Quiz' ? 'Q' : 'E'}
                          </div>
                          <span className="font-medium">{item.type}</span>
                        </div>
                      </td>
                      <td className="py-2 font-medium">{item.course}</td>
                      <td className="py-2">
                        <div className="flex items-center">
                          <TeamOutlined className="mr-1 text-[#0A6B6A]" />
                          {item.participants}
                        </div>
                      </td>
                      <td className="py-2">
                        <div className="flex items-center">
                          <CheckCircleOutlined className="mr-1 text-green-500" />
                          <span className="font-medium">{item.submitted}</span>
                        </div>
                      </td>
                      <td className="py-2">
                        <div className="flex items-center">
                          <span className={`${item.status === 'Running' ? 'text-green-500' : 'text-gray-500'} mr-1`}>●</span>
                          <span className={`font-medium ${item.status === 'Running' ? 'text-green-500' : 'text-gray-500'}`}>{item.status}</span>
                        </div>
                      </td>
                      <td className="py-2">
                        <div className="flex items-center">
                          <CalendarOutlined className="mr-1 text-[#0A6B6A]" />
                          {item.schedule}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
            {/* <div className="flex justify-center mt-3 gap-1 flex-wrap">
              <motion.button
                className="px-2 py-1 border rounded text-xs hover:bg-gray-50 transition-colors duration-300 font-medium"
                whileHover={{ scale: 1.05, backgroundColor: "#F9FAFB" }}
                whileTap={{ scale: 0.95 }}
              >
                Previous
              </motion.button>
              <motion.button
                className="px-2 py-1 border rounded text-xs hover:bg-gray-50 transition-colors duration-300 font-medium"
                whileHover={{ scale: 1.05, backgroundColor: "#F9FAFB" }}
                whileTap={{ scale: 0.95 }}
              >
                1
              </motion.button>
              <motion.button
                className="px-2 py-1 border rounded text-xs bg-[#0A6B6A] text-white font-medium"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                2
              </motion.button>
              <motion.button
                className="px-2 py-1 border rounded text-xs hover:bg-gray-50 transition-colors duration-300 font-medium"
                whileHover={{ scale: 1.05, backgroundColor: "#F9FAFB" }}
                whileTap={{ scale: 0.95 }}
              >
                4
              </motion.button>
            </div> */}
          </motion.div>
        </main>
      </div>
    </div>
  );
};


export default Dashboard;