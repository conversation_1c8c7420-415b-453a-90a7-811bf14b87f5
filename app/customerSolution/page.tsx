'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Form, Select, message, ConfigProvider } from 'antd';
import { 
  CheckOutlined, 
  GlobalOutlined, 
  BarChartOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  CalendarOutlined 
} from '@ant-design/icons';
import Layout from '@/components/general/indexPagelayout/layout'; 
import InputTemplate from '@/components/ui/input-template'; 
import ButtonTemplate from '@/components/ui/button-template'; 

const { Option } = Select;

const CustomerSolution: React.FC = () => {
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('Enterprise inquiry submitted successfully!');
      form.resetFields();
    } catch (error) {
      message.error('Submission failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const enterpriseFeatures = [
    {
      icon: <CheckOutlined className="text-4xl text-teal-700" />,
      title: "Unmatched Security",
      description: "Custom-built fraud prevention with advanced machine learning."
    },
    {
      icon: <GlobalOutlined className="text-4xl text-teal-700" />,
      title: "Global Scalability",
      description: "Unlimited domains and enterprise-grade infrastructure."
    },
    {
      icon: <BarChartOutlined className="text-4xl text-teal-700" />,
      title: "Comprehensive Insights",
      description: "Real-time analytics and detailed compliance reportsing."
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-16">
        <div className="container mx-auto px-4">
          {/* Header Section */}
          <motion.div 
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl font-bold text-teal-700 mb-4">
              Enterprise Credential Verification Solutions
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Tailored verification strategies for organizations with complex security needs
            </p>
          </motion.div>

          {/* Grid Layout for Features and Form */}
          <div className="grid md:grid-cols-2 gap-12">
            {/* Features Section */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="space-y-8"
            >
              {enterpriseFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  whileHover={{ scale: 1.02 }}
                  className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all"
                >
                  <div className="flex items-center mb-4">
                    {feature.icon}
                    <h3 className="ml-2 text-2xl font-semibold text-teal-700">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-gray-700">{feature.description}</p>
                </motion.div>
              ))}

              {/* Enterprise Hotline Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                whileHover={{ scale: 1.02 }}
                className="bg-teal-50 p-6 rounded-xl border-l-4 border-teal-700"
              >
                <div className="flex items-center mb-4">
                  <PhoneOutlined className="text-3xl text-teal-700 mr-4" />
                  <span className="text-lg font-semibold text-teal-700">
                    Enterprise Hotline
                  </span>
                </div>
                <p className="text-gray-700">+1 (+233) 247605936</p>
                <p className="text-gray-700">+1 (+14) 042596885</p>
              </motion.div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white p-8 rounded-xl shadow-lg"
            >
              <h2 className="text-2xl font-bold text-teal-700 mb-6">
                Request Enterprise Consultation
              </h2>
              <Form 
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
              >
                <Form.Item
                  name="name"
                  label="Full Name"
                  rules={[{ required: true, message: 'Please enter your name' }]}
                >
                  <InputTemplate 
                    label=""
                    fieldName="name"
                    placeHolder="Your Full Name"
                    required={true}
                  />
                </Form.Item>

                <Form.Item
                  name="email"
                  label="Business Email"
                  rules={[
                    { required: true, message: 'Please enter your email' },
                    { type: 'email', message: 'Please enter a valid email' }
                  ]}
                >
                  <InputTemplate 
                    label=""
                    fieldName="email"
                    placeHolder="<EMAIL>"
                    required={true}
                    inputType="email"
                  />
                </Form.Item>
             
                <Form.Item
                  name="company"
                  label="Company Name"
                  rules={[{ required: true, message: 'Please enter your company name' }]}
                >
                  <InputTemplate 
                    label=""
                    fieldName="company"
                    placeHolder="Company Name"
                    required={true}
                  />
                </Form.Item>

                <Form.Item
                  name="industry"
                  label="Industry"
                  rules={[{ required: true, message: 'Please select your industry' }]}
                >
                  <Select 
                    size="large" 
                    placeholder="Select Your Industry"
                  >
                    <Option value="finance">Financial Services</Option>
                    <Option value="healthcare">Healthcare</Option>
                    <Option value="education">Education</Option>
                    <Option value="technology">Technology</Option>
                    <Option value="government">Government</Option>
                    <Option value="other">Other</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="message"
                  label="Additional Details"
                >
                  <InputTemplate 
                    label="Additional Details"
                    fieldName="message"
                    placeHolder="Tell us about your verification challenges"
                    textarea={true}
                    textareaRows={4}
                  />
                </Form.Item>

                <Form.Item>
                  <ButtonTemplate 
                    label="Request Consultation"
                    htmlType="submit"
                    type="primary"
                    className="w-full bg-teal-700 hover:bg-teal-800"
                    size="large"
                    handleClick={() => form.submit()}
                  />
                </Form.Item>
              </Form>
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CustomerSolution;