"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  LockOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import { Input, Button, ConfigProvider, ThemeConfig } from "antd";
import { useState } from "react";

const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};

function CreateNewPassword() {
    const [visible, setVisible] = useState(false);

    const toggleVisibility = () => {
        setVisible(!visible);
      };
    const router = useRouter();
  return (
    <div className="bg-[#f4f8f7] m-4 rounded-xl p-3">
      <div className="flex flex-row  ml-4 justify-between ">

        <div className="hidden md:block ">
          <Image src="/stud2.jpg" alt="Image1" width={500} height={10} />
        </div>
        <div className="mb-4 ">
          <div className="flex justify-center items-center ">
            <Image src={"/logo.png"} alt="logo" width={120} height={50} />
          </div>
            <div className="flex justify-center items-center">
            <h1 className=" text-4xl  text-[#008080] font-bold">
            Create New Password
          </h1>
            </div>
            <div className="flex justify-center text-sm/6 mt-4">
            <p>Your new password is different from previous used passwords</p>
            </div>


          <form className="mt-4">
            <ConfigProvider theme={theme}>
            <Input
                size="large"
                placeholder="Password"
                prefix={<LockOutlined />}
                className="mt-4"
                type={visible ? "text" : "password"} // Toggle password visibility
                suffix={
                  // Toggle the visibility icon based on the `visible` state
                  visible ? (
                    <EyeInvisibleOutlined onClick={toggleVisibility} />
                  ) : (
                    <EyeOutlined onClick={toggleVisibility} />
                  )
                }
              />
                            <Input
                size="large"
                placeholder="Password"
                prefix={<LockOutlined />}
                className="mt-4"
                type={visible ? "text" : "password"} // Toggle password visibility
                suffix={
                  // Toggle the visibility icon based on the `visible` state
                  visible ? (
                    <EyeInvisibleOutlined onClick={toggleVisibility} />
                  ) : (
                    <EyeOutlined onClick={toggleVisibility} />
                  )
                }
              />
            </ConfigProvider>
          </form>
          <div className="flex justify-center mt-20">
            <ConfigProvider theme={theme}>
              <Button className="text-xl p-5 text-[#008080]" block>
                Submit
              </Button>
            </ConfigProvider>
          </div>
          <div className="flex justify-center mt-20">
            <p>
              Do not have an account?  
              <span
                className="text-[#008080] cursor-pointer font-semibold ml-1 hover:underline"
                onClick={() => router.push("/login")}  
              >
                Sign in
              </span>
            </p>
          </div>

        </div>
      </div>
    </div>
  );
}

export default CreateNewPassword;
