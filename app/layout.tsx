import type { Metadata } from "next";
import "../styles/globals.css";
import { App } from "antd";
import BackToTop from "@/components/general/backtoTop/BackToTop";
import {TeacherThemeProvider } from '@/components/general/TeacherTheme/TeachersTheme';

export const metadata: Metadata = {
  title: "LearnKonnect",
  description: "LearnKonnect is an innovative online educational platform designed to cater to your unique learning needs. Whether you're a student, professional, or lifelong learner, our platform offers a personalized learning experience with curated courses, interactive resources, and expert guidance. With a user-friendly interface and adaptive learning paths, LearnKonnect ensures that you gain the knowledge and skills you need to excel in your field.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <TeacherThemeProvider>
          <App>
            {children}
            <BackToTop /> 
          </App>
        </TeacherThemeProvider>
      </body>
    </html>
  );
}