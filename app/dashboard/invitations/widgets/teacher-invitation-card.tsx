'use client';
import React from 'react';
import { useNotification } from '@/hooks/useNotifs';
import { TeacherInvitation } from '../../notice-logic/useTeacherInvitations';

interface Props {
  invitation: TeacherInvitation;
  onAccept: () => Promise<boolean> | void;
  onReject: () => Promise<boolean> | void;
}

export default function TeacherInvitationCard({ invitation, onAccept, onReject }: Props) {
  const { showNotification } = useNotification();
  const [loading, setLoading] = React.useState(false);

  const handle = async (cb: () => Promise<boolean> | void) => {
    try {
      setLoading(true);
      await cb();
    } catch (err) {
      console.error(err);
      showNotification('error', 'Error', 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const statusBadge = (status: string) => {
    const map: Record<string, { bg: string; text: string; label: string }> = {
      pending: { bg: 'bg-yellow-50', text: 'text-yellow-700', label: 'Pending' },
      accepted: { bg: 'bg-green-50', text: 'text-green-700', label: 'Accepted' },
      declined: { bg: 'bg-red-50', text: 'text-red-700', label: 'Declined' },
    };
    const { bg, text, label } = map[status.toLowerCase()] || {
      bg: 'bg-gray-100',
      text: 'text-gray-700',
      label: status,
    };
    return <span className={`text-xs px-2 py-1 rounded-full ${bg} ${text} font-medium`}>{label}</span>;
  };

  return (
    <div className="flex flex-col justify-between bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden min-h-[260px]">
      <div className="p-3 flex-1 flex flex-col gap-1">
        <div className="flex items-center justify-between mb-1 text-[11px] text-gray-500">
          <span>{new Date(invitation.created_at).toLocaleDateString()}</span>
          {statusBadge(invitation.status)}
        </div>
        <h4 className="text-base font-semibold text-gray-900 truncate" title={invitation.group_name || 'Group Invitation'}>
          {invitation.group_name || 'Group Invitation'}
        </h4>
        {invitation.message && (
          <p className="text-sm text-gray-600 leading-snug line-clamp-4">{invitation.message}</p>
        )}
        {invitation.group_description && (
          <p className="text-sm text-gray-600 leading-snug line-clamp-3 mt-2">
            {invitation.group_description}
          </p>
        )}
      </div>
      {invitation.status === 'pending' ? (
        <div className="bg-gray-50 px-2 py-1.5 flex justify-between border-t border-gray-100 space-x-2">
          <button
            className="text-sm font-semibold text-white bg-teal-600 hover:bg-teal-700 px-4 py-2 rounded transition-colors flex-1 disabled:opacity-50"
            disabled={loading}
            onClick={() => handle(onAccept)}
          >
            {loading ? '...' : 'Accept'}
          </button>
          <button
            className="text-sm font-semibold text-gray-700 hover:bg-gray-100 px-4 py-2 rounded transition-colors border border-gray-300 flex-1 disabled:opacity-50"
            disabled={loading}
            onClick={() => handle(onReject)}
          >
            Decline
          </button>
        </div>
      ) : (
        <div className="bg-gray-50 px-3 py-2 text-center border-t border-gray-100">
          <span
            className={`text-xs font-medium ${invitation.status === 'accepted' ? 'text-green-600' : 'text-gray-600'}`}
          >
            {invitation.status === 'accepted' ? '✓ Accepted' : '✗ Declined'}
          </span>
        </div>
      )}
    </div>
  );
}
