"use client";
import React from 'react';
import { Spin } from 'antd';
import { useTeacherInvitations } from '../notice-logic/useTeacherInvitations';
import TeacherInvitationCard from './widgets/teacher-invitation-card';

export default function TeacherInvitationsPage() {
  const {
    loading,
    currentInvitations,
    currentPage,
    totalPages,
    handlePageChange,
    respondToInvitation,
  } = useTeacherInvitations();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Spin />
      </div>
    );
  }

  return (
    <div className="p-4 max-w-6xl mx-auto w-full overflow-y-auto">
      <h1 className="text-2xl font-bold text-teal-800 mb-6 underline">Teacher Invitation Requests</h1>

      <div className="flex justify-between items-center mb-4">
        {totalPages > 1 && (
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-2 py-1 rounded-md border hover:bg-gray-50 disabled:opacity-50"
            >
              Prev
            </button>
            <span>
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-2 py-1 rounded-md border hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        )}
      </div>

      {currentInvitations.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4" id="invites-container">
          {currentInvitations.map(invite => (
            <TeacherInvitationCard
              key={invite.id}
              invitation={invite}
              onAccept={async () => await respondToInvitation(invite.id, invite.chat_group_id, true)}
              onReject={async () => await respondToInvitation(invite.id, invite.chat_group_id, false)}
            />
          ))}
        </div>
      ) : (
        <p className="text-gray-600">No invitation requests.</p>
      )}
    </div>
  );
}
