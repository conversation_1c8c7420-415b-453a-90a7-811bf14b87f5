'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, TimePicker, InputNumber, Select } from 'antd';
import { LiveSessionData } from '../dummydata/livesessiondata';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

interface EditSessionProps {
    session: LiveSessionData;
    isVisible: boolean;
    onSave: (updatedSession: LiveSessionData) => void;
    onCancel: () => void;
}

const EditSession: React.FC<EditSessionProps> = ({ session, isVisible, onSave, onCancel }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (isVisible && session) {
            // Reset form with session data when modal becomes visible
            form.setFieldsValue({
                title: session.title,
                description: session.description,
                courseCode: session.courseCode,
                programName: session.programName,
                scheduledDate: dayjs(session.scheduledDate),
                startTime: dayjs(session.startTime, 'HH:mm'),
                duration: session.duration,
                maxCapacity: session.maxCapacity,
                materials: session.materials ? session.materials.join(', ') : '',
            });
        }
    }, [isVisible, session, form]);

    const handleSubmit = async () => {
        try {
            setLoading(true);
            const values = await form.validateFields();

            const updatedSession: LiveSessionData = {
                ...session,
                title: values.title,
                description: values.description,
                courseCode: values.courseCode,
                programName: values.programName,
                scheduledDate: values.scheduledDate.format('YYYY-MM-DD'),
                startTime: values.startTime.format('HH:mm'),
                duration: values.duration,
                maxCapacity: values.maxCapacity,
                materials: values.materials ? values.materials.split(',').map((item: string) => item.trim()) : [],
            };

            onSave(updatedSession);
            setLoading(false);
        } catch (error) {
            console.error('Validation failed:', error);
            setLoading(false);
        }
    };

    return (
        <Modal
            title="Edit Live Session"
            open={isVisible}
            onOk={handleSubmit}
            onCancel={onCancel}
            okText="Save Changes"
            cancelText="Cancel"
            confirmLoading={loading}
            width={600}
            okButtonProps={{ className: "bg-blue-600 hover:bg-blue-700" }}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    title: session?.title,
                    description: session?.description,
                    courseCode: session?.courseCode,
                    programName: session?.programName,
                }}
            >
                <Form.Item
                    name="title"
                    label="Session Title"
                    rules={[{ required: true, message: 'Please enter session title' }]}
                >
                    <Input placeholder="Enter session title" />
                </Form.Item>

                <Form.Item
                    name="description"
                    label="Description"
                >
                    <TextArea rows={3} placeholder="Enter session description" />
                </Form.Item>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Form.Item
                        name="courseCode"
                        label="Course Code"
                        rules={[{ required: true, message: 'Please enter course code' }]}
                    >
                        <Input placeholder="E.g. CS101" />
                    </Form.Item>

                    <Form.Item
                        name="programName"
                        label="Program Name"
                        rules={[{ required: true, message: 'Please enter program name' }]}
                    >
                        <Input placeholder="E.g. Computer Science" />
                    </Form.Item>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Form.Item
                        name="scheduledDate"
                        label="Date"
                        rules={[{ required: true, message: 'Please select a date' }]}
                    >
                        <DatePicker className="w-full" format="YYYY-MM-DD" />
                    </Form.Item>

                    <Form.Item
                        name="startTime"
                        label="Start Time"
                        rules={[{ required: true, message: 'Please select start time' }]}
                    >
                        <TimePicker className="w-full" format="HH:mm" />
                    </Form.Item>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Form.Item
                        name="duration"
                        label="Duration"
                        rules={[{ required: true, message: 'Please enter duration' }]}
                    >
                        <Input placeholder="E.g. 1 hour" />
                    </Form.Item>

                    <Form.Item
                        name="maxCapacity"
                        label="Maximum Capacity"
                        rules={[{ required: true, message: 'Please enter maximum capacity' }]}
                    >
                        <InputNumber min={1} className="w-full" placeholder="Enter maximum capacity" />
                    </Form.Item>
                </div>

                <Form.Item
                    name="materials"
                    label="Materials (comma separated)"
                >
                    <Input placeholder="E.g. Slides, Handouts, Quiz" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default EditSession;