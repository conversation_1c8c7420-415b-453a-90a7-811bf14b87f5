'use client';
import React from 'react';
import { Modal } from 'antd';
import { LiveSessionData } from '../dummydata/livesessiondata';

interface CancelSessionProps {
    session: LiveSessionData;
    onConfirm: (session: LiveSessionData) => void;
    onCancel: () => void;
}

const CancelSession: React.FC<CancelSessionProps> = ({ session, onConfirm, onCancel }) => {
    return (
        <Modal
            title="Cancel Live Session"
            open={true}
            onOk={() => onConfirm(session)}
            onCancel={onCancel}
            okText="Cancel Session"
            cancelText="Keep Session"
            okButtonProps={{ danger: true }}
        >
            <p className="text-sm font-light">Are you sure you want to cancel "{session.title}"?</p>
            
            <div className="mt-4 p-3 bg-red-50 text-red-800 rounded text-xs">
                <p className="font-normal mb-1">Cancelling will:</p>
                <ul className="list-disc pl-5 font-light">
                    <li>Send cancellation notices to all {session.enrolledStudents} enrolled students</li>
                    <li>Mark the session as cancelled in your records</li>
                    <li>Free up the time slot for other sessions</li>
                </ul>
                <p className="mt-2 font-light">This action cannot be undone.</p>
            </div>
        </Modal>
    );
};

export default CancelSession;