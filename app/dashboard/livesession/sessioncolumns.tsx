import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Tooltip } from 'antd';
import { BookOutlined, CalendarOutlined, ClockCircleOutlined, MoreOutlined, PlayCircleOutlined, StopOutlined, UserOutlined } from '@ant-design/icons';
import { format, isPast } from 'date-fns';
import { LiveSessionData } from '../dummydata/livesessiondata';
import type { MenuProps } from 'antd';

interface ColumnProps {
    parseDate: (dateString: string) => Date;
    handleViewDetails: (session: LiveSessionData) => void;
    handleStartSession: (session: LiveSessionData) => void;
    handleEndSession: (sessionId: string) => void;
    getActionMenu: (record: LiveSessionData) => MenuProps;
}

export const renderSessionColumns = ({
    parseDate,
    handleViewDetails,
    handleStartSession,
    handleEndSession,
    getActionMenu
}: ColumnProps) => {
    return [
        {
            title: 'Session Info',
            dataIndex: 'title',
            key: 'title',
            render: (text: string, record: LiveSessionData) => (
                <div>
                    <div className="font-light text-sm md:text-base">{text}</div>
                    <div className="text-xs text-gray-500 flex items-center gap-2 mt-1">
                        <span className="flex items-center">
                            <BookOutlined className="mr-1" />
                            {record.courseCode}
                        </span>
                        <span className="hidden sm:inline">•</span>
                        <span className="hidden sm:inline">{record.programName}</span>
                    </div>
                </div>
            ),
        },
        {
            title: 'Instructor',
            dataIndex: 'instructorName',
            key: 'instructorName',
            render: (text: string) => (
                <div className="flex items-center">
                    <UserOutlined className="mr-2 text-gray-500" />
                    <span className="text-sm font-light">{text}</span>
                </div>
            ),
            responsive: ['md'],
        },
        {
            title: 'Schedule',
            key: 'schedule',
            render: (_: any, record: LiveSessionData) => {
                const sessionDate = parseDate(record.scheduledDate);
                const isPastDate = isPast(sessionDate);

                return (
                    <div>
                        <div className="flex items-center">
                            <CalendarOutlined className="mr-2 text-gray-500" />
                            <span className={`text-sm font-light ${isPastDate ? 'text-gray-500' : ''}`}>
                                {format(sessionDate, 'MMM dd, yyyy')}
                            </span>
                        </div>
                        <div className="flex items-center mt-1 text-sm font-light">
                            <ClockCircleOutlined className="mr-2 text-gray-500" />
                            <span>{record.startTime} ({record.duration})</span>
                        </div>
                    </div>
                );
            },
        },
        {
            title: 'Students',
            key: 'students',
            render: (_: any, record: LiveSessionData) => {
                // Different display format based on session status
                if (record.status === 'completed') {
                    return (
                        <div>
                            <div className="flex items-center">
                                <UserOutlined className="mr-2 text-gray-500" />
                                <span className="text-sm font-light">{record.enrolledStudents} of {record.maxCapacity} students attended</span>
                            </div>
                            <div className="w-full bg-teal-700 rounded-full h-1.5 mt-1">
                                <div
                                    className="h-1.5 rounded-full"
                                    style={{ width: `${(record.enrolledStudents / record.maxCapacity) * 100}%` }}
                                />
                            </div>
                        </div>
                    );
                } else if (record.status === 'cancelled') {
                    return (
                        <div>
                            <div className="flex items-center">
                                <UserOutlined className="mr-2 text-teal-700" />
                                <span className="text-sm font-light">{record.enrolledStudents} of {record.maxCapacity} students enrolled</span>
                            </div>
                            <div className="w-full bg-red-700 rounded-full h-1.5 mt-1">
                                <div
                                    className="h-1.5 rounded-full"
                                    style={{ width: `${(record.enrolledStudents / record.maxCapacity) * 100}%` }}
                                />
                            </div>
                        </div>
                    );
                } else if (record.status === 'in-progress') {
                    return (
                        <div>
                            <div className="flex items-center">
                                <UserOutlined className="mr-2 text-gray-500" />
                                <span className="text-sm font-light">{record.enrolledStudents} students expected</span>
                            </div>
                            <div className="w-full bg-teal-700 rounded-full h-1.5 mt-1">
                                <div
                                    className="h-1.5 rounded-full font-regular-text"
                                    style={{ width: `${(record.enrolledStudents / record.maxCapacity) * 100}%` }}
                                />
                            </div>
                        </div>
                    );
                } else {
                    // For scheduled sessions
                    const percentage = (record.enrolledStudents / record.maxCapacity) * 100;
                    let color = 'teal-700';
                    if (percentage >= 90) color = 'green';
                    if (percentage <= 30) color = 'orange';

                    return (
                        <div>
                            <div className="flex items-center">
                                <UserOutlined className="mr-2 text-gray-500" />
                                <span className="text-sm font-light">{record.enrolledStudents} students enrolled</span>
                            </div>
                            <div className="w-full bg-teal-700 rounded-full h-1.5 mt-1">
                                <div
                                    className={`h-1.5 rounded-full bg-${color}-500`}
                                    style={{ width: `${percentage}%` }}
                                />
                            </div>
                        </div>
                    );
                }
            },
            responsive: ['lg'],
        },
        {
            title: 'Status',
            key: 'status',
            dataIndex: 'status',
            render: (status: string) => {
                const statusMap: { [key: string]: { color: string, text: string } } = {
                    'scheduled': { color: 'blue', text: 'Scheduled' },
                    'in-progress': { color: 'green', text: 'In Progress' },
                    'completed': { color: 'gray', text: 'Completed' },
                    'cancelled': { color: 'red', text: 'Cancelled' }
                };

                const { color, text } = statusMap[status] || { color: 'default', text: status };

                return <span className={`px-2 py-1 text-xs font-normal rounded-full bg-${color}-100 text-${color}-700`}>{text}</span>;
            },
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_: any, record: LiveSessionData) => {
                const isPastSession = isPast(parseDate(record.scheduledDate));

                return (
                    <Space>
                        {record.status === 'scheduled' && !isPastSession && (
                            <Tooltip title="Start Session">
                                <Button
                                    icon={<PlayCircleOutlined />}
                                    onClick={() => handleStartSession(record)}
                                    className="bg-teal-700 hover:bg-teal-700 text-white border-none"
                                    style={{
                                        backgroundColor: '#0d9488',
                                        borderColor: '#0d9488',
                                        color: 'white', // Ensure text color remains white
                                    }}
                                    size="small"
                                >
                                    <span className="hidden sm:inline">Start</span>
                                </Button>
                            </Tooltip>
                        )}

                        {record.status === 'in-progress' && (
                            <Tooltip title="End Session">
                                <Button
                                    danger
                                    icon={<StopOutlined />}
                                    onClick={() => handleEndSession(record.id)}
                                    size="small"
                                >
                                    <span className="hidden sm:inline">End</span>
                                </Button>
                            </Tooltip>
                        )}

                        <Tooltip title="More Actions">
                            <Button type="text" icon={<MoreOutlined />} onClick={() => handleViewDetails(record)} />
                        </Tooltip>
                    </Space>
                );
            },
        },
    ];
};