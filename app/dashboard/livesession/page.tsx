'use client';

import React, { useState, useEffect } from 'react';
import { Table, Space, Dropdown, Tag } from 'antd';
import { SearchOutlined, MoreOutlined, VideoCameraAddOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import liveSessions, { LiveSessionData } from '../dummydata/livesessiondata';
import type { Breakpoint, MenuProps } from 'antd';
import { format, isPast } from 'date-fns';
import { parseISO } from 'date-fns/parseISO';

// Import components
import Sidebar from '@/app/dashboard/sidebar';
import TopBar from '@/components/general/dashboard/sidebar/topbar';
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import ButtonTemplate from '@/components/ui/button-template';
import SessionModal from '@/components/general/dashboard/sessionmodal/sessionmodal';
import InputTemplate from '@/components/ui/input-template';

// Import custom session action components
import ViewSession from './viewsession';
// import StartSession from './start-your-session';
import CancelSession from './cancelsession';
import EditSession from './editsession';
import { renderSessionColumns } from './sessioncolumns';

const LiveSessionPage = () => {
    const [data, setData] = useState<LiveSessionData[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchText, setSearchText] = useState('');
    const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
    const [isViewModalVisible, setIsViewModalVisible] = useState(false);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [isStartModalVisible, setIsStartModalVisible] = useState(false);
    const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
    const [selectedSession, setSelectedSession] = useState<LiveSessionData | null>(null);
    const [isSessionModalOpen, setIsSessionModalOpen] = useState(false);
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [isMobile, setIsMobile] = useState(false);

    const { isDark } = useTeacherTheme();
    const themeStyles = getThemeStyles(isDark);

    // Check if screen is mobile on component mount and window resize
    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 768);
            if (window.innerWidth < 768) {
                setIsSidebarOpen(false);
            }
        };

        // Check initially
        checkIfMobile();

        // Listen for resize events
        window.addEventListener('resize', checkIfMobile);

        // Clean up
        return () => window.removeEventListener('resize', checkIfMobile);
    }, []);

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    // Simulate loading data
    useEffect(() => {
        const fetchData = async () => {
            try {
                // API Integration placeholder - replace with your actual API call
                // const response = await fetchLiveSessions();
                // setData(response.data);
                
                // For now, using dummy data with timeout to simulate API delay
                await new Promise(resolve => setTimeout(resolve, 800));
                setData(liveSessions);
                setLoading(false);
            } catch (error) {
                console.error('Error fetching sessions:', error);
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Helper function to parse date string
    const parseDate = (dateString: string) => {
        return parseISO(dateString);
    };

    // Handle search input changes
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setSearchText(e.target.value);
    };

    // Filter data based on search text and selected status
    const filteredData = data.filter(session => {
        const matchesSearch = (
            session.title.toLowerCase().includes(searchText.toLowerCase()) ||
            session.courseCode.toLowerCase().includes(searchText.toLowerCase()) ||
            session.instructorName.toLowerCase().includes(searchText.toLowerCase()) ||
            session.programName.toLowerCase().includes(searchText.toLowerCase())
        );

        const matchesStatus = selectedStatus ? session.status === selectedStatus : true;

        return matchesSearch && matchesStatus;
    });

    // Handler for ending a session
    const handleEndSession = (sessionId: string) => {
        // API Integration placeholder - replace with your actual API call
        // await endSession(sessionId);
        
        const updatedData = data.map(item =>
            item.id === sessionId ? { ...item, status: 'completed' as 'completed' } : item
        );
        setData(updatedData);
    };

    // View session details
    const handleViewDetails = (session: LiveSessionData) => {
        setSelectedSession(session);
        setIsViewModalVisible(true);
    };

    // Edit session
    const handleEditSession = (session: LiveSessionData) => {
        setSelectedSession(session);
        setIsEditModalVisible(true);
    };

    // Start session
    const handleStartSessionClick = (session: LiveSessionData) => {
        setSelectedSession(session);
        setIsStartModalVisible(true);
    };

    // Cancel session
    const handleCancelSessionClick = (session: LiveSessionData) => {
        setSelectedSession(session);
        setIsCancelModalVisible(true);
    };

    // Update session data after actions
    const updateSessionData = (updatedSession: LiveSessionData) => {
        // API Integration placeholder - replace with your actual API call
        // await updateSession(updatedSession);
        
        setData(prevData => 
            prevData.map(item => item.id === updatedSession.id ? updatedSession : item)
        );
    };

    // Action menu for each row
    const getActionMenu = (record: LiveSessionData): MenuProps => ({
        items: [
            {
                key: '1',
                label: 'View Details',
                onClick: () => handleViewDetails(record)
            },
            {
                key: '2',
                label: 'Edit Session',
                disabled: record.status === 'completed' || record.status === 'cancelled',
                onClick: () => handleEditSession(record)
            },
            record.status === 'scheduled' ? {
                key: '3',
                label: 'Start Session',
                onClick: () => handleStartSessionClick(record)
            } : null,
            record.status === 'in-progress' ? {
                key: '4',
                label: 'End Session',
                onClick: () => handleEndSession(record.id)
            } : null,
            record.status === 'scheduled' ? {
                key: '5',
                label: 'Cancel Session',
                danger: true,
                onClick: () => handleCancelSessionClick(record)
            } : null,
        ].filter(Boolean) as MenuProps['items']
    });

    // Status filter options
    const statusFilters = [
        { text: 'All', value: null },
        { text: 'Scheduled', value: 'scheduled' },
        { text: 'In Progress', value: 'in-progress' },
        { text: 'Completed', value: 'completed' },
        { text: 'Cancelled', value: 'cancelled' },
    ];

    // Get columns with actions and handlers
    const columns = renderSessionColumns({
        parseDate,
        handleViewDetails,  // Correctly passes the view handler
        handleStartSession: handleStartSessionClick,  // Correctly passes the start handler
        handleEndSession,   // Correctly passes the end handler
        getActionMenu       // Correctly passes the menu handler
    }).map(column => ({
        ...column,
        responsive: column.responsive as Breakpoint[] | undefined
    }));

    return (
        <div className="flex h-screen">
            {/* Sidebar */}
            {/* <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} /> */}

            <div className="flex flex-col flex-grow">
                {/* Top Bar */}
                {/* <TopBar toggleSidebar={toggleSidebar} /> */}

                {/* Main Content */}
                <div className="p-3 md:p-6 overflow-auto">
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 md:mb-6 gap-3 md:gap-0">
                            <h1 className="text-xl md:text-2xl font-light text-gray-800">Live Sessions</h1>
                              <motion.div
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                      onClick={() => setIsSessionModalOpen(true)}
                                    >
                                      <ButtonTemplate
                                        className="!px-4 !py-2 text-sm font-semibold shadow-md hover:shadow-lg transition-all duration-300"
                                        label="New Session"
                                        icon={<VideoCameraAddOutlined className="text-base" />}
                                      />
                                    </motion.div>
                                    <SessionModal isOpen={isSessionModalOpen} onClose={() => setIsSessionModalOpen(false)} />
                        </div>

                        <div className="bg-white rounded-lg shadow">
                            <div className="p-3 md:p-4 border-b">
                                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-4">
                                    <div className="w-full md:max-w-md">
                                        <InputTemplate
                                            placeHolder="Search sessions..."
                                            prefix={<SearchOutlined />}
                                            className="w-full"
                                            value={searchText}
                                            onChange={handleSearchChange}
                                            allowClear
                                            label=""
                                            fieldName="search"
                                            aria-label="Search sessions"
                                        />
                                    </div>

                                    <div className="w-full md:w-auto overflow-x-auto pb-1 md:pb-0">
                                        <div className="flex flex-nowrap md:flex-wrap items-center">
                                            <span className="text-gray-500 mr-2 whitespace-nowrap font-light">Status:</span>
                                            {statusFilters.map(filter => (
                                                <Tag
                                                    key={filter.value || 'all'}
                                                    color={selectedStatus === filter.value ? 'blue' : 'default'}
                                                    className="cursor-pointer px-2 py-0.5 whitespace-nowrap mr-2 mb-1 font-light"
                                                    onClick={() => setSelectedStatus(filter.value)}
                                                    role="button"
                                                    aria-pressed={selectedStatus === filter.value}
                                                    tabIndex={0}
                                                    onKeyPress={(e) => {
                                                        if (e.key === 'Enter' || e.key === ' ') {
                                                            setSelectedStatus(filter.value);
                                                        }
                                                    }}
                                                >
                                                    {filter.text}
                                                </Tag>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <AnimatePresence>
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="overflow-x-auto">
                                        <Table
                                            columns={columns}
                                            dataSource={filteredData}
                                            rowKey="id"
                                            loading={loading}
                                            pagination={{
                                                pageSize: 5,
                                                showSizeChanger: true,
                                                pageSizeOptions: ['5', '10', '20'],
                                                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} sessions`,
                                                responsive: true
                                            }}
                                            expandable={{
                                                expandedRowRender: record => (
                                                    <div className="py-2 px-4">
                                                        <p className="text-gray-500 text-sm font-light mb-2">{record.description}</p>
                                                        {record.materials && (
                                                            <div className="flex flex-wrap gap-2 mt-2">
                                                                <span className="text-gray-500 text-sm font-light">Materials:</span>
                                                                {record.materials.map((material, index) => (
                                                                    <Tag key={index} color="blue" className="font-light">{material}</Tag>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                ),
                                            }}
                                            className="ant-table-custom"
                                            scroll={{ x: 'max-content' }}
                                        />
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </div>
                    </motion.div>

                    {/* View Session Modal - Only render when visible */}
                    {selectedSession && isViewModalVisible && (
                        <ViewSession
                            session={selectedSession}
                            isVisible={isViewModalVisible}
                            onClose={() => setIsViewModalVisible(false)}
                            onStartSession={(session) => {
                                const updated = { ...session, status: 'in-progress' as const };
                                updateSessionData(updated);
                                setIsViewModalVisible(false);
                            }}
                            parseDate={parseDate}
                        />
                    )}

                    {/* Edit Session Modal - Only render when visible */}
                    {selectedSession && isEditModalVisible && (
                        <EditSession
                            session={selectedSession}
                            isVisible={isEditModalVisible}
                            onSave={(updatedSession) => {
                                updateSessionData(updatedSession);
                                setIsEditModalVisible(false);
                            }}
                            onCancel={() => setIsEditModalVisible(false)}
                        />
                    )}

                    {/* Start Session Modal - Only render when visible
                    {selectedSession && isStartModalVisible && (
                        <StartSession
                            session={selectedSession}
                            onConfirm={(session) => {
                                const updated = { ...session, status: 'in-progress' as const };
                                updateSessionData(updated);
                                setIsStartModalVisible(false);
                            }}
                            onCancel={() => setIsStartModalVisible(false)}
                        />
                    )} */}

                    {/* Cancel Session Modal - Only render when visible */}
                    {selectedSession && isCancelModalVisible && (
                        <CancelSession
                            session={selectedSession}
                            onConfirm={(session) => {
                                const updated = { ...session, status: 'cancelled' as const };
                                updateSessionData(updated);
                                setIsCancelModalVisible(false);
                            }}
                            onCancel={() => setIsCancelModalVisible(false)}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default LiveSessionPage;