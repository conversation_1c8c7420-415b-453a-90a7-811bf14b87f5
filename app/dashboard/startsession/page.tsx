'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, DatePicker, TimePicker, Select, Switch, Button } from 'antd';
import { VideoCameraOutlined, FileOutlined, TeamOutlined, ClockCircleOutlined, CalendarOutlined, AppstoreOutlined, ReloadOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import ButtonTemplate from '@/components/ui/button-template';
import InputTemplate from '@/components/ui/input-template';
import { LiveSessionData } from '../dummydata/livesessiondata';
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import Sidebar from '@/app/dashboard/sidebar';
import TopBar from '@/components/general/dashboard/sidebar/topbar';

const { TextArea } = Input;
const { Option } = Select;

type CreateSessionFormProps = {
  onSubmit: (sessionData: Partial<LiveSessionData>) => void;
  onCancel: () => void;
  isLoading?: boolean;
};

const CreateSessionForm: React.FC<CreateSessionFormProps> = ({ 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const [form] = Form.useForm();
  const [materialsList, setMaterialsList] = useState<string[]>([]);
  const [materialInput, setMaterialInput] = useState<string>('');
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isRecurring, setIsRecurring] = useState(false);
  
  const { isDark } = useTeacherTheme();
  const themeStyles = getThemeStyles(isDark);
  
  // Fixed useEffect implementation
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  
  // Programs and courses could come from API in a real application
  const programOptions = [
    { value: 'CSC', label: 'Computer Science' },
    { value: 'BUS', label: 'Business Administration' },
    { value: 'ENG', label: 'Engineering' },
    { value: 'MED', label: 'Medical Sciences' },
  ];
  
  const courseOptions = [
    { value: 'CSC101', label: 'Introduction to Programming' },
    { value: 'CSC202', label: 'Data Structures' },
    { value: 'BUS301', label: 'Marketing Principles' },
    { value: 'ENG150', label: 'Physics for Engineers' },
    { value: 'MED220', label: 'Human Anatomy' },
  ];

  const sessionTypeOptions = [
    { value: 'lecture', label: 'Lecture' },
    { value: 'tutorial', label: 'Tutorial' },
    { value: 'workshop', label: 'Workshop' },
    { value: 'seminar', label: 'Seminar' },
    { value: 'exam', label: 'Exam' },
  ];

  const recurringOptions = [
    { value: 'none', label: 'None' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'biweekly', label: 'Bi-weekly' },
    { value: 'monthly', label: 'Monthly' },
  ];

  const handleAddMaterial = () => {
    if (materialInput.trim() && !materialsList.includes(materialInput.trim())) {
      setMaterialsList([...materialsList, materialInput.trim()]);
      setMaterialInput('');
    }
  };

  const handleRemoveMaterial = (material: string) => {
    setMaterialsList(materialsList.filter(item => item !== material));
  };

  const handleRecurringChange = (value: boolean) => {
    setIsRecurring(value);
    if (!value) {
      form.setFieldsValue({ recurringPattern: 'none' });
    }
  };

  const handleSubmit = (values: any) => {
    // Format the date and time values
    const startDateTime = new Date(
      values.sessionDate.year(),
      values.sessionDate.month(),
      values.sessionDate.date(),
      values.sessionTime.hour(),
      values.sessionTime.minute()
    ).toISOString();
    
    // Calculate end time (assume 1 hour duration as default)
    const endDateTime = new Date(
      values.sessionDate.year(),
      values.sessionDate.month(),
      values.sessionDate.date(),
      values.sessionTime.hour() + (values.duration || 1),
      values.sessionTime.minute()
    ).toISOString();

    // Create a new session object
    const newSession: Partial<LiveSessionData> = {
      title: values.title,
      courseCode: values.courseCode,
      programName: values.program,
      instructorName: values.instructor,
      description: values.description,
      startTime: startDateTime,
      endTime: endDateTime,
      status: 'scheduled',
      materials: materialsList,
      isRecorded: values.isRecorded,
      attendees: 0, // Will be populated when students join
      duration: values.duration || 1,
      sessionType: values.sessionType,
      isRecurring: values.isRecurring,
      recurringPattern: values.isRecurring ? values.recurringPattern || 'none': 'none',
    };

    onSubmit(newSession);
  };

  const formItemAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.5,
        ease: "easeOut"
      }
    })
  };

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />

      <div className="flex flex-col flex-grow">
        {/* Top Bar */}
        <TopBar toggleSidebar={toggleSidebar} />

        {/* Main Content */}
        <div className="p-3 md:p-6 overflow-auto">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6"
          >
            <h2 className="text-2xl font-light text-gray-800 mb-6">Create New Live Session</h2>
            
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              requiredMark={false}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.div 
                  custom={0} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="title"
                    label="Session Title"
                    rules={[{ required: true, message: 'Please enter session title' }]}
                  >
                    <InputTemplate
                      fieldName="title"
                      label=""
                      prefix={<VideoCameraOutlined />}
                      placeHolder="Enter session title"
                    />
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={1} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="instructor"
                    label="Instructor Name"
                    rules={[{ required: true, message: 'Please enter instructor name' }]}
                  >
                    <InputTemplate
                      fieldName="instructor"
                      label=""
                      prefix={<TeamOutlined />}
                      placeHolder="Enter instructor name"
                    />
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={2} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="program"
                    label="Program"
                    rules={[{ required: true, message: 'Please select a program' }]}
                  >
                    <Select 
                      placeholder="Select program" 
                      className="w-full"
                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    >
                      {programOptions.map(option => (
                        <Option key={option.value} value={option.value}>{option.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={3} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="courseCode"
                    label="Course"
                    rules={[{ required: true, message: 'Please select a course' }]}
                  >
                    <Select 
                      placeholder="Select course" 
                      className="w-full"
                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    >
                      {courseOptions.map(option => (
                        <Option key={option.value} value={option.value}>{option.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={4} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="sessionDate"
                    label="Date"
                    rules={[{ required: true, message: 'Please select session date' }]}
                  >
                    <DatePicker 
                      className="w-full" 
                      format="YYYY-MM-DD" 
                      placeholder="Select date"
                      suffixIcon={<CalendarOutlined />}
                    />
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={5} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="sessionTime"
                    label="Start Time"
                    rules={[{ required: true, message: 'Please select start time' }]}
                  >
                    <TimePicker 
                      className="w-full" 
                      format="HH:mm" 
                      placeholder="Select start time"
                      suffixIcon={<ClockCircleOutlined />}
                    />
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={6} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="duration"
                    label="Duration (hours)"
                    rules={[{ required: true, message: 'Please enter duration' }]}
                  >
                    <InputTemplate
                      fieldName="duration"
                      label=""
                      prefix={<ClockCircleOutlined />}
                      placeHolder="Enter duration in hours"
                      type="number"
                      min={0.5}
                      step={0.5}
                    />
                  </Form.Item>
                </motion.div>

                <motion.div 
                  custom={7} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item
                    name="sessionType"
                    label="Session Type"
                    rules={[{ required: true, message: 'Please select session type' }]}
                  >
                    <Select 
                      placeholder="Select session type" 
                      className="w-full"
                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                      suffixIcon={<AppstoreOutlined />}
                    >
                      {sessionTypeOptions.map(option => (
                        <Option key={option.value} value={option.value}>{option.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </motion.div>

                <motion.div 
                  custom={8} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item 
                    name="isRecurring" 
                    label="Recurring Session" 
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch onChange={handleRecurringChange} />
                  </Form.Item>
                </motion.div>

                {isRecurring && (
                  <motion.div 
                    custom={9} 
                    initial="hidden" 
                    animate="visible" 
                    variants={formItemAnimation}
                  >
                    <Form.Item
                      name="recurringPattern"
                      label="Recurring Pattern"
                      rules={[{ required: isRecurring, message: 'Please select recurring pattern' }]}
                    >
                      <Select 
                        placeholder="Select recurring pattern" 
                        className="w-full"
                        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                        suffixIcon={<ReloadOutlined />}
                      >
                        {recurringOptions.map(option => (
                          <Option key={option.value} value={option.value}>{option.label}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </motion.div>
                )}
                
                <motion.div 
                  custom={10} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                  className="md:col-span-2"
                >
                  <Form.Item
                    name="description"
                    label="Session Description"
                    rules={[{ required: true, message: 'Please enter session description' }]}
                  >
                    <TextArea 
                      rows={4} 
                      placeholder="Enter session description" 
                      className="w-full rounded"
                    />
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={11} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                  className="md:col-span-2"
                >
                  <Form.Item label="Session Materials">
                    <div className="flex flex-wrap items-center gap-2">
                      <InputTemplate
                        fieldName="materialInput"
                        value={materialInput}
                        onChange={(e) => setMaterialInput(e.target.value)}
                        prefix={<FileOutlined />}
                        placeHolder="Add material (e.g., PDF, Slides)"
                        className="flex-grow" label={''}                        // onPressEnter={handleAddMaterial}
                      />
                      <Button 
                        type="default" 
                        className="bg-teal-500 text-white hover:bg-teal-600 border-none"
                        onClick={handleAddMaterial}
                      >
                        Add
                      </Button>
                    </div>
                    
                    {materialsList.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {materialsList.map((material, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            className="bg-blue-50 text-blue-800 rounded-full px-3 py-1 text-sm flex items-center gap-2"
                          >
                            <span>{material}</span>
                            <button 
                              onClick={() => handleRemoveMaterial(material)}
                              className="text-blue-500 hover:text-blue-700 focus:outline-none"
                            >
                              ×
                            </button>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </Form.Item>
                </motion.div>
                
                <motion.div 
                  custom={12} 
                  initial="hidden" 
                  animate="visible" 
                  variants={formItemAnimation}
                >
                  <Form.Item 
                    name="isRecorded" 
                    label="Record Session" 
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </motion.div>
              </div>
              
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="flex justify-end space-x-4 pt-4"
              >
                <Button 
                  onClick={onCancel} 
                  disabled={isLoading}
                  className="px-6"
                >
                  Cancel
                </Button>
                <ButtonTemplate
                  htmlType="submit"
                  loading={isLoading}
                  className="!px-6 !py-2"
                  label="Create Session"
                  icon={<VideoCameraOutlined />}
                />
              </motion.div>
            </Form>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Page component that uses the form
const StartSessionPage = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (sessionData: Partial<LiveSessionData>) => {
    setIsLoading(true);
    try {
      // Here you would typically submit the data to your API
      console.log('Session data to submit:', sessionData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message and redirect
      alert('Session created successfully!');
      router.push('/dashboard');
    } catch (error) {
      console.error('Error creating session:', error);
      alert('Failed to create session. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Navigate back to dashboard
    router.push('/dashboard');
  };

  return (
    <CreateSessionForm 
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isLoading={isLoading}
    />
  );
};

export default StartSessionPage;