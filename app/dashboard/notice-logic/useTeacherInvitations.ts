import { useState, useEffect, useCallback, useRef } from 'react';
import { useApi } from '@/hooks/useRequest';
import { useNotification } from '@/hooks/useNotifs';

interface GroupInfo { 
  name: string; 
  description?: string 
}

export interface TeacherInvitation {
  id: string;
  chat_group_id: string;
  status: string;
  created_at: string;
  group_name?: string;
  message?: string;
  group_description?: string;
}

interface UseTeacherInvitationsReturn {
  loading: boolean;
  invitations: TeacherInvitation[];
  currentInvitations: TeacherInvitation[];
  currentPage: number;
  totalPages: number;
  handlePageChange: (page: number) => void;
  respondToInvitation: (invitationId: string, chatGroupId: string, accept: boolean) => Promise<boolean>;
  refetch: () => Promise<void>;
}

export const useTeacherInvitations = (): UseTeacherInvitationsReturn => {
  const { request } = useApi();
  const { showNotification } = useNotification();

  const [loading, setLoading] = useState<boolean>(false);
  const [invitations, setInvitations] = useState<TeacherInvitation[]>([]);
  const [groups, setGroups] = useState<Record<string, GroupInfo>>({});
  const isMounted = useRef(true);

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 4;

  // Fetch group information
  const fetchGroups = useCallback(async (): Promise<Record<string, GroupInfo>> => {
    try {
      const response = await request("GET", "/chat-group/", null, "application/json");
      if (response?.data?.data?.chat_groups) {
        const groupsMap: Record<string, GroupInfo> = response.data.data.chat_groups.reduce((acc: Record<string, GroupInfo>, group: any) => {
          acc[group.id] = { 
            name: (group.name || group.group_name || group.title || 'Unnamed Group'), 
            description: group.description || group.group_description || '' 
          };
          return acc;
        }, {});
        return groupsMap;
      }
      return {};
    } catch (error) {
      console.error('Error fetching groups:', error);
      return {};
    }
  }, [request]);

  // Fetch invitations from the notice board endpoint
  const fetchInvitations = useCallback(async () => {
    if (!isMounted.current) return;
    
    setLoading(true);
    try {
      const [noticeResponse, groupsMap] = await Promise.all([
        request('GET', '/protected/notice-board', null, 'application/json'),
        fetchGroups()
      ]);

      if (!isMounted.current) return;

      if (noticeResponse?.status === 200) {
        const data = noticeResponse.data?.data || {};
        let mergedGroupsMap = { ...groupsMap };
        const invitationsRaw = (data.chat_group_invitations || []) as TeacherInvitation[];

        // Fetch any missing group details
        const missingIds = invitationsRaw
          .filter(inv => !mergedGroupsMap[inv.chat_group_id])
          .map(inv => inv.chat_group_id);

        if (missingIds.length) {
          try {
            const fetched = await Promise.all(
              missingIds.map(id => request("GET", `/chat-group/${id}`, null, "application/json"))
            );
            
            fetched.forEach(resp => {
              if (resp?.status === 200 && resp.data?.data?.chat_group) {
                const g = resp.data.data.chat_group;
                mergedGroupsMap[g.id] = {
                  name: g.name || 'Group',
                  description: g.description || ''
                };
              }
            });
            
            setGroups(prev => ({ ...prev, ...mergedGroupsMap }));
          } catch (err) {
            console.error('Failed to fetch missing group information', err);
          }
        }

        // Enrich invitations with group data
        const enrichedInvitations = invitationsRaw.map((invite: any) => ({
          ...invite,
          group_name: mergedGroupsMap[invite.chat_group_id]?.name || 
                     (invite.message?.match(/Join my group\s+\"?([^\"]+)\"?/i)?.[1] || 'Group'),
          group_description: mergedGroupsMap[invite.chat_group_id]?.description || '',
          message: invite.message || ''
        }));

        setInvitations(enrichedInvitations);
        setCurrentPage(1);
      } else {
        showNotification('error', 'Error', 'Failed to load invitations');
        setInvitations([]);
      }
    } catch (err) {
      console.error('Error fetching invitations:', err);
      showNotification('error', 'Error', 'Failed to load invitations');
      setInvitations([]);
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [request]);

  // Respond to invitation using the chat group invitation endpoint
  const respondToInvitation = useCallback(async (invitationId: string, chatGroupId: string, accept: boolean) => {
    try {
      const response = await request(
        'POST',
        `/chat-group/${chatGroupId}/invitations/${accept ? 'accept' : 'reject'}`,
        null,
        'application/json'
      );

      if (response?.status === 200) {
        showNotification(
          'success',
          accept ? 'Invitation Accepted' : 'Invitation Declined',
          accept ? 'You have joined the group successfully!' : 'Invitation has been declined.'
        );
        // Refresh the list after responding
        await fetchInvitations();
        return true;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Unable to process your request');
        return false;
      }
    } catch (err) {
      console.error('Failed to respond to invitation', err);
      showNotification('error', 'Error', 'Network error. Please try again');
      return false;
    }
  }, [request, fetchInvitations]);

  // Fetch invitations on mount only
  useEffect(() => {
    isMounted.current = true;
    fetchInvitations();

    return () => {
      isMounted.current = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Pagination helpers
  const totalPages = Math.max(1, Math.ceil(invitations.length / ITEMS_PER_PAGE));
  const currentInvitations = invitations.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    loading,
    invitations,
    currentInvitations,
    currentPage,
    totalPages,
    handlePageChange,
    respondToInvitation,
    refetch: fetchInvitations
  };
};
