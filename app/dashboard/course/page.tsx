'use client';
import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Pagination, App, Switch, Upload, Spin } from 'antd';
import { SearchOutlined, EditOutlined, EyeOutlined, DeleteOutlined, BellOutlined, InboxOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
// Import components
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import InputTemplate from "@/components/ui/input-template";
import ButtonTemplate from "@/components/ui/button-template";
import EditModalTemplate from "@/components/ui/edit-modal-template";
import ViewModalTemplate from "@/components/ui/view-modal-template";
import DeleteModalTemplate from "@/components/ui/delete-templates";
import TextAreaTemplate from '@/components/ui/TextArea-template';
import SelectTemplate from '@/components/ui/select-template';
import '../../../styles/globals.css';
import { useUserStore } from '@/store/userStore';
import { useLecturerStore } from '@/store/lecturerStore';
import UploadTemplate from '@/components/ui/upload-template';
import { Loader } from '@/components/general/loader';


// Import course logic
import { useCourseLogic, CourseType } from '@/logics/course';
import { useNotification } from '@/hooks/useNotifs';
import { useApi } from '@/hooks/useRequest';
import NotFoundPage from '@/components/general/not-found';

const CourseTable = () => {

  const { message } = App.useApp();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [form] = Form.useForm();
  // Sidebar state is not used in this component but kept for consistency
  const [, _setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const { user } = useUserStore();
  const { Lecturer } = useLecturerStore();
  // Function to format topics as an object with week keys
  const formatTopicsAsObject = (topicsString: string) => {
    const topicsArray = topicsString.split(',').map(topic => topic.trim());
    const topicsObject: Record<string, string> = {};

    topicsArray.forEach((topic, index) => {
      topicsObject[`week${index + 1}`] = topic;
    });

    return topicsObject;
  };


  // Get course logic from the hook
  const {
    // Course data and state
    courses,
    setCourses,
    filteredCourses,
    selectedCourse,
    fetchTeacherCourses,
    editCourse,
    deleteCourse,
    tokenExpired,

    // Search and pagination
    searchText,
    setSearchText,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,

    // Modal visibility
    isAddModalVisible,
    setIsAddModalVisible,
    isEditModalVisible,
    setIsEditModalVisible,
    isViewModalVisible,
    setIsViewModalVisible,
    isDeleteModalVisible,
    setIsDeleteModalVisible,

    // Image handling (used in the course logic)
    imageUrl,
    setImageUrl,

    // Description character counter
    descriptionChars,
    setDescriptionChars,
    handleDescriptionChange,

    // Course operations
    createCourse,
    openEditModal,
    openViewModal,
    openDeleteModal,

    // Loading state
    loading,
  } = useCourseLogic();

  // Enhanced responsive detection for all devices
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 640);
      setIsTablet(width >= 640 && width < 1024);

      if (width < 768) {
        _setIsSidebarOpen(false);
      } else if (width >= 1024) {
        _setIsSidebarOpen(true);
      }
    };

    // Check initially
    handleResize();

    // Listen for resize events
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize isPublished state from form value when the modal opens
  useEffect(() => {
    if (isAddModalVisible) {
      const initialPublishValue = form.getFieldValue('is_published') || false;
      setIsPublished(initialPublishValue);
    }
  }, [isAddModalVisible, form]);

  // Fetch courses when component mounts or pagination changes
  useEffect(() => {
    fetchTeacherCourses(currentPage, pageSize);
  }, [currentPage, pageSize]);

  // Custom wrapper functions to handle modal state
  const handleEditCourseWithModal = async (values: any) => {
    if (!selectedCourse || !selectedCourse.id) {
      console.error('No course selected or course ID is missing');
      return;
    }

    const result = await editCourse(selectedCourse.id, values);
    if (result !== null) {
      setIsEditModalVisible(false);
    }
  };

  const handleDeleteCourseWithModal = async () => {
    if (!selectedCourse || !selectedCourse.id) {
      console.error('No course selected or course ID is missing');
      showNotification('error', 'Error', 'No course selected or course ID is missing');
      return;
    }

    try {
      // Close the modal immediately before starting the delete operation
      setIsDeleteModalVisible(false);

      // The spinner notification is shown in the deleteCourse function
      const result = await deleteCourse(selectedCourse.id);

      if (!result && tokenExpired) {
        // If token expired, the not-found page will be shown automatically
        // due to the tokenExpired state being set in the deleteCourse function
        console.log('Token expired during course deletion');
      }
    } catch (error) {
      console.error('Error in handleDeleteCourseWithModal:', error);
      showNotification('error', 'Error', 'An unexpected error occurred while deleting the course');
    }
  };

  // Handle publish toggle
  const handlePublishToggle = async (courseId: string, currentStatus: boolean) => {
    try {
      // Toggle the current status
      const newStatus = !currentStatus;

      console.log(`Toggling publish status for course ${courseId} from ${currentStatus} to ${newStatus}`);

      // Show notification with spinner
      showNotification(
        'info',
        newStatus ? 'Publishing Course' : 'Unpublishing Course',
        newStatus ? 'Publishing your course...' : 'Unpublishing your course...',
        true,
        <Spin />
      );

      try {
        // First, get the current course data to ensure we have all required fields
        const courseResponse = await request(
          "GET",
          `/course/${courseId}`,
          null,
          "application/json"
        );

        if (!courseResponse) {
          destroyNotifications();
          console.error('Failed to fetch course data for publishing: No response');
          showNotification('error', 'Error', 'Failed to fetch course data');
          return;
        }

        // Check for token expiration (401 or 403 status)
        if (courseResponse.status === 401 || courseResponse.status === 403) {
          destroyNotifications();
          console.error('Token expired or unauthorized:', courseResponse.status);
          showNotification('error', 'Session Expired', 'Your session has expired.');
          return;
        }

        // Check for other errors
        if (courseResponse.status !== 200) {
          destroyNotifications();
          console.error('Failed to fetch course data for publishing:', courseResponse?.data);
          showNotification('error', 'Error', 'Failed to fetch course data');
          return;
        }

        // Update only the is_published field
        const updateData = {
          is_published: newStatus
        };

        // Make the API request to update the course
        const response = await request(
          "PUT",
          `/course/${courseId}`,
          updateData,
          "application/json"
        );

        // Clear the notification
        destroyNotifications();

        // Check for token expiration
        if (response && (response.status === 401 || response.status === 403)) {
          console.error('Token expired or unauthorized during update:', response.status);
          showNotification('error', 'Session Expired', 'Your session has expired.');
          return;
        }

        // Check for successful response
        if (response && response.status === 200) {
          // Show success notification
          showNotification(
            'success',
            'Success',
            newStatus ? 'Course published successfully' : 'Course unpublished successfully'
          );

          // Update the course in the local state
          const updatedCourses = courses.map(course => {
            if (course.id === courseId) {
              return {
                ...course,
                is_published: newStatus,
                published: newStatus,
                status: newStatus ? 'Published' : 'Draft'
              };
            }
            return course;
          });

          // Update the courses state
          setCourses(updatedCourses);
        } else {
          // Handle error
          const errorMessage = response?.data?.detail ||
            (newStatus ? 'Failed to publish course' : 'Failed to unpublish course');

          showNotification('error', 'Error', errorMessage);
        }
      } catch (error: any) {
        destroyNotifications();

        // Check if the error is due to token expiration
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          console.error('Token expired or unauthorized:', error.response.status);
          showNotification('error', 'Session Expired', 'Your session has expired.');
        } else {
          const errorMessage = error.message || 'An unexpected error occurred';
          showNotification('error', 'Error', errorMessage);
        }
      }
    } catch (error) {
      destroyNotifications();
      console.error('Error in handlePublishToggle:', error);
      showNotification('error', 'Error', 'An unexpected error occurred while updating the course status');
    }
  };



  // Greeting logic for the UI
  let greeting;
  const hour = new Date().getHours();
  if (hour < 12) {
    greeting = 'Good Morning';
  } else if (hour === 12) {
    greeting = 'Good Afternoon';
  } else {
    greeting = 'Good Evening';
  }

  const { isDark } = useTeacherTheme();
  const themeStyles = getThemeStyles(isDark);



  // Show not found page if token is expired
  if (tokenExpired) {
    return <NotFoundPage code="401" value="Your session has expired. Please sign in again." />;
  }

  return (
    <div className="flex flex-col bg-[#F9FAFB]">

      <div className="flex flex-1">

        <main className="flex-1" style={{ backgroundColor: themeStyles.bgSecondary }}>
          {/* Secondary Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white shadow-sm px-3 sm:px-4 md:px-6 py-3 md:py-4 sticky top-0 z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-2"
            style={{ backgroundColor: themeStyles.bgHeader }}
          >
            <div className="flex items-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                className="mr-2 text-white"
              >
                <BellOutlined style={{ fontSize: '18px' }} />
              </motion.div>
              <h1 className="text-lg font-bold text-white">
                {greeting}, <span className="text-white">
                  {user ?
                    `${user.first_name} ${user.last_name}` :
                    (Lecturer && Lecturer.name ? Lecturer.name : 'lecturer')
                  }
                </span> 👋
              </h1>
            </div>
          </motion.header>
          <div className="p-3 sm:p-4 md:p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 md:mb-6">
              <h1 className="text-xl sm:text-2xl font-bold mb-3 md:mb-0"></h1>
              <div className="flex items-center w-full md:w-auto">
                <div className="w-64 mr-3">
                  <InputTemplate
                    inputType="text"
                    fieldName="searchCourses"
                    label=""
                    placeHolder="Search courses"
                    prefix={<SearchOutlined />}
                    outerClassName="mb-0"
                    className="font-regular-text-bold"
                    required={false}
                    value={searchText}
                    onChange={(e: any) => setSearchText(e.target.value)}
                  />
                </div>
                <div>
                  <ButtonTemplate
                    label="Add a course"
                    icon={<span className="mr-2 flex items-center">+</span>}
                    handleClick={() => {
                      form.resetFields();
                      setDescriptionChars(500);
                      setIsPublished(false);
                      setImageUrl(null);
                      setIsAddModalVisible(true);
                    }}
                    className="text-black hover:!bg-teal-600 whitespace-nowrap mb-6"
                  />
                </div>
              </div>
            </div>

            {loading ? (
              <Loader />
            ) : (
              <div className="bg-white rounded-lg shadow">
                {!courses || courses.length === 0 ? (
                  <div className="flex flex-col justify-center items-center py-20 text-black">
                    <div className="text-5xl mb-4">📚</div>
                    <p className="text-lg font-medium mb-2">No courses found</p>
                    <p className="text-sm">Click "Add a course" to create your first course</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="bg-white border-b">
                        <th className="py-3 px-2 sm:px-4 text-left">
                          <input type="checkbox" className="w-4 h-4" />
                        </th>
                        <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Course Code</th>
                        <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Course Title</th>
                        {!isMobile && !isTablet && (
                          <>
                            <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Course Level</th>
                            <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Duration</th>
                            <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Price</th>
                            <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Status</th>
                          </>
                        )}
                        <th className="py-3 px-2 sm:px-4 text-left text-xs sm:text-sm font-medium text-black">Actions</th>
                      </tr>
                    </thead>

                    <tbody>
                      {/* Use the courses directly from the API response - pagination is handled server-side */}
                      {filteredCourses.map((course) => (
                          <tr key={course.id} className="border-b hover:bg-gray-50">
                            <td className="py-2 sm:py-3 px-2 sm:px-4">
                              <input type="checkbox" className="w-4 h-4" />
                            </td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">{course.code}</td>
                            <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">
                              {isMobile ? course.name?.substring(0, 15) + (course.name?.length > 15 ? '...' : '') : course.name}
                            </td>
                            {!isMobile && !isTablet && (
                              <>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">{course.level}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">
                                  {course.cohort_duration_weeks ?
                                    `${course.cohort_duration_weeks} weeks` :
                                    (course.duration ?
                                      `${course.duration} ${Number(course.duration) === 1 ? 'week' : 'weeks'}` :
                                      'N/A')
                                  }
                                </td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">${course.base_price || 'N/A'}</td>
                                <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm text-gray-900">
                                  <span className={`px-2 py-1 rounded-full text-xs ${course.is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                    {course.is_published ? 'Published' : 'Draft'}
                                  </span>
                                </td>
                              </>
                            )}
                            <td className="py-2 sm:py-3 px-2 sm:px-4">
                              <div className="flex items-center space-x-1 sm:space-x-2">
                                <button
                                  className="p-1 rounded-full bg-teal-100 text-black hover:bg-teal-200 flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8"
                                  onClick={() => openEditModal(course)}
                                  title="Edit Course"
                                >
                                  <EditOutlined style={{ fontSize: '14px' }} />
                                </button>
                                <button
                                  className="p-1 rounded-full bg-blue-100 text-black  hover:bg-blue-200 flex items-center justify-center w-6 h-6"
                                  onClick={() => openViewModal(course)}
                                  title="View Course"
                                >
                                  <EyeOutlined style={{ fontSize: '14px' }} />
                                </button>
                                <button
                                  className="p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8"
                                  onClick={() => openDeleteModal(course)}
                                  title="Delete Course"
                                >
                                  <DeleteOutlined style={{ fontSize: '14px' }} />
                                </button>
                                <div className="ml-2 flex items-center" title={course.is_published ? "Published - Click to unpublish" : "Draft - Click to publish"}>
                                  <Switch
                                    size="default"
                                    checked={course.is_published || false}
                                    onChange={() => handlePublishToggle(course.id, course.is_published || false)}
                                    className={course.is_published ? "bg-teal-600" : "bg-gray-300"}
                                    checkedChildren="Published"
                                    unCheckedChildren="Draft"
                                    style={{
                                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                      minWidth: '85px'
                                    }}
                                  />
                                </div>
                              </div>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                )}

                {/* Ant Design Pagination - only show when there are courses */}
                {courses && courses.length > 0 && (
                  <div className="flex justify-center py-4 border-t">
                    <Pagination
                      current={currentPage}
                      pageSize={pageSize}
                      // Use the total count from the API response if available, otherwise use the filtered courses length
                      // This ensures we're using server-side pagination when possible
                      total={filteredCourses.length}
                      onChange={(page) => {
                        // When page changes, fetch the new page of courses
                        setCurrentPage(page);
                        // fetchTeacherCourses is called in the useEffect when currentPage changes
                      }}
                      showSizeChanger
                      onShowSizeChange={(_, size) => {
                        // When page size changes, reset to page 1 and update the page size
                        setCurrentPage(1);
                        setPageSize(size);
                        // fetchTeacherCourses is called in the useEffect when pageSize changes
                      }}
                      pageSizeOptions={['5', '10', '20', '50']}
                      showTotal={(total, range) => (
                        <span className="hidden sm:inline">{`${range[0]}-${range[1]} of ${total} items`}</span>
                      )}
                      size={isMobile ? "small" : "default"}
                      className="text-xs sm:text-sm"
                    />
                  </div>
                )}
              </div>
            )}
          </div>




          {/* Add Course Modal */}
          <Modal
            title={
              <div className="flex justify-between items-center">
                <span className="text-teal-600 font-semibold text-lg">Add Course</span>
                <Button
                  type="text"
                  onClick={() => {
                    setDescriptionChars(500);
                    setIsPublished(false);
                    setImageUrl(null);
                    form.resetFields();
                    setIsAddModalVisible(false);
                  }}
                  className="flex items-center justify-center"
                />
              </div>
            }
            open={isAddModalVisible}
            onCancel={() => {
              setDescriptionChars(500);
              setIsPublished(false);
              setImageUrl(null);
              form.resetFields();
              setIsAddModalVisible(false);
            }}
            footer={null}
            className="top-8"
            width="90%"
            style={{ maxWidth: '800px' }}
          >
            <Form form={form} layout="vertical" onFinish={(values: any) => {
              // Clear any previous errors
              try {
                // Check if cover_image exists and is valid
                console.log('Form values:', values);
                if (!values.cover_image || values.cover_image.length === 0) {
                  message.error('Please upload a course cover image');
                  return;
                }

                // Log the cover_image for debugging
                console.log('Cover image:', values.cover_image);

                // Prepare the course data
                const courseData = {
                  name: values.name,
                  code: values.code,
                  credits: parseInt(values.credits),
                  description: values.description,
                  base_price: parseFloat(values.base_price),
                  topics: values.topics ? formatTopicsAsObject(values.topics) : {},
                  duration: parseInt(values.duration),
                  level: values.level,
                  category: values.category,
                  tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
                  cover_image: values.cover_image,
                  has_cohorts: values.has_cohortss === 'true',
                  has_curriculum: values.has_curriculum === 'true',
                  is_self_paced: values.is_self_paced === 'true',
                  cohort_duration_weeks: parseInt(values.cohort_duration_weeks),
                  max_students_per_cohort: parseInt(values.max_students_per_cohort),
                  auto_create_cohorts: values.auto_create_cohorts === 'true',
                  days_between_cohorts: parseInt(values.days_between_cohorts),
                  // Ensure is_published is always included, even if it's false
                  is_published: values.is_published === true || values.is_published === 'true'
                };

                // Disable the form to prevent multiple submissions
                form.setFields([{ name: '_disabled', value: true }]);
                // Call the createCourse function directly
                createCourse(courseData).then(result => {

                  // Re-enable the form
                  form.setFields([{ name: '_disabled', value: false }]);

                  if (result && result.success) {
                    // Close modal and reset form
                    setIsAddModalVisible(false);
                    form.resetFields();
                    setDescriptionChars(500);
                    setIsPublished(false);
                    setImageUrl(null);
                  } else {
                    // Show error message if result is not successful
                    message.error(result?.error || 'Failed to create course. Please try again.');
                  }
                }).catch(() => {
                  // Re-enable the form in case of error
                  form.setFields([{ name: '_disabled', value: false }]);
                  message.error('An error occurred. Please try again.');
                });
              } catch (error) {
                message.error('Failed to create course. Please try again.');
              }
            }}>
              {/* Course Image Upload */}
              <div className="mb-6">
                <Form.Item
                  name="cover_image"
                  label={<span className="font-medium text-gray-700">Course Cover Image</span>}
                  rules={[{
                    required: true,
                    message: 'Please upload a course cover image',
                    validator: (_) => {
                      if (imageUrl) {
                        return Promise.resolve();
                      }
                      return Promise.reject('Please upload a course cover image');
                    }
                  }]}
                  valuePropName="fileList"
                  getValueFromEvent={(e) => {
                    if (Array.isArray(e)) {
                      return e;
                    }
                    return e?.fileList;
                  }}
                >
                  <div className="flex justify-center items-center w-full">
                    <Upload
                      name="cover_image"
                      accept="image/*"
                      showUploadList={false}
                      customRequest={({ onSuccess }) => {
                        // This prevents the default upload behavior
                        setTimeout(() => {
                          onSuccess && onSuccess("ok");
                        }, 0);
                      }}
                      onChange={(info) => {
                        if (info.file.originFileObj) {
                          // Set the form field value directly
                          form.setFieldsValue({
                            cover_image: [info.file]
                          });

                          // Create a preview URL
                          const reader = new FileReader();
                          reader.onload = (event) => {
                            setImageUrl(event.target?.result as string);
                          };
                          reader.readAsDataURL(info.file.originFileObj);
                        }
                      }}
                    >
                      {imageUrl ? (
                        <div className="border border-dashed border-teal-300 rounded-md p-6 flex flex-col items-center justify-center hover:border-teal-500 transition-colors duration-200 w-64 h-40">
                          <img src={imageUrl} alt="Course Cover" className="max-h-full max-w-full object-contain" />
                          <div className="mt-2 text-sm text-gray-500">Change your cover image</div>
                        </div>
                      ) : (
                        <div className="border border-dashed border-teal-300 rounded-md p-6 flex flex-col items-center justify-center hover:border-teal-500 transition-colors duration-200 w-64 h-40">
                          <div className="text-2xl mb-1 text-gray-400">+</div>
                          <div className="text-sm text-gray-500">Upload Image</div>
                        </div>
                      )}
                    </Upload>
                  </div>
                </Form.Item>
              </div>

              {/* using flex to align fields side by side */}
              <div className="space-y-6">
                {/* Basic Course Information Section */}
                <div className="mb-2">
                  <h3 className="text-gray-700 font-medium mb-4">Basic Course Information</h3>
                </div>

                {/* Row 1: Name and Code */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="name"
                    label={<span className="font-medium text-gray-700">Course Name</span>}
                    rules={[{ required: true, message: 'Please enter course name' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'name'} className="font-regular-text text-black course-name-placeholder" />
                  </Form.Item>

                  <Form.Item
                    name="code"
                    label={<span className="font-medium text-gray-700">Course Code</span>}
                    rules={[{ required: true, message: 'Please enter course code' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'code'} className="font-regular-text text-black" />
                  </Form.Item>
                </div>

                {/* Row 2: Credits and Base Price */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="credits"
                    label={<span className="font-medium text-gray-700">Credits</span>}
                    rules={[{ required: true, message: 'Please enter credits' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'credits'} className="font-regular-text text-black" />
                  </Form.Item>

                  <Form.Item
                    name="base_price"
                    label={<span className="font-medium text-gray-700">Base Price</span>}
                    rules={[{ required: true, message: 'Please enter base price' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'base_price'} className="font-regular-text text-black" />
                  </Form.Item>
                </div>

                {/* Row 3: Duration and Topics */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="duration"
                    label={<span className="font-medium text-gray-700">Duration (days)</span>}
                    rules={[{ required: true, message: 'Please enter duration' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'duration'} className="font-regular-text text-black" />
                  </Form.Item>

                  <Form.Item
                    name="topics"
                    label={<span className="font-medium text-gray-700">Course topics</span>}
                    rules={[{ required: true, message: 'Please enter topics' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate
                      placeHolder='Enter topics separated by commas'
                      label={''}
                      fieldName={'topics'}
                      className="font-regular-text text-black"
                    />
                  </Form.Item>
                </div>

                {/* Row 4: Level and Category */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="level"
                    label={<span className="font-medium text-gray-700">Course Level</span>}
                    rules={[{ required: true, message: 'Please select level' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <SelectTemplate
                      placeHolder="Select Course Level"
                      options={[
                        { value: 'high_school', label: 'High School' },
                        { value: 'undergraduate', label: 'Undergraduate' },
                        { value: '100', label: 'Level 100' },
                        { value: '200', label: 'Level 200' },
                        { value: '300', label: 'Level 300' },
                        { value: '400', label: 'Level 400' },
                        { value: 'masters', label: 'Masters' },
                        { value: 'phd', label: 'PhD' },
                        { value: 'other', label: 'Other' }
                      ]}
                      label={''}
                      fieldName="level"
                      className="font-regular-text text-black"
                    />
                  </Form.Item>

                  <Form.Item
                    name="category"
                    label={<span className="font-medium text-gray-700">Category</span>}
                    rules={[{ required: true, message: 'Please enter category' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'category'} className="font-regular-text text-black" />
                  </Form.Item>
                </div>


                {/* Row 5: Description - Full Width */}
                <div className="flex flex-col gap-4">
                  <Form.Item
                    name="description"
                    label={<span className="font-medium text-gray-700">Description</span>}
                    rules={[{ required: true, message: 'Please enter description' }]}
                    className="mb-0 w-full"
                  >
                    <div className="relative">
                      <TextAreaTemplate
                        // placeHolder="A comprehensive introduction to Python programming language covering basic syntax and concepts"
                        label={''}
                        fieldName="description"
                        className="font-regular-text text-black"
                        rows={4}
                        maxLength={500}
                        onChange={handleDescriptionChange}
                      />
                      {/* Character counter that shows remaining characters */}
                      <span className="text-gray-500 text-xs absolute bottom-2 right-4">
                        <span id="char-counter">{descriptionChars}</span> characters left
                      </span>
                    </div>
                  </Form.Item>
                </div>

                {/* Row 6: Publish course */}
                <div className="flex flex-col md:flex-row gap-4 items-center">
                  <Form.Item
                    name="is_published"
                    label={<span className="font-medium text-gray-700">Publish course</span>}
                    className="mb-0 w-full md:w-1/2"
                    initialValue={false}
                  >
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={isPublished}
                        onChange={(checked: boolean) => {
                          form.setFieldsValue({ is_published: checked });
                          // Force re-render by updating state
                          setIsPublished(checked);
                        }}
                        className={isPublished ? "bg-teal-600" : "bg-gray-300"}
                        checkedChildren="Published"
                        unCheckedChildren="Draft"
                        style={{
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                          minWidth: '85px'
                        }}
                      />
                      <span className="text-sm text-gray-600 ml-2">
                        {isPublished ? 'Visible to students' : 'Not visible to students'}
                      </span>
                    </div>
                  </Form.Item>
                  <div className="w-full md:w-1/2 text-sm text-gray-500 mt-1 md:mt-6">
                    <p>Published courses are immediately visible to students</p>
                  </div>
                </div>

                {/* Add some extra spacing */}
                <div className="mb-4"></div>

                {/* Cohort Settings Section */}
                <div className="border-t pt-4 mt-2 mb-2">
                  <h3 className="text-gray-700 font-medium mb-4">Cohort Settings</h3>
                </div>

                {/* Row 6: Cohort Settings */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="has_cohortss"
                    label={<span className="font-medium text-gray-700">Has Cohort</span>}
                    rules={[{ required: true, message: 'Please select if course has cohorts' }]}
                    className="mb-0 w-full md:w-1/3"
                  >
                    <SelectTemplate
                      placeHolder="Select"
                      options={[
                        { value: 'false', label: 'False' },
                        { value: 'true', label: 'True' }
                      ]}
                      label={''}
                      fieldName="has_cohortss"
                      className="font-regular-text text-black"
                    />
                  </Form.Item>

                  <Form.Item
                    name="has_curriculum"
                    label={<span className="font-medium text-gray-700">Has Curriculum</span>}
                    rules={[{ required: true, message: 'Please select if course has curriculum' }]}
                    className="mb-0 w-full md:w-1/3"
                  >
                    <SelectTemplate
                      placeHolder="Select"
                      options={[
                        { value: 'false', label: 'False' },
                        { value: 'true', label: 'True' }
                      ]}
                      label={''}
                      fieldName="has_curriculum"
                      className="font-regular-text text-black"
                    />
                  </Form.Item>

                  <Form.Item
                    name="is_self_paced"
                    label={<span className="font-medium text-gray-700">Is Self Paced</span>}
                    rules={[{ required: true, message: 'Please select if course is self-paced' }]}
                    className="mb-0 w-full md:w-1/3"
                  >
                    <SelectTemplate
                      placeHolder="Select"
                      options={[
                        { value: 'false', label: 'False' },
                        { value: 'true', label: 'True' }
                      ]}
                      label={''}
                      fieldName="is_self_paced"
                      className="font-regular-text text-black"
                    />
                  </Form.Item>
                </div>

                {/* Row 7: Cohort Details */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="cohort_duration_weeks"
                    label={<span className="font-medium text-gray-700">Cohort Duration (weeks)</span>}
                    rules={[{ required: true, message: 'Please enter cohort duration' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate  label={''} fieldName={'cohort_duration_weeks'} className="font-regular-text text-black" />
                  </Form.Item>

                  <Form.Item
                    name="max_students_per_cohort"
                    label={<span className="font-medium text-gray-700">Max Students Per Cohort</span>}
                    rules={[{ required: true, message: 'Please enter max students' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate label={''} fieldName={'max_students_per_cohort'} className="font-regular-text text-black" />
                  </Form.Item>
                </div>

                {/* Row 8: Auto Create Cohorts and Days Between Cohorts */}
                <div className="flex flex-col md:flex-row gap-4">
                  <Form.Item
                    name="auto_create_cohorts"
                    label={<span className="font-medium text-gray-700">Auto Create Cohorts</span>}
                    rules={[{ required: true, message: 'Please select if cohorts are auto-created' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <SelectTemplate
                      placeHolder="Select"
                      options={[
                        { value: 'false', label: 'False' },
                        { value: 'true', label: 'True' }
                      ]}
                      label={''}
                      fieldName="auto_create_cohorts"
                      className="font-regular-text text-black"
                    />
                  </Form.Item>

                  <Form.Item
                    name="days_between_cohorts"
                    label={<span className="font-medium text-gray-700">Days Between Cohorts</span>}
                    rules={[{ required: true, message: 'Please enter days between cohorts' }]}
                    className="mb-0 w-full md:w-1/2"
                  >
                    <InputTemplate placeHolder="31" label={''} fieldName={'days_between_cohorts'} className="font-regular-text text-black" />
                  </Form.Item>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-end mt-8 gap-2">
                <ButtonTemplate
                  label="Cancel"
                  handleClick={() => {
                    setDescriptionChars(500);
                    setIsPublished(false);
                    setImageUrl(null);
                    form.resetFields();
                    setIsAddModalVisible(false);
                  }}
                  className="border border-gray-300 hover:bg-gray-100 text-gray-700 w-full sm:w-24 text-sm py-1"
                  disabled={loading}
                />
                <Button
                  type="primary"
                  htmlType="submit"
                  className="bg-teal-600 hover:bg-teal-700 text-white w-full sm:w-32 text-sm py-1 mt-1 sm:mt-0"
                  loading={loading}
                >
                  Add Course
                </Button>
              </div>
            </Form>
          </Modal>
          {/* Edit Course Modal */}
          <EditModalTemplate
            isVisible={isEditModalVisible}
            onCancel={() => setIsEditModalVisible(false)}
            onSubmit={handleEditCourseWithModal}
            item={selectedCourse}
            loading={loading}
          />

          {/* View Course Modal */}
          <ViewModalTemplate
            isVisible={isViewModalVisible}
            onCancel={() => setIsViewModalVisible(false)}
            item={selectedCourse}
          />

          {/* Delete Course Modal */}
          <DeleteModalTemplate
            isVisible={isDeleteModalVisible}
            onCancel={() => setIsDeleteModalVisible(false)}
            onConfirm={handleDeleteCourseWithModal}
            item={selectedCourse}
            loading={loading}
          />
        </main>
      </div >
    </div >
  );
};

const CourseTableWithApp = () => (
  <App>
    <CourseTable />
  </App>
);

export default CourseTableWithApp;
