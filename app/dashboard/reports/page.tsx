'use client';

import React, { useEffect, useState } from 'react';
import { Table, Input, Button, Select, Tag } from 'antd';
import { SearchOutlined, ExportOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

import Sidebar from '@/app/dashboard/sidebar';
import TopBar from '@/components/general/dashboard/sidebar/topbar';
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

interface StudentReport {
  key: string;
  name: string;
  enrolledCourses: string;
  totalLessonsCompleted: string;
  attendance: string;
  quizPerformance: string;
  status: 'Active' | 'Inactive' | 'Suspended';
}

const ReportsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'students' | 'courses' | 'lessons'>('students');
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  const { isDark } = useTeacherTheme();
  const themeStyles = getThemeStyles(isDark);

  // Check if screen is mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Sample data generation
  const generateStudentData = (): StudentReport[] => {
    return Array(30).fill(null).map((_, index) => ({
      key: `student-${index}`,
      name: 'A. Judas Barnabas',
      enrolledCourses: 'Cloud computing',
      totalLessonsCompleted: '29/30',
      attendance: '15/16',
      quizPerformance: '45/50',
      status: 'Active'
    }));
  };

  const studentData = generateStudentData();
  
  // Filter data based on search text
  const filteredData = studentData.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase())
  );

  // Pagination settings
  const paginatedData = filteredData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const totalPages = Math.ceil(filteredData.length / pageSize);

  // Table columns
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Enrolled Courses',
      dataIndex: 'enrolledCourses',
      key: 'enrolledCourses',
    },
    {
      title: 'Total Lessons Completed',
      dataIndex: 'totalLessonsCompleted',
      key: 'totalLessonsCompleted',
    },
    {
      title: 'Attendance (%)',
      dataIndex: 'attendance',
      key: 'attendance',
    },
    {
      title: 'Quiz Performance (%)',
      dataIndex: 'quizPerformance',
      key: 'quizPerformance',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          'Active': 'green',
          'Inactive': 'gray',
          'Suspended': 'red'
        };
        return (
          <Tag color={colorMap[status] || 'default'}>
            {status}
          </Tag>
        );
      },
    },
  ];

  // Animation variants with Framer Motion
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.2 } }
  };

  const handleTabChange = (tabKey: 'students' | 'courses' | 'lessons') => {
    setActiveTab(tabKey);
    setCurrentPage(1);
  };

  // Function to generate page numbers array with ellipsis
  const getPageNumbers = () => {
    const pages = [];
    const maxDisplayedPages = 5;
    
    if (totalPages <= maxDisplayedPages) {
      // Show all pages if there are fewer than max
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate middle pages
      const leftBoundary = Math.max(2, currentPage - 1);
      const rightBoundary = Math.min(totalPages - 1, currentPage + 1);
      
      // Add ellipsis if needed before middle pages
      if (leftBoundary > 2) {
        pages.push('ellipsis1');
      }
      
      // Add middle pages
      for (let i = leftBoundary; i <= rightBoundary; i++) {
        pages.push(i);
      }
      
      // Add ellipsis if needed after middle pages
      if (rightBoundary < totalPages - 1) {
        pages.push('ellipsis2');
      }
      
      // Always show last page
      pages.push(totalPages);
    }
    
    return pages;
  };

  return (
    <div className="flex h-screen">
        <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />
 
      <div className="flex flex-col flex-grow" style={{ backgroundColor: themeStyles.bgSecondary }}>
 
          <TopBar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />


        <motion.main 
          className="flex-1 overflow-y-auto p-6"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div variants={itemVariants} className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-gray-800">Reports</h1>
          </motion.div>

          <motion.div variants={itemVariants} className="bg-white rounded-lg shadow p-6">
            {/* Tab buttons */}
            <div className="flex justify-between items-center mb-6">
              <div className="space-x-2">
              <Button 
            onClick={() => handleTabChange('students')}
            className={activeTab === 'students' ? 'bg-teal-600 hover:bg-teal-700 text-white' : 'hover:bg-teal-100 text-teal-600'}
            >
            Students
            </Button>
                <Button 
                  onClick={() => handleTabChange('courses')}
                  className={activeTab === 'courses' ? 'bg-teal-600 hover:bg-teal-700 text-white' : 'hover:bg-teal-100 text-teal-600'}
                >
                  Courses
                </Button>
                <Button 
                  type={activeTab === 'lessons' ? 'primary' : 'default'}
                  onClick={() => handleTabChange('lessons')}
                  className={activeTab === 'lessons' ? 'bg-blue-600 hover:bg-blue-700' : ''}
                >
                  Lessons
                </Button>
              </div>

              <div className="flex space-x-3">
                <Button 
                  icon={<ExportOutlined />}
                  className="border border-gray-300"
                >
                  Export
                </Button>
                <Input
                  placeholder="Search students..."
                  prefix={<SearchOutlined className="text-gray-400" />}
                  value={searchText}
                  onChange={(e) => {
                    setSearchText(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-64"
                />
              </div>
            </div>

            {/* Table with data */}
            <Table
              dataSource={paginatedData}
              columns={columns}
              pagination={false}
              rowKey="key"
              rowClassName="hover:bg-gray-50"
              size="middle"
              className="border border-gray-200 rounded"
              rowSelection={{
                type: 'checkbox',
                columnWidth: 48,
              }}
            />

            {/* Pagination controls */}
            <div className="flex justify-between items-center mt-6">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Show</span>
                <Select
                  value={pageSize}
                  onChange={(value) => {
                    setPageSize(value);
                    setCurrentPage(1);
                  }}
                  style={{ width: 80 }}
                >
                  <Select.Option value={5}>5</Select.Option>
                  <Select.Option value={10}>10</Select.Option>
                  <Select.Option value={20}>20</Select.Option>
                  <Select.Option value={30}>30</Select.Option>
                  <Select.Option value={50}>50</Select.Option>
                </Select>
                <span className="text-sm text-gray-500">entries per page</span>
              </div>
              
              <div className="text-sm text-gray-500">
                Showing {Math.min(filteredData.length, 1) + (currentPage - 1) * pageSize} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries
              </div>
              
              <div className="flex items-center space-x-1">
                <Button 
                  size="small" 
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  Previous
                </Button>
                
                {getPageNumbers().map((page, index) => {
                  if (page === 'ellipsis1' || page === 'ellipsis2') {
                    return <span key={`${page}-${index}`} className="px-2">...</span>;
                  }
                  
                  return (
                    <Button
                      key={`page-${page}`}
                      size="small"
                      type={currentPage === page ? "primary" : "default"}
                      className={currentPage === page ? "bg-blue-600" : ""}
                      onClick={() => setCurrentPage(Number(page))}
                    >
                      {page}
                    </Button>
                  );
                })}
                
                <Button 
                  size="small" 
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                >
                  Next
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.main>
      </div>
    </div>
  );
};

export default ReportsPage;