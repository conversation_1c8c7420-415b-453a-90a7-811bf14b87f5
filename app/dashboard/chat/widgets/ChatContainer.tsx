'use client';

import React from 'react';
import { ChatSidebar } from './ChatSidebar';
import { ChatMessageArea } from './ChatMessageArea';
import { ChatMessageInput } from './ChatMessageInput';
import { ChatProvider, useChatContext } from '../_logics/chat_context';
import { useLecturerStore } from '@/store/lecturerStore';

export const ChatContainer = () => {
  const { Lecturer } = useLecturerStore();
  
  // Format user data for ChatProvider
  const chatUser = Lecturer ? {
    id: Lecturer.id,
    name: `${Lecturer.first_name} ${Lecturer.last_name}`.trim() || 'Lecturer',
    type: 'lecturer',
    email: Lecturer.email,
    avatar: Lecturer.profile_image_path || ''
  } : undefined;

  return (
    <ChatProvider user={chatUser}>
      <ChatContent />
    </ChatProvider>
  );
};

// Separate component to access ChatContext
const ChatContent = () => {
  const chatContext = useChatContext();
  const hasActiveRoom = !!chatContext?.state.activeRoomId;

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat main container */}
      <div className="flex flex-1 overflow-hidden">
        <ChatSidebar />
        <div className="flex-1 flex flex-col">
          <ChatMessageArea />
          {hasActiveRoom && <ChatMessageInput />}
        </div>
      </div>
    </div>
  );
};
