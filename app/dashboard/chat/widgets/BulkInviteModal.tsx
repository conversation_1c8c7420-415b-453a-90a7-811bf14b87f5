'use client';

import React, { useEffect, useState } from 'react';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { useNotification } from '@/hooks/useNotifs';
import { useChatApiService } from '../_logics/chat_api_service';
import { BulkInviteModalProps, UserSearchResult } from '../_logics/chat_types';

export const BulkInviteModal: React.FC<BulkInviteModalProps> = ({
  isOpen,
  onClose,
  groupId,
  groupName,
}) => {
  const { isDark } = useTeacherTheme();
  const { showNotification } = useNotification();
  const { sendChatGroupBulkInvite, searchStudents } = useChatApiService();

  const [query, setQuery] = useState('');
  const [results, setResults] = useState<UserSearchResult[]>([]);
  const [selected, setSelected] = useState<UserSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [inviteMessage, setInviteMessage] = useState(`Join my group "${groupName}" on LearnKonnect!`);

  const validateSearchQuery = (input: string): boolean => {
    const validPattern = /^[a-zA-Z0-9\s.@\-_]*$/;
    return validPattern.test(input);
  };

  const validateMessage = (input: string): boolean => {
    const trimmed = input.trim();
    if (trimmed.length === 0 || trimmed.length > 200) return false;
    const invalidPattern = /[<>]/;
    return !invalidPattern.test(trimmed);
  };

  const handleSearch = async (q: string) => {
    setQuery(q);
    if (!q.trim()) {
      setResults([]);
      return;
    }
    
    if (!validateSearchQuery(q)) {
      showNotification('error', 'Please use only letters, numbers, and common symbols');
      return;
    }
    
    if (q.trim().length < 2) {
      showNotification('info', 'Please enter at least 2 characters to search');
      return;
    }
    
    setSearching(true);
    try {
      const students = await searchStudents(q, 1, 20, { sort_by: q });
      const formatted: UserSearchResult[] = students.map((stu: any) => ({
        id: stu.id || stu._id,
        _id: stu._id || stu.id,
        name: stu.name ?? `${stu.first_name ?? ''} ${stu.last_name ?? ''}`.trim(),
        email: stu.email ?? 'N/A',
        type: 'student',
        user_type: 'student',
      }));
      setResults(formatted);
    } catch (e) {
      console.error(e);
      showNotification('error', 'Search failed');
    } finally {
      setSearching(false);
    }
  };

  const toggleSelect = (user: UserSearchResult) => {
    setSelected((prev) => {
      const exists = prev.find((u) => u.id === user.id);
      if (exists) return prev.filter((u) => u.id !== user.id);
      return [...prev, user];
    });
  };

  const handleInvite = async () => {
    if (selected.length === 0) {
      showNotification('warning', 'Please select at least one user to invite');
      return;
    }
    
    if (!validateMessage(inviteMessage)) {
      showNotification('error', 'Please enter a valid invitation message (1-200 characters, no HTML tags)');
      return;
    }
    
    setLoading(true);
    try {
      // Prepare bulk invitation data - only include required fields
      const invitations = selected.map(user => ({
        invitee_id: user.id,
        invitee_type: user.user_type || 'student'
      }));
      
      console.log('Sending bulk invite with data:', {
        groupId,
        invitations,
        message: inviteMessage
      });
      
      // Send bulk invitation
      const success = await sendChatGroupBulkInvite(
        groupId, 
        invitations, 
        inviteMessage
      );
      
      if (success) {
        showNotification('success', `Invites sent to ${selected.length} user${selected.length > 1 ? 's' : ''}`);
        // Reset form state
        setSelected([]);
        setQuery('');
        setResults([]);
        onClose();
      } else {
        showNotification('error', 'Failed to send invites. Please try again.');
      }
    } catch (e) {
      console.error('Bulk invite error:', e);
      showNotification('error', 'Failed to send invites. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setQuery('');
      setResults([]);
      setSelected([]);
      setInviteMessage(`Join my group "${groupName}" on LearnKonnect!`);
    }
  }, [isOpen, groupName]);

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 overflow-y-auto ${isDark ? 'bg-black/70' : 'bg-black/50'}`} onClick={onClose}>
      <div className="flex items-center justify-center min-h-screen p-4" onClick={(e) => e.stopPropagation()}>
        <div className={`w-full max-w-lg rounded-lg p-6 ${isDark ? 'bg-[#1E2430]' : 'bg-white'}`}>
          <div className="flex justify-between items-center mb-4">
            <h2 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Invite to {groupName}
            </h2>
            {selected.length > 0 && (
              <span className={`px-2 py-1 text-xs rounded-full ${isDark ? 'bg-teal-900 text-teal-200' : 'bg-teal-100 text-teal-800'}`}>
                {selected.length} selected
              </span>
            )}
          </div>

          {/* Search input */}
          <div className="relative mb-4">
            <input
              className={`w-full px-3 py-2 rounded-md border text-sm ${isDark ? 'bg-[#2D3440] border-[#384058] text-white' : 'bg-gray-50 border-gray-300 text-gray-900'}`}
              placeholder="Search users…"
              value={query}
              onChange={(e) => handleSearch(e.target.value)}
              disabled={loading}
            />
            {searching && <div className="absolute right-3 top-2.5 animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full" />}
          </div>

          {/* Message input */}
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              Invitation Message
            </label>
            <textarea
              rows={3}
              maxLength={200}
              className={`w-full px-3 py-2 rounded-md border text-sm resize-none ${
                isDark
                  ? 'bg-[#2D3440] border-[#384058] text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } ${!validateMessage(inviteMessage) && inviteMessage.length > 0 ? 'border-red-500' : ''}`}
              placeholder="Enter a message to include with your invitation..."
              value={inviteMessage}
              onChange={(e) => {
                if (e.target.value.length <= 200) {
                  setInviteMessage(e.target.value);
                }
              }}
              disabled={loading}
            />
            <div className={`text-right text-xs mt-1 ${
              inviteMessage.length > 180 ? 'text-orange-500' : 'text-gray-400'
            }`}>
              {inviteMessage.length}/200 characters
            </div>
          </div>

          {/* Selected users preview */}
          {selected.length > 0 && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Selected Users ({selected.length})
                </h3>
                <button
                  onClick={() => setSelected([])}
                  className="text-xs text-red-500 hover:text-red-400 disabled:opacity-50"
                  disabled={loading}
                >
                  Clear All
                </button>
              </div>
              <div className={`max-h-32 overflow-y-auto p-2 rounded-md border border-dashed mb-4 ${
                isDark ? 'border-gray-600' : 'border-gray-300'
              }`}>
                {selected.map((user) => (
                  <div 
                    key={`selected-${user.id}`} 
                    className={`flex items-center justify-between p-2 rounded mb-1 last:mb-0 ${
                      isDark ? 'hover:bg-gray-700/30' : 'hover:bg-gray-100'
                    }`}
                  >
                    <div>
                      <span className={`block text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                        {user.name}
                      </span>
                      <span className="text-xs text-gray-400">{user.email}</span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSelect(user);
                      }}
                      className="text-red-500 hover:text-red-400 disabled:opacity-50"
                      disabled={loading}
                    >
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Search results */}
          <div className="max-h-64 overflow-y-auto">
            {results.length > 0 ? (
              <ul className="space-y-2">
                {results.map((user) => {
                  const isSelected = selected.some((u) => u.id === user.id);
                  return (
                    <li key={user.id}>
                      <button
                        className={`w-full text-left p-2 rounded-md flex items-center justify-between transition-colors ${
                          isSelected 
                            ? isDark 
                              ? 'bg-teal-900/30 text-teal-200' 
                              : 'bg-teal-100 text-teal-800'
                            : isDark 
                              ? 'hover:bg-gray-700/50 text-white' 
                              : 'hover:bg-gray-100 text-gray-900'
                        }`}
                        onClick={() => toggleSelect(user)}
                        disabled={loading}
                      >
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-xs text-gray-400">{user.email}</div>
                        </div>
                        {isSelected && (
                          <svg className="h-5 w-5 text-teal-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </button>
                    </li>
                  );
                })}
              </ul>
            ) : query ? (
              <div className="text-center py-4 text-gray-400">
                {searching ? 'Searching...' : 'No users found'}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-400">
                Start typing to search for users
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                isDark
                  ? 'text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
              }`}
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              className={`px-4 py-2 rounded-md text-sm font-medium text-white transition-colors ${
                selected.length === 0 || loading || !validateMessage(inviteMessage)
                  ? 'bg-teal-400 cursor-not-allowed'
                  : 'bg-teal-600 hover:bg-teal-700'
              }`}
              onClick={handleInvite}
              disabled={selected.length === 0 || loading || !validateMessage(inviteMessage)}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Sending...
                </div>
              ) : (
                `Invite ${selected.length > 0 ? `(${selected.length})` : ''}`
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};