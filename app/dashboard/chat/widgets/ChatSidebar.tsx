'use client';

import React, { useState, useEffect } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { format } from 'date-fns';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { CreateChatGroupModal } from './CreateChatGroupModal';


export const ChatSidebar = () => {
  const { state, setActiveRoom, searchChats, refreshChatRooms, fetchRoomMembers } = useChatContext();
  const [searchTerm, setSearchTerm] = useState('');
  const { isDark } = useTeacherTheme();
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);

  const [activeTab, setActiveTab] = useState<'all' | 'group'>('all');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchTerm(query);
    searchChats(query);
  };

  const handleTabClick = (tab: 'all' | 'group') => {
    setActiveTab(tab);
    setSearchTerm('');
    searchChats('');
  };

  const formatTime = (date: Date | string) => {
    if (!date) return '';
    
    try {
      // Convert to Date object if it's not already one
      const messageDate = date instanceof Date ? date : new Date(date);
      
      // Check if the date is valid
      if (isNaN(messageDate.getTime())) {
        return '';
      }
      
      // If it's today, just show the time
      if (messageDate.toDateString() === new Date().toDateString()) {
        return format(messageDate, 'h:mm a');
      }
      
      // If it's within the last week, show the day name
      const daysDiff = Math.floor((new Date().getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff < 7) {
        return format(messageDate, 'EEE');
      }
      
      // Otherwise show the date
      return format(messageDate, 'MMM d');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleModalSuccess = async () => {
    // Refresh chat rooms after successful member operations
    await refreshChatRooms();
  };

  return (
    <div className={`w-[280px] flex-shrink-0 border-r ${isDark ? 'border-[#384058] bg-[#252B42]' : 'border-gray-200 bg-white'} h-full flex flex-col`}>
      <div className={`px-3 py-3 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex items-center">
          <img src="/logo.png" alt="LearnKonnect" className="w-6 h-6 mr-2" />
          <div className="flex flex-col">
            <div className="flex items-center">
              <h2 className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base`}>LearnKonnect</h2>
              <span className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base ml-1`}>community</span>
            </div>
            <p className={`text-xs ${isDark ? 'text-gray-200' : 'text-gray-500'}`}>Communities pages</p>
          </div>
        </div>
      </div>
      
      <div className="px-3 py-2 relative">
        <div className="relative flex items-center justify-between">
          <div className="relative text-white w-2/3">
            <input 
              type="text" 
              placeholder="Search..." 
              className={`w-full py-1.5 pl-7 pr-2 ${isDark ? 'bg-[#1e232e] border-[#384058] text-white placeholder-gray-300' : 'bg-gray-100 border-gray-200 text-gray-800 placeholder-gray-500'} border rounded-lg focus:outline-none focus:ring-1 focus:ring-[#006060] text-xs`}
              value={searchTerm}
              onChange={handleSearch}
            />
            <svg 
              className="w-4 h-4 absolute left-2 top-2 text-gray-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
          <button 
            className={`p-1.5 ${isDark ? 'text-white hover:bg-[#1e232e]' : 'text-gray-700 hover:bg-gray-100'} rounded-full`}
            onClick={() => setShowCreateGroupModal(true)}
            title="Create New Group"
          >
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      
      <div className={`flex border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'} mb-2`}>
        <button 
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'all' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('all')}
        >
          All
        </button>
        <button 
          className={`flex-1 py-2 text-sm font-medium ${activeTab === 'group' ? (isDark ? 'text-white border-b-2 border-white' : 'text-[#006060] border-b-2 border-[#006060]') : (isDark ? 'text-gray-300' : 'text-gray-500')}`}
          onClick={() => handleTabClick('group')}
        >
          Groups
        </button>
      </div>
      
      <div className="overflow-y-auto flex-1">
        {state.isLoading && !state.activeRoomId ? (
          <div className="flex items-center justify-center h-24">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#006060]"></div>
          </div>
        ) : state.rooms.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
              No chat rooms available.
            </p>
          </div>
        ) : (
          <>
            <div className="px-3 py-2 relative">
              <h3 className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase`}>
                {activeTab === 'all' ? 'All Chats' : 'Groups'}
              </h3>
            </div>

            {state.isLoading && !state.activeRoomId ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#006060]"></div>
                <span className={`ml-2 text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>Loading chat groups...</span>
              </div>
            ) : state.error ? (
              <div className="px-3 py-4 text-center">
                <p className={`text-sm ${isDark ? 'text-red-400' : 'text-red-600'}`}>{state.error}</p>
                <button
                  onClick={refreshChatRooms}
                  className={`mt-2 text-xs ${isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-500'} underline`}
                >
                  Try again
                </button>
              </div>
            ) : state.rooms.length === 0 ? (
              <div className="px-3 py-4 text-center">
                <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>No chat groups found</p>
                <p className={`text-xs ${isDark ? 'text-gray-500' : 'text-gray-400'} mt-1`}>Create your first chat group to get started</p>
              </div>
            ) : (
              state.rooms
                .filter(room => activeTab === 'all' || room.isGroup)
                .map((room) => (
              <div 
                key={room.id}
                className={`flex items-center p-3 cursor-pointer transition-colors duration-200 ${isDark ? 
                  (state.activeRoomId === room.id ? 'bg-[#1e232e]' : 'hover:bg-[#1e232e]') : 
                  (state.activeRoomId === room.id ? 'bg-gray-100' : 'hover:bg-gray-50')
                } rounded-lg mx-1 my-0.5`}
                onClick={() => setActiveRoom(room.id)}
              >
                <div className="relative">
                  {/* Always use logo for chat avatars instead of course images */}
                  <img 
                    src="/logo.png" 
                    alt={room.name} 
                    className={`w-10 h-10 rounded-full object-cover shadow-md border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}
                  />
                  
                  {/* Unread message count badge */}
                  {room.lastMessage && room.unreadCount > 0 && (
                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                      {room.unreadCount > 9 ? '9+' : room.unreadCount}
                    </div>
                  )}
                </div>
                <div className="ml-3 flex-1 overflow-hidden">
                  <div className="flex justify-between items-center">
                    <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-800'} truncate`}>{room.name}</p>
                    <div className="flex items-center space-x-1">
                      <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} whitespace-nowrap ml-1`}>{formatTime(room.timestamp)}</p>
                    </div>
                  </div>
                  <div className="flex items-center mt-0.5">
                    <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-500'} truncate`}>{room.lastMessage || 'No messages yet'}</p>
                  </div>
                </div>
              </div>
                ))
            )}
          </>
        )}
      </div>
      


      {/* Create Chat Group Modal */}
      <CreateChatGroupModal
        isOpen={showCreateGroupModal}
        onClose={() => setShowCreateGroupModal(false)}
        onSuccess={handleModalSuccess}
      />

      {/* Chat Group Members Modal */}
      {/* Removed members modal */}
    </div>
  );
};
