'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import dynamic from 'next/dynamic';
import { FileIcon, X } from 'lucide-react';

// Importing type for TS checking, but using dynamic import for the component
import type { Theme } from 'emoji-picker-react';

// Define allowed file types and max size (5MB)
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv',
  'text/plain'
];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Dynamically import EmojiPicker to avoid SSR issues
const EmojiPicker = dynamic(() => import('emoji-picker-react'), { ssr: false });

interface FileWithPreview extends File {
  preview: string;
  id: string;
}

export const ChatMessageInput = () => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [attachments, setAttachments] = useState<FileWithPreview[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const { sendMessage } = useChatContext();
  const { isDark } = useTeacherTheme();

  // Handle emoji selection
  const handleEmojiClick = (emojiData: any) => {
    const emoji = emojiData.emoji || (emojiData.srcElement && emojiData.srcElement.innerText) || '';
    setMessage(prevMessage => prevMessage + emoji);
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current && 
        !emojiPickerRef.current.contains(event.target as Node) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      sendMessage(message);
      setMessage('');
      setAttachments([]);
      setShowEmojiPicker(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const validFiles = files.filter(file => ALLOWED_FILE_TYPES.includes(file.type) && file.size <= MAX_FILE_SIZE);
      
      const filePreviews = validFiles.map((file) => {
        const previewUrl = URL.createObjectURL(file);
        return {
          ...file,
          preview: previewUrl,
          name: file.name, // Ensure name is preserved
          size: file.size,
          type: file.type,
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        };
      });
      
      setAttachments(prev => [...prev, ...filePreviews]);
      // Reset the input value to allow selecting the same file again if needed
      e.target.value = '';
    }
  };

  const removeAttachment = (id: string) => {
    setAttachments(attachments.filter(file => file.id !== id));
  };

  return (
    <div className={`${isDark ? 'bg-[#252B42] border-[#384058]' : 'bg-white border-gray-200'} border-t`}>
      {/* File previews */}
      {attachments.length > 0 && (
        <div className={`p-2 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
          <div className="flex flex-wrap gap-2">
            {attachments.map((file) => (
              <div 
                key={file.id}
                className="relative group flex items-center gap-2 p-2 pr-8 rounded-lg bg-opacity-20 bg-[#006060] text-sm"
              >
                <FileIcon className="h-4 w-4 flex-shrink-0" />
                <span className="truncate max-w-[150px]">{file.name}</span>
                <span className="text-xs text-gray-500">
                  {(file.size / 1024).toFixed(1)}KB
                </span>
                <button
                  type="button"
                  onClick={() => removeAttachment(file.id)}
                  className="absolute right-1 p-1 text-gray-400 hover:text-red-500 rounded-full"
                  title="Remove file"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="py-2.5 px-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button
              ref={emojiButtonRef}
              type="button"
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full"
              title="Emoji"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            
            {showEmojiPicker && (
              <div 
                ref={emojiPickerRef}
                className="absolute bottom-12 left-0 z-10" 
              >
                <EmojiPicker 
                  onEmojiClick={handleEmojiClick} 
                  theme={(isDark ? "dark" : "light") as Theme}
                  autoFocusSearch={false}
                />
              </div>
            )}
          </div>
          
          <div className={`flex-1 flex items-center py-2 px-4 border ${isDark ? 
            'bg-[#2d3445] border-[#384058]' : 
            'bg-gray-100 border-gray-200'} rounded-full focus-within:ring-1 focus-within:ring-[#006060]`}>
            <input
              type="text"
              placeholder={attachments.length > 0 ? 'Add a message...' : 'Type a message...'}
              className={`flex-1 bg-transparent border-none p-0 ${isDark ? 
                'text-white placeholder-gray-400' : 
                'text-gray-900 placeholder-gray-500'} focus:outline-none text-sm`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
            
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt"
              className="hidden"
              id="file-upload"
            />
            <label 
              htmlFor="file-upload"
              className="text-gray-400 hover:text-gray-600 ml-1 cursor-pointer p-1 rounded-full"
              title="Attach file"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
            </label>
          </div>
          
          <button
            type="submit"
            disabled={!message.trim() && attachments.length === 0}
            className={`p-2 rounded-full ${(message.trim() || attachments.length > 0) ? 'text-[#006060] hover:bg-gray-100' : 'text-gray-400'} transition-colors`}
            title="Send message"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};
