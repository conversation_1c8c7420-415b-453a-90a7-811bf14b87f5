import { Tag } from 'antd';
import { ReactNode } from 'react';

// In LiveSessionData interface, add maxCapacity as required property
export interface LiveSessionData {
  recurring: boolean;
  id: string;
  title: string;
  courseCode: string;
  programName: string;
  instructorName: string;
  scheduledDate: string;
  startTime: string;
  duration: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  enrolledStudents: number;
  maxCapacity: number;
  materials?: string[];
  description?: string;
  meetingLink?: string;
  endTime: string;
  isRecorded: boolean;
  attendees?: number;
  sessionType: string;
  isRecurring: string;
  recurringPattern?: string; 
}

// renderStudentInfo function
export const renderStudentInfo = (session: LiveSessionData): ReactNode => {
  if (session.status === 'completed') {
    // For completed sessions, show attended vs capacity
    return <span>{session.enrolledStudents} of {session.maxCapacity} students attended</span>;
  } else if (session.status === 'cancelled') {
    // For cancelled sessions, show enrolled vs capacity
    return <span>{session.enrolledStudents} of {session.maxCapacity} students enrolled</span>;
  } else if (session.status === 'in-progress') {
    // For in-progress sessions, just show enrolled count
    return <span>{session.enrolledStudents} students expected</span>;
  } else {
    // For scheduled sessions, just show enrolled count
    return <span>{session.enrolledStudents} students enrolled</span>;
  }
};

// renderDetailedStudentInfo function
export const renderDetailedStudentInfo = (session: LiveSessionData): ReactNode => {
  if (session.status === 'completed') {
    // For completed sessions, show attendance rate
    return <span>{session.enrolledStudents} of {session.maxCapacity} students attended</span>;
  } else if (session.status === 'cancelled') {
    // For cancelled sessions, show enrolled vs capacity
    return <span>{session.enrolledStudents} of {session.maxCapacity} students enrolled</span>;
  } else if (session.status === 'in-progress') {
    // For in-progress sessions, just show expected attendance
    return <span>{session.enrolledStudents} students expected</span>;
  } else {
    // For scheduled sessions, just show enrollment info
    return <span>{session.enrolledStudents} students enrolled</span>;
  }
};
const liveSessions: LiveSessionData[] = [
  {
    id: 'LS-001',
    title: 'Introduction to Quantum Computing',
    courseCode: 'CS-401',
    programName: 'Computer Science',
    instructorName: 'Dr. Freda Elves',
    scheduledDate: '2025-03-20',
    startTime: '10:00 AM',
    duration: '1.5 hours',
    enrolledStudents: 28,
    maxCapacity: 35, // Added maxCapacity
    status: 'scheduled',
    materials: ['Lecture Slides', 'Practice Problems'],
    description: 'Basic principles of quantum computing and its applications in modern technology',
    meetingLink: 'https://meet.example.com/quantum',
    recurring: false,
    endTime: '',
    isRecorded: false,
    sessionType: 'exam',
    isRecurring: "once, twice"
  },
  {
    id: 'LS-002',
    title: 'Advanced Machine Learning Techniques',
    courseCode: 'DS-302',
    programName: 'Data Science',
    instructorName: 'Prof. Freda Elves',
    scheduledDate: '2025-03-19',
    startTime: '2:00 PM',
    duration: '2 hours',
    enrolledStudents: 42,
    maxCapacity: 50,
    status: 'in-progress',
    materials: ['Jupyter Notebook', 'Dataset Files'],
    description: 'Exploring advanced ML algorithms and their implementation',
    meetingLink: 'https://meet.example.com/adv-ml',
    recurring: false,
    endTime: '',
    isRecorded: false,
      sessionType: 'exam',
      isRecurring: "once, twice"
  },
  {
    id: 'LS-003',
    title: 'Financial Accounting Fundamentals',
    courseCode: 'ACC-101',
    programName: 'Business Administration',
    instructorName: 'Prof. Freda Elves',
    scheduledDate: '2025-03-18',
    startTime: '9:00 AM',
    duration: '1 hour',
    enrolledStudents: 35,
    maxCapacity: 40,
    status: 'completed',
    materials: ['Case Studies', 'Practice Exercises'],
    description: 'Understanding basic financial accounting principles and statements',
    recurring: false,
    endTime: '',
    isRecorded: false,
        sessionType: 'exam',
        isRecurring: "once, twice"
  },
  {
    id: 'LS-004',
    title: 'Modern Web Development',
    courseCode: 'WD-201',
    programName: 'Web Technologies',
    instructorName: 'Dr. Freda Elves',
    scheduledDate: '2025-03-21',
    startTime: '3:30 PM',
    duration: '2 hours',
    enrolledStudents: 22,
    maxCapacity: 30,
    status: 'scheduled',
    materials: ['Code Repository', 'Tutorial Videos'],
    description: 'Building responsive web applications with React and Next.js',
    meetingLink: 'https://meet.example.com/webdev',
    recurring: false,
    endTime: '',
    isRecorded: false,
        sessionType: 'exam',
        isRecurring: "once, twice"
  },
  {
    id: 'LS-005',
    title: 'Bioethics and Medical Research',
    courseCode: 'BIO-405',
    programName: 'Biomedical Sciences',
    instructorName: 'Dr. Freda Elves',
    scheduledDate: '2025-03-19',
    startTime: '11:00 AM',
    duration: '1.5 hours',
    enrolledStudents: 15,
    maxCapacity: 25,
    status: 'cancelled',
    description: 'Ethical considerations in modern biomedical research',
    recurring: false,
    endTime: '',
    isRecorded: false,
        sessionType: 'exam',
        isRecurring: "once, twice"
  },
  {
    id: 'LS-006',
    title: 'Environmental Sustainability',
    courseCode: 'ENV-202',
    programName: 'Environmental Science',
    instructorName: 'Prof. Freda Elves',
    scheduledDate: '2025-03-22',
    startTime: '1:00 PM',
    duration: '1.5 hours',
    enrolledStudents: 32,
    maxCapacity: 40,
    status: 'scheduled',
    materials: ['Field Study Guide', 'Research Papers'],
    description: 'Exploring sustainable practices and environmental conservation',
    meetingLink: 'https://meet.example.com/env-sus',
    recurring: false,
    endTime: '',
    isRecorded: false,
        sessionType: 'exam',
        isRecurring: "once, twice"
  },
  {
    id: 'LS-007',
    title: 'Literary Analysis: Shakespeare',
    courseCode: 'LIT-303',
    programName: 'English Literature',
    instructorName: 'Dr. Freda Elves',
    scheduledDate: '2025-03-20',
    startTime: '4:00 PM',
    duration: '2 hours',
    enrolledStudents: 18,
    maxCapacity: 25,
    status: 'scheduled',
    materials: ['Text Excerpts', 'Analysis Framework'],
    description: 'Critical analysis of selected Shakespearean works',
    meetingLink: 'https://meet.example.com/lit-shakespeare',
    recurring: false,
    endTime: '',
    isRecorded: false,
        sessionType: 'exam',
        isRecurring: "once, twice"
  }
];
export default liveSessions;