import { LiveSessionData } from './livesessiondata';

// Types for session settings
export interface SessionSettings {
  recordSession: boolean;
  allowStudentAudio: boolean;
  allowStudentVideo: boolean;
  allowChat: boolean;
  allowScreenShare: boolean;
  waitingRoom: boolean;
}

// Student type for the session
export interface SessionStudent {
  id: string;
  name: string;
  avatar: string;
  status: 'online' | 'offline' | 'away';
  email?: string;
}

// Default session settings
export const defaultSessionSettings: SessionSettings = {
  recordSession: true,
  allowStudentAudio: true,
  allowStudentVideo: false,
  allowChat: true,
  allowScreenShare: false,
  waitingRoom: true,
};

// Mock students data
export const mockStudents: SessionStudent[] = [
  { id: '1', name: '<PERSON>', avatar: '/avatars/student1.png', status: 'online', email: '<EMAIL>' },
  { id: '2', name: '<PERSON>', avatar: '/avatars/student2.png', status: 'online', email: '<EMAIL>' },
  { id: '3', name: '<PERSON>', avatar: '/avatars/student3.png', status: 'away', email: '<EMAIL>' },
  { id: '4', name: 'Jamal Williams', avatar: '/avatars/student4.png', status: 'online', email: '<EMAIL>' },
  { id: '5', name: 'Aisha Patel', avatar: '/avatars/student5.png', status: 'offline', email: '<EMAIL>' },
  { id: '6', name: 'Tyler Zhang', avatar: '/avatars/student6.png', status: 'online', email: '<EMAIL>' },
  { id: '7', name: 'Olivia Brown', avatar: '/avatars/student7.png', status: 'away', email: '<EMAIL>' },
  { id: '8', name: 'Noah Kim', avatar: '/avatars/student8.png', status: 'offline', email: '<EMAIL>' },
];

// Helper function to generate a join link
export const generateSessionJoinLink = (sessionId: string): string => {
  const randomString = Math.random().toString(36).substring(2, 8);
  return `https://learn.example.com/session/${sessionId}/${randomString}`;
};

// Default notification message template
export const getDefaultNotificationMessage = (sessionTitle: string): string => {
  return `Your ${sessionTitle} session is starting soon. Please join using the link provided.`;
};

// Function to fetch session by ID (mock implementation)
export const fetchSessionById = async (sessionId: string): Promise<LiveSessionData | null> => {
  // Simulating API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Import the live sessions data
  const { default: liveSessions } = await import('./livesessiondata');
  
  // Find the requested session
  return liveSessions.find((session: LiveSessionData) => session.id === sessionId) || null;
};