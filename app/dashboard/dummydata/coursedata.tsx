import { ReactNode } from "react";

export interface CourseType {
  [x: string]: any;
  id: string;
  courseCode: string;
  title: string;
  program: string;
  instructor: string;
  level: string;
  duration: string;
  price: string;
  status: string;
  description?: string | ReactNode;
}

export const dummyCourses: CourseType[] = [
  {
    id: '1',
    courseCode: 'CS101',
    title: 'Introduction to Cloud Computing',
    program: 'IT & Software',
    instructor: '<PERSON>',
    level: 'Advanced',
    duration: '8 Weeks',
    price: '$49.99',
    status: 'Published',
    description: 'This course provides an introduction to cloud computing concepts, principles, and services. Students will learn about various cloud deployment models, service models, and key technologies.'
  },
  {
    id: '2',
    courseCode: 'CS102',
    title: 'Web Development Fundamentals',
    program: 'IT & Software',
    instructor: '<PERSON>',
    level: 'Beginner',
    duration: '10 Weeks',
    price: '$39.99',
    status: 'Published',
    description: 'Learn the basics of web development including HTML, CSS, and JavaScript. This course covers responsive design principles and modern web development practices.'
  },
  {
    id: '3',
    courseCode: 'MTH201',
    title: 'Advanced Calculus',
    program: 'Mathematics',
    instructor: '<PERSON>',
    level: 'Advanced',
    duration: '12 Weeks',
    price: '$59.99',
    status: 'Draft',
    description: 'An in-depth exploration of advanced calculus topics including multivariable calculus, vector analysis, and differential equations with practical applications.'
  },
  {
    id: '4',
    courseCode: 'MKT101',
    title: 'Digital Marketing Strategies',
    program: 'Marketing',
    instructor: 'Freda Elves',
    level: 'Intermediate',
    duration: '6 Weeks',
    price: '$44.99',
    status: 'Published',
    description: 'Explore digital marketing channels and techniques including SEO, content marketing, social media marketing, and online advertising campaigns.'
  },
  {
    id: '5',
    courseCode: 'ENG205',
    title: 'Introduction to Robotics',
    program: 'Engineering',
    instructor: 'Freda Elves',
    level: 'Intermediate',
    duration: '9 Weeks',
    price: '$69.99',
    status: 'Published',
    description: 'Learn robotics fundamentals including kinematics, sensor technologies, programming autonomous behaviors, and practical robot design principles.'
  },
  {
    id: '6',
    courseCode: 'BUS302',
    title: 'Business Analytics',
    program: 'Business',
    instructor: 'Lisa Anderson',
    level: 'Advanced',
    duration: '8 Weeks',
    price: '$54.99',
    status: 'Published',
    description: 'Master business analytics tools and techniques to make data-driven decisions. Topics include data visualization, predictive modeling, and business intelligence.'
  },
  {
    id: '7',
    courseCode: 'CS301',
    title: 'Machine Learning Fundamentals',
    program: 'IT & Software',
    instructor: 'David Wilson',
    level: 'Advanced',
    duration: '10 Weeks',
    price: '$79.99',
    status: 'Draft',
    description: 'Introduction to machine learning algorithms, techniques, and applications. Learn to implement supervised and unsupervised learning models.'
  },
  {
    id: '8',
    courseCode: 'MTH101',
    title: 'Statistics for Data Science',
    program: 'Mathematics',
    instructor: 'Emily Brown',
    level: 'Beginner',
    duration: '8 Weeks',
    price: '$49.99',
    status: 'Published',
    description: 'Learn essential statistical concepts and methods for data analysis, including probability, distributions, hypothesis testing, and regression analysis.'
  },
  {
    id: '9',
    courseCode: 'MKT202',
    title: 'Social Media Marketing',
    program: 'Marketing',
    instructor: 'Alex Thompson',
    level: 'Intermediate',
    duration: '5 Weeks',
    price: '$39.99',
    status: 'Published',
    description: 'Master social media marketing strategies across platforms including content planning, audience engagement, analytics, and campaign optimization.'
  },
  {
    id: '10',
    courseCode: 'BUS101',
    title: 'Introduction to Entrepreneurship',
    program: 'Business',
    instructor: 'Robert Davis',
    level: 'Beginner',
    duration: '7 Weeks',
    price: '$44.99',
    status: 'Archived',
    description: 'Learn the fundamentals of launching and growing a successful business, including opportunity identification, business planning, and startup strategies.'
  },
];