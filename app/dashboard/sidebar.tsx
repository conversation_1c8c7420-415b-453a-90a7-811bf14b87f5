'use client';
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DashboardOutlined,
  FileTextOutlined,
  BookOutlined,
  VideoCameraOutlined,
  PlusCircleOutlined,
  Bar<PERSON>hartOutlined,
  AppstoreOutlined,
  SettingOutlined,
  LogoutOutlined,
  CloseOutlined,
  UserOutlined,
  MessageOutlined,
  BellOutlined
} from '@ant-design/icons';
import { ThemeToggler, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { useRouter } from 'next/navigation';
import Cookies from "js-cookie";

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  isMobile: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen, isMobile }) => {
  const { isDark } = useTeacherTheme();
  const router = useRouter();

  const desktopSidebarVariants = {
    open: { width: '240px', transition: { duration: 0.3 } },
    closed: { width: '72px', transition: { duration: 0.3 } }
  };

  const mobileSidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const overlayVariants = {
    open: { opacity: 0.5, display: "block" },
    closed: { opacity: 0, transitionEnd: { display: "none" } }
  };

  const handleNavigation = (path: string) => {
    if (path === "/logout") {
      Cookies.remove("access_token");
      window.location.href = "/auth/signin";
    } else {
      router.push(path);
    }
  };

  const menuItems = [
    { icon: <DashboardOutlined />, text: 'Dashboard', path: '/dashboard' },
    { icon: <BookOutlined />, text: 'Courses', path: '/dashboard/course' },
    { icon: <VideoCameraOutlined />, text: 'Live Sessions', path: '/dashboard/livesession' },
    { icon: <FileTextOutlined />, text: 'Exams', path: '/dashboard/livesession/startsession' },
    { icon: <FileTextOutlined />, text: 'Assessments', path: '/dashboard/assessments' },
    { icon: <AppstoreOutlined />, text: 'Collections', path: '/dashboard/collections' },
    { icon: <BarChartOutlined />, text: 'Reports', path: '/dashboard/reports' },
    { icon: <MessageOutlined />, text: 'Chat', path: '/dashboard/chat' },
    { icon: <BellOutlined />, text: 'Invitations', path: '/dashboard/invitations' },
  ];

  const bottomItems = [
    { icon: <SettingOutlined />, text: 'Settings', path: '/dashboard/settings' },
    { icon: <LogoutOutlined />, text: 'Logout', path: '/logout' },
  ];

  // Theme-based sidebar colors
  const sidebarBgColor = isDark ? 'bg-[#252B42]' : 'bg-[#0A6B6A]';
  const sidebarHoverColor = isDark ? 'hover:bg-[#0b4340]' : 'hover:bg-[#095958]';
  const sidebarBorderColor = isDark ? 'border-[#0b4340]' : 'border-[#095958]';

  // Mobile sidebar with overlay
  if (isMobile) {
    return (
      <>
        {/* Overlay */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="fixed inset-0 bg-black z-40"
              initial="closed"
              animate={isOpen ? "open" : "closed"}
              exit="closed"
              variants={overlayVariants}
              onClick={() => setIsOpen(false)}
            />
          )}
        </AnimatePresence>

        {/* Mobile Sidebar */}
        <AnimatePresence>
          <motion.div
            className={`fixed left-0 top-0 h-full text-white z-50 w-64 shadow-lg ${sidebarBgColor}`}
            variants={mobileSidebarVariants}
            initial="closed"
            animate={isOpen ? "open" : "closed"}
          >
            <div className={`flex items-center p-4 justify-between h-14 border-b ${sidebarBorderColor}`}>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <img src="/logo.png" alt="Logo" />
                </div>
                <span className="font-bold text-lg">LearnKonnect</span>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className={`p-2 ${sidebarHoverColor} rounded-lg transition-colors`}
                aria-label="Close sidebar"
              >
                <CloseOutlined className="text-lg" />
              </button>
            </div>

            <nav className="mt-4 px-2 space-y-1 overflow-y-auto max-h-[calc(100vh-120px)]">
              {menuItems.map((item, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center p-3 rounded-lg cursor-pointer justify-start ${sidebarHoverColor}`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleNavigation(item.path)}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="ml-3 text-sm font-medium">{item.text}</span>
                </motion.div>
              ))}

              {/* Theme toggle button */}
              <motion.div
                className={`flex items-center p-3 rounded-lg cursor-pointer justify-start ${sidebarHoverColor}`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ThemeToggler />
                {/* <span className="ml-3 text-sm font-medium">Toggle Theme</span> */}
              </motion.div>
            </nav>

            <div className="absolute bottom-4 left-0 right-0 px-2 space-y-1">
              {bottomItems.map((item, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center p-3 rounded-lg cursor-pointer justify-start ${sidebarHoverColor}`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleNavigation(item.path)}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="ml-3 text-sm font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </AnimatePresence>
      </>
    );
  }

  // Desktop sidebar
  return (
    <motion.div
      className={`${sidebarBgColor} text-white h-full flex flex-col justify-between sticky top-0`}
      variants={desktopSidebarVariants}
      animate={isOpen ? 'open' : 'closed'}
      initial={isOpen ? 'open' : 'closed'}
    >
      <div>
        <div className={`flex items-center p-4 justify-between h-16 border-b ${sidebarBorderColor}`}>
          {isOpen && (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <img src="/logo.png" alt="Logo" />
              </div>
              <span className="font-bold text-bold">LearnKonnect</span>
            </div>
          )}
        </div>

        <nav className="mt-4 px-2 space-y-1">
          {menuItems.map((item, index) => (
            <motion.div
              key={index}
              className={`flex items-center p-3 rounded-lg cursor-pointer ${isOpen ? 'justify-start' : 'justify-center'} ${sidebarHoverColor}`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => handleNavigation(item.path)}
            >
              <span className="text-xl">{item.icon}</span>
              {isOpen && <span className="ml-3 text-sm">{item.text}</span>}
            </motion.div>
          ))}

          {/* Theme toggle button */}
          <motion.div
            className={`flex items-center p-3 rounded-lg cursor-pointer ${isOpen ? 'justify-start' : 'justify-center'} ${sidebarHoverColor}`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ThemeToggler />
            {isOpen && <span className="ml-3 text-sm"></span>}
          </motion.div>
        </nav>
      </div>

      <div className="mb-4 px-2 space-y-1">
        {bottomItems.map((item, index) => (
          <motion.div
            key={index}
            className={`flex items-center p-3 rounded-lg cursor-pointer ${isOpen ? 'justify-start' : 'justify-center'} ${sidebarHoverColor}`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleNavigation(item.path)}
          >
            <span className="text-xl">{item.icon}</span>
            {isOpen && <span className="ml-3 text-sm">{item.text}</span>}
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default Sidebar;