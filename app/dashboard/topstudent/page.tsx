'use client';
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { GlobalOutlined, ArrowLeftOutlined, BellOutlined } from '@ant-design/icons';

// Import components data
import Sidebar from '@/app/dashboard/sidebar';
import TopBar from '@/components/general/dashboard/sidebar/topbar';
import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

const topstudentPage = () => {
  // Extended data for the detailed view
  const [locations, setLocations] = useState([
    { country: 'Ghana', percentage: '75%', students: 1250, color: 'bg-[#0A6B6A]' },
    { country: 'Nigeria', percentage: '20%', students: 334, color: 'bg-[#F59E0B]' },
    { country: 'Zimbabwe', percentage: '15%', students: 250, color: 'bg-[#3B82F6]' },
    { country: 'Kenya', percentage: '12%', students: 200, color: 'bg-[#34D399]' },
    { country: 'South Africa', percentage: '10%', students: 167, color: 'bg-[#F87171]' },
    { country: 'Egypt', percentage: '8%', students: 133, color: 'bg-[#A78BFA]' },
    { country: 'Ethiopia', percentage: '5%', students: 83, color: 'bg-[#FBBF24]' },
    { country: 'Tanzania', percentage: '3%', students: 50, color: 'bg-[#60A5FA]' }
  ]);

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);


  const { isDark } = useTeacherTheme();
  const themeStyles = getThemeStyles(isDark);




  // Check if screen is mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Greeting logic for the UI
  let greeting;
  const hour = new Date().getHours();
  if (hour < 12) {
    greeting = 'Good Morning';
  } else if (hour === 12) {
    greeting = 'Good Afternoon';
  } else {
    greeting = 'Good Evening';
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />

      {/* Main Content */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${isSidebarOpen && !isMobile ? 'ml' : 'ml-0'}`}>
        {/* Top Bar */}
        <TopBar toggleSidebar={toggleSidebar} />
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white shadow-sm px-4 md:px-6 py-4 sticky top-0 z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-2"
          style={{ backgroundColor: themeStyles.bgHeader }}
        >
          <div className="flex items-center" >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="mr-2 text-white"
            >
              <BellOutlined style={{ fontSize: '18px' }} />
            </motion.div>
            <h1 className="text-lg font-bold text-white">
              {greeting}, <span className="text-white">learnKonnect</span> 👋
            </h1>
          </div>
        </motion.header>


        {/* Page Content */}
        <div className="flex-1 overflow-auto p-4 md:p-6">
          <div className="max-w-6xl mx-auto">


            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6"
            >
              <h1 className="text-2xl md:text-3xl font-bold text-gray-800 flex items-center">
                <GlobalOutlined className="mr-3 text-[#0A6B6A]" />
                Top Student Locations Worldwide
              </h1>
              <p className="text-gray-600 mt-2">
                Detailed breakdown of student distribution across different countries
              </p>
            </motion.div>

            {/* Responsive table */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="bg-white rounded-lg shadow-md overflow-hidden"
            >
              {/* Table header - visible on all devices */}
              <div className="px-4 py-3 bg-gray-50 text-sm font-medium text-gray-700 grid grid-cols-12 gap-4">
                <div className="col-span-1 md:col-span-1"></div>
                <div className="col-span-5 md:col-span-4">Country</div>
                <div className="col-span-3 md:col-span-3 text-center">Students</div>
                <div className="col-span-3 md:col-span-4 text-right pr-4">Percentage</div>
              </div>

              {/* Table rows */}
              <div className="divide-y divide-gray-100">
                {locations.map((location, index) => (
                  <motion.div
                    key={location.country}
                    variants={itemVariants}
                    className="px-4 py-3 grid grid-cols-12 gap-4 hover:bg-gray-50 transition-colors duration-200"
                  >
                    <div className="col-span-1 md:col-span-1 flex items-center justify-center">
                      <motion.div
                        className={`w-3 h-3 rounded-full ${location.color}`}
                        whileHover={{ scale: 1.5 }}
                        transition={{ duration: 0.2 }}
                      />
                    </div>
                    <div className="col-span-5 md:col-span-4 flex items-center">
                      <span className="font-medium text-gray-800">{location.country}</span>
                    </div>
                    <div className="col-span-3 md:col-span-3 flex items-center justify-center">
                      <span className="text-gray-700">{location.students}</span>
                    </div>
                    <div className="col-span-3 md:col-span-4 flex items-center justify-end pr-4">
                      <div className="w-full max-w-[150px]">
                        <div className="flex items-center">
                          <div className="flex-1 mr-2">
                            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                              <motion.div
                                className={`h-full ${location.color}`}
                                initial={{ width: 0 }}
                                animate={{ width: location.percentage }}
                                transition={{ duration: 1, delay: index * 0.1 }}
                              />
                            </div>
                          </div>
                          <span className="text-sm font-bold">{location.percentage}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="mt-6 text-center text-gray-600 text-sm"
            >
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default topstudentPage;