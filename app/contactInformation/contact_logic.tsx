import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form,  Spin } from "antd";
import React from "react";
import { useState } from "react"
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import axios from "axios";

export const ContactLogics = () => {
   const router = useRouter();
    const { request } = useApi();
    const {showNotification, destroyNotifications} = useNotification(); 
    const [loading,setLoading ] = useState(false);
    const [contactForm] = Form.useForm();


    //Create User
    async function sendMessage(userData: any) {
        try {
            console.log('data', userData);
            showNotification( 'success', 'sending message', '', true, <Spin />);
  
            setLoading(true);
            const requestResponse = await request("POST", "/unprotected/contact_us", 
                JSON.stringify(
                {
                ...userData,
               }), "");
              console.log(requestResponse,'mainData');
              setLoading(false);
              destroyNotifications();
              if(requestResponse && requestResponse?.status <= 204){
                showNotification( 'success', 'Success', requestResponse.data.message);
                contactForm.resetFields(); // Clears form when component unmounts

              }else{
                showNotification( 'error', 'Error sending message',  requestResponse.data.detail);
             
              }
            
        } catch (error) {
           
        }
    }

    


    return {
      sendMessage, contactForm

  }
  }