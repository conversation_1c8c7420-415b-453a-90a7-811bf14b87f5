import { GlobalOutlined } from '@ant-design/icons'
import React, { ReactNode } from 'react'

interface ProgramCardTemplateProps{
    icon: ReactNode
    title: string
    description: string
    courses: string
}

export default function ProgramCardTemplate({icon, title, description, courses}:ProgramCardTemplateProps) {
  return (
    <div className="flex items-center p-4 bg-white rounded-2xl shadow-[0_4px_6px_rgba(0,0,0,0.1),0_10px_20px_rgba(0,0,0,0.1)] space-x-4 w-full">
  <div className="flex-shrink-0">{icon}</div>
  <div>
    <h2 className="text-lg font-semibold text-primayColor">{title}</h2>
    <p className="text-xs text-primayColor">{description}</p>
    <p className="text-gray-600 font-medium mt-1">{courses}</p>
  </div>
</div>
  )
}
