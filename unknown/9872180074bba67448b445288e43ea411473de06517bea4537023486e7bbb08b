import { useState, useEffect } from "react";
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Spin } from "antd";
import { useUserStore } from "@/store/userStore";
import Cookies from "js-cookie";

export const useTeacherSettings = () => {
  const { user } = useUserStore();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  
  // Auto Logout functionality
  const [autoLogoutEnabled, setAutoLogoutEnabled] = useState(false);

  // Initialize auto logout state from cookie
  useEffect(() => {
    const autoLogoutCookie = Cookies.get('auto-logout');
    setAutoLogoutEnabled(autoLogoutCookie === 'true');
  }, []);

  const handleAutoLogoutChange = (value: boolean) => {
    Cookies.set('auto-logout', value.toString());
    setAutoLogoutEnabled(value);
    console.log('Auto logout set to:', value);
  };

  // Deactivate account functionality
  const deactivateTeacherAccount = async () => {
    try {
      setLoading(true);
      showNotification('info', 'Deactivating Account', 'Processing your request...', true, <Spin />);

      const response = await request("PUT", "/auth/user_deactivate", {}, "multipart/form-data");

      setLoading(false);
      destroyNotifications();

      if (response && response.status === 200) {
        showNotification('success', 'Account Deactivated', 'Your account has been deactivated successfully');

        // Redirect to login page after a short delay
        setTimeout(() => {
          Cookies.remove("access_token");
          window.location.href = "/auth/signin";
        }, 2000);
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to deactivate account');
      }
    } catch (error) {
      console.error('Error deactivating account:', error);
      setLoading(false);
      destroyNotifications();
      showNotification('error', 'Error', 'An error occurred while deactivating your account');
    }
  };

  // Delete account functionality
  const deleteTeacherAccount = async () => {
    try {
      if (!user?.id) {
        showNotification('error', 'Error', 'User ID not found');
        return;
      }

      setLoading(true);
      showNotification('info', 'Deleting Account', 'Processing your request...', true, <Spin />);

      const response = await request("PUT", `/auth/delete?user_id=${user.id}`, {}, "");

      setLoading(false);
      destroyNotifications();

      if (response && response.status === 200) {
        showNotification('success', 'Account Deleted', 'Your account has been deleted successfully');

        // Redirect to login page after a short delay
        setTimeout(() => {
          Cookies.remove("access_token");
          window.location.href = "/auth/signin";
        }, 2000);
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      setLoading(false);
      destroyNotifications();
      showNotification('error', 'Error', 'An error occurred while deleting your account');
    }
  };

  return {
    loading,
    autoLogoutEnabled,
    handleAutoLogoutChange,
    deactivateTeacherAccount,
    deleteTeacherAccount
  };
};
