"use client";
import React, { useEffect, useRef, useState } from 'react';
import { SorterResult } from 'antd/es/table/interface';
import dynamic from 'next/dynamic';
import { ArrowRightOutlined, FileTextOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import courseFrame from '@/public/course-frame.png'
import Image from 'next/image'
import TagTemplate from '@/components/ui/tag-template';
import ButtonTemplate from '@/components/ui/button-template';
import { ConfigProvider, Divider, Tabs, TabsProps } from 'antd';
import InputTemplate from '@/components/ui/input-template';
import { useCartStore } from '@/store/cartStore';
import { div } from 'framer-motion/client';
import { EducationLevelList } from '@/utils/levels';
import { useUserStore } from '@/store/userStore';
import { CartLogics } from '../cart/_logics/cart_logics';
import PlatformPage from '../profile/platform/platform';
import ProfileSettingsPage from '../profile/profile-settings/profile-settings';
import AccountSettingsPage from '../profile/account-settings/account-settings';
import AllLearningPage from './all-page';
import OngoingPage from './ongoing-page';
import CompletedPage from './completed-page';
import RecordingsPage from './recordings-page';

const MotionDiv = dynamic(() => import('framer-motion').then(mod => mod.motion.div), { ssr: false });

interface UINotification {
    timestamp: any;
    key: string;
    sender: string;
    type: string;
    content: string;
    date: string;
    time: string;
    status: 'viewed' | 'unviewed';
}

export default function LearningPage() {
    const { checkoutCart, removeFromCart } = CartLogics()
    const { cart } = useCartStore()
    const { user } = useUserStore()

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: 'All',
            children: <AllLearningPage />,
        },
        {
            key: '2',
            label: 'Ongoing',
            children: <OngoingPage />,
        },
        {
            key: '3',
            label: 'Completed',
            children: <CompletedPage />,
        },
        {
            key: '4',
            label: 'Recordings',
            children: <RecordingsPage />,
        },
    ];

    useEffect(() => {
        //   getCart();
    }, []);




    return (
        <div className="w-full p-4 h-full overflow-y-scroll">
            <MotionDiv
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="relative w-full mb-8"
            >
                <img src="/notification.png" alt="Notifications Header" className="w-full rounded-lg" />
                <div className="absolute inset-0 flex items-center justify-center">
                    <h1 className="text-2xl md:text-3xl font-bold text-white">My Learning</h1>
                </div>
                <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 bg-white p-3 rounded-full shadow-lg">
                <FileTextOutlined  className="text-2xl text-gray-600" />
                </div>
            </MotionDiv>

            <ConfigProvider

                theme={{
                    components: {
                        Tabs: {
                            /* here is your component tokens */
                            inkBarColor: "#008080",
                            itemActiveColor: "#008080",
                            itemColor: "#6e6d6b",
                            itemHoverColor: "#000000",
                            itemSelectedColor: "#008080"
                        },
                    },
                }}
            >

                <Tabs defaultActiveKey="1" items={items} onChange={(onChange) => { }} />

            </ConfigProvider>
        </div>
    );
}