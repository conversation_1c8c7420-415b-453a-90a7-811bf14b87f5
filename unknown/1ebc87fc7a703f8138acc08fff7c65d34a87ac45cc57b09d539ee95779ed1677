'use client';

import { But<PERSON>, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';

export interface NoteCardProps {
  id: string;
  timeCategory: string;
  title: string;
  description: string;
  author: { name: string; avatar: string };
  date: string;
  onEdit?: (noteId: string) => void;
  onView?: (noteId: string) => void;
}

export const NoteCard: React.FC<NoteCardProps> = ({
  id,
  timeCategory,
  title,
  description,
  author,
  date,
  onEdit,
  onView
}) => {
  // Map time categories to styles
  const getStyles = () => {
    switch(timeCategory) {
      case 'today':
        return {
          borderColor: 'border-teal-700',
          bgColor: 'bg-teal-50',
          textColor: 'text-teal-800'
        };
      case 'yesterday':
        return {
          borderColor: 'border-green-400',
          bgColor: 'bg-green-50',
          textColor: 'text-green-800'
        };
      case 'outdated':
      default:
        return {
          borderColor: 'border-orange-400',
          bgColor: 'bg-orange-50',
          textColor: 'text-orange-800'
        };
    }
  };

  const styles = getStyles();

  return (
    <div className={`bg-white rounded-sm shadow-sm border-l-4 ${styles.borderColor} flex flex-col h-full`}>
      <div className="p-3 flex-1">
        <div className="mb-1">
          <span className={`text-xs px-2 py-0.5 rounded ${styles.bgColor} ${styles.textColor} inline-block`}>
            {timeCategory.charAt(0).toUpperCase() + timeCategory.slice(1)}
          </span>
        </div>

        <h3 className="text-sm font-medium text-teal-800 mt-1 truncate">{title}</h3>
        <p className="text-xs text-gray-600 mt-1 line-clamp-3">{description}</p>

        <div className="flex items-center mt-2 pt-1 border-t border-gray-100">
          <Avatar size="small" icon={<UserOutlined />} src={author.avatar} />
          <span className="text-xs text-gray-500 ml-2 truncate">{author.name}</span>
          <span className="text-xs text-gray-400 ml-auto">{date}</span>
        </div>
      </div>

      <div className="border-t border-gray-100 py-1 px-2 flex justify-between">
        <Button 
          type="link" 
          size="small" 
          className="text-xs !text-teal-700 hover:!text-teal-800 px-1 min-w-0"
          style={{ color: '#0f766e' }} /* teal-700 hex color */
          onClick={() => onEdit && onEdit(id)}
        >
          Edit note
        </Button>
        
        <Button 
          type="link" 
          size="small" 
          className="text-xs !text-teal-700 hover:!text-teal-800 px-1 min-w-0"
          style={{ color: '#0f766e' }} /* teal-700 hex color */
          onClick={() => onView && onView(id)}
        >
          View full note
        </Button>
      </div>
    </div>
  );
};

export interface MonthSectionProps {
  month: string;
  category: string;
  notes: any[];
  onEdit?: (noteId: string) => void;
}

export const MonthSection: React.FC<MonthSectionProps> = ({
  month,
  category,
  notes,
  onEdit
}) => {
  // Function to group notes in sets of 3
  const groupNotesInRows = (notesList: any[]) => {
    const rows = [];
    for (let i = 0; i < notesList.length; i += 3) {
      rows.push(notesList.slice(i, i + 3));
    }
    return rows;
  };

  // Group notes in rows of 3
  const notesInRows = groupNotesInRows(notes);

  return (
    <div className="mb-8">
      <div className="flex items-center mb-3">
        <span className="text-teal-600 text-sm font-medium">{month}</span>
        <div className="h-1 w-1 bg-gray-300 rounded-full mx-2"></div>
        <span className="text-teal-700 text-sm">{category}</span>
      </div>

      {notesInRows.map((row, rowIndex) => (
        <div key={`row-${rowIndex}`} className="flex flex-wrap -mx-2 mb-4">
          {row.map((note) => (
            <div key={note.id} className="w-full sm:w-1/2 lg:w-1/3 px-2 mb-4">
              <NoteCard
                id={note.id}
                timeCategory={note.timeCategory}
                title={note.title}
                description={note.description}
                author={note.author}
                date={note.date}
                onEdit={onEdit}
              />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

