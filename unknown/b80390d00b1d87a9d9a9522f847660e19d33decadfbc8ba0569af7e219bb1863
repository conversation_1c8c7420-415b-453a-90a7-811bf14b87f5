export type SchoolType = {
    city: string;
    id: string; // Could use a UUID type if you have a library for it
    country: string;
    location: string; // Latitude and longitude as a formatted string
    updated_at: string; // ISO 8601 date string; could use Date if parsed
    address: string;
    state: string;
    name: string;
    type: string; // Could be an enum like 'university' | 'school' if restricted
    created_at: string; // ISO 8601 date string; could use Date if parsed
};

export  type SchoolProgram = {
    school_id: string;
    description: string;
    updated_at: string; // Consider using Date if you'll parse it
    name: string;
    created_at: string; // Consider using Date if you'll parse it
    id: string;
};

// Resource type for curriculum items
export type Resource = {
  title: string;
  type: 'pdf' | 'link'; // Add more types if needed (e.g., 'video', 'image')
  url: string;
};

// Curriculum item type
export type CurriculumItem = {
  id: string;
  title: string;
  description: string;
  type: 'video' | 'quiz' | 'assignment' | 'discussion' | 'live_session' | 'project' | 'exam'; // Add more types as needed
  duration_minutes: number | null;
  order: number;
  content_id: string | null;
  resources: Resource[] | null;
  is_preview: boolean;
};

// Curriculum section type
export type CurriculumSection = {
  id: string;
  title: string;
  description: string;
  order: number;
  items: CurriculumItem[];
};

// Curriculum type
export type Curriculum = {
  _id: string;
  id: string;
  course_id: string;
  sections: CurriculumSection[];
  created_at: string;
  updated_at: string;
};

// Course type
export type Course = {
  id: string;
  topics: string[] | Record<string, string> | null;
  auto_create_cohorts: boolean;
  name: string;
  cover_image: string;
  cover_image_path: string;
  days_between_cohorts: number;
  code: string;
  duration: number | null;
  created_at: string;
  description: string;
  has_cohorts: boolean;
  updated_at: string;
  credits: number;
  has_curriculum: boolean;
  deleted_at: string | null;
  base_price: number;
  is_self_paced: boolean;
  teacher_id: string;
  teacher_name?: string; // Teacher's full name
  teacher_avatar?: string | null; // Teacher's avatar image
  cohort_duration_weeks: number | null;
  level: string; // Allow any level value from the API
  teacher_commission_percentage: number;
  max_students_per_cohort: number | null;
  is_published?: boolean;
};

// Teacher type
export type Teacher = {
  name: string;
  specialization: string;
  email: string;
  school_id: string;
  availability: boolean;
  cover_image_path: string | null;
  updated_at: string;
  phone: string;
  id: string;
  user_id: string;
  rating: number;
  profile_image_path: string | null;
  created_at: string;
  introduction: string;
  deleted_at: string | null;
};

// Top-level type for the entire object
export type CourseResponseData = {
  course: Course;
  curriculum: Curriculum;
  ratings: string[]; // Adjust this if ratings has a more specific structure
  teacher: Teacher;
  total_student_enrolled: number;
  cover_image: string;
};
