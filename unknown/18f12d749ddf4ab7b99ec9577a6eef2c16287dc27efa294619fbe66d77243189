import { useState } from "react";
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Form, Spin, message } from "antd";
import { useLecturerStore } from "@/store/lecturerStore";
import { useUserStore } from "@/store/userStore";
import { processImageSource } from "@/utils/imageUtils";

export const useTeacherProfile = () => {
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);
  const [form] = Form.useForm();

  const { Lecturer, setLecturer } = useLecturerStore();
  const { user } = useUserStore();

  // Fetch Teacher Profile
  const fetchTeacherProfile = async () => {
    try {
      console.log('TeacherProfile - Starting fetch');
      setLoading(true);
      const response = await request("GET", "/protected/me", null, "");
      console.log('TeacherProfile - /me response:', response);

      if (response && response.status === 200) {
        console.log('TeacherProfile - Response data:', response.data);
        // For teachers, the API returns 'teacher'
        const teacherData = response.data.data.teacher;
        console.log('TeacherProfile - Teacher data:', teacherData);

        if (teacherData) {
          // Process profile and cover images with proper format for display
          if (response.data.data.profile_image) {
            teacherData.profile_image = response.data.data.profile_image;
            teacherData.profile_image_path = processImageSource(response.data.data.profile_image);
          } else if (teacherData.profile_image) {
            teacherData.profile_image_path = processImageSource(teacherData.profile_image);
          }

          if (response.data.data.cover_image) {
            teacherData.cover_image = response.data.data.cover_image;
            teacherData.cover_image_path = processImageSource(response.data.data.cover_image);
          } else if (teacherData.cover_image) {
            teacherData.cover_image_path = processImageSource(teacherData.cover_image);
          }

          setLecturer(teacherData);
          console.log('TeacherProfile - Teacher data set in store');
          console.log('TeacherProfile - Availability value:', teacherData.availability);

          // Set form values immediately after getting the data
          // Mask sensitive data for display
          const maskedEmail = response.data.data.user.email?.replace(/(?<=^.{3}).*(?=@)/g, '******');
          const maskedPhone = (response.data.data.user.phone || teacherData.phone || '')?.replace(/(?<=^.{3}).*(?=.{2}$)/g, '*****');

          form.setFieldsValue({
            first_name: response.data.data.user.first_name,
            last_name: response.data.data.user.last_name,
            email: maskedEmail,
            phone: maskedPhone,
            bio: teacherData.introduction || '',
            specialization: teacherData.specialization || '',
            availability: teacherData.availability !== undefined ? String(teacherData.availability) : '',
          });

          return teacherData;
        } else {
          console.log('TeacherProfile - No teacher data found in response');
          message.error('No teacher data found');
          return null;
        }
      } else if (response?.status === 401 || response?.status === 403) {
        // Token expired or invalid - set flag to show not found page
        console.log('TeacherProfile - Token expired or invalid');
        setTokenExpired(true);
        return null;
      } else {
        console.log('TeacherProfile - Error response:', response);
        message.error('Failed to fetch profile data');
        return null;
      }
    } catch (error: any) {
      console.error('TeacherProfile - Error fetching profile:', error);
      // If there's an authentication error, set flag to show not found page
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        message.error('An error occurred while fetching profile data');
      }
      return null;
    } finally {
      // Only set loading to false if we're not showing the not found page
      if (!tokenExpired) {
        setLoading(false);
      }
    }
  };

  // Update Teacher Profile
  const updateTeacherProfile = async (values: any) => {
    try {
      // Only show toast notification, not full-page loader
      showNotification('info', 'Updating Profile', 'Saving your profile information...', true, <Spin />);

      if (!Lecturer?.id) {
        destroyNotifications();
        showNotification('error', 'Error', 'Teacher ID not found');
        return null;
      }

      const updateData = {
        specialization: values.specialization || '',
        introduction: values.bio || ''
      };

      console.log('Updating teacher profile with data:', updateData);

      const response = await request(
        "PUT",
        `/teacher/${Lecturer.id}`,
        updateData,
        "application/json"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        setLecturer(response.data.data);
        showNotification('success', 'Success', 'Profile updated successfully');
        return response.data.data;
      } else if (response?.status === 401 || response?.status === 403) {
        // Token expired or invalid - set flag to show not found page
        setTokenExpired(true);
        return null;
      } else {
        // More detailed error logging
        console.error('Update failed with status:', response?.status);
        console.error('Error details:', response?.data);
        showNotification('error', 'Error', response?.data?.detail || 'Failed to update profile');
        return null;
      }
    } catch (error: any) {
      console.error('Error updating profile:', error);
      console.error('Error response:', error?.response?.data);

      // If there's an authentication error, set flag to show not found page
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        destroyNotifications();
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while updating your profile');
      }
      return null;
    }
  };

  // Upload Teacher Profile Image
  const uploadProfileImage = async (file: File) => {
    try {
      if (!Lecturer?.id) {
        showNotification('error', 'Error', 'Teacher ID not found');
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your profile image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      const response = await request(
        "POST",
        `/teacher/${Lecturer.id}/upload-profile-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the lecturer state with the new image
        const updatedLecturer = { ...Lecturer };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedLecturer.profile_image_path = e.target.result as string;

            // Update the lecturer state with the server response data
            if (response.data.data.profile_image) {
              updatedLecturer.profile_image = response.data.data.profile_image;
            } else if (response.data.data.profile_image_path) {
              updatedLecturer.profile_image_path_server = response.data.data.profile_image_path;
            }

            setLecturer({...updatedLecturer});
          }
        };

        setLecturer(updatedLecturer);
        showNotification('success', 'Success', 'Profile image updated successfully');
        return updatedLecturer;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        return null;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to upload profile image');
        return null;
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        destroyNotifications();
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while uploading your profile image');
      }
      return null;
    }
  };

  // Upload Teacher Cover Image
  const uploadCoverImage = async (file: File) => {
    try {
      if (!Lecturer?.id) {
        showNotification('error', 'Error', 'Teacher ID not found');
        return null;
      }

      // Show notification while uploading
      showNotification('info', 'Uploading Image', 'Uploading your cover image...', true, <Spin />);

      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Make the API request
      const response = await request(
        "POST",
        `/teacher/${Lecturer.id}/upload-cover-image`,
        formData,
        "multipart/form-data"
      );

      destroyNotifications();

      if (response && response.status === 200) {
        // Update the lecturer state with the new image
        const updatedLecturer = { ...Lecturer };

        // Use the uploaded file for immediate display
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            updatedLecturer.cover_image_path = e.target.result as string;

            // Update the lecturer state with the server response data
            if (response.data.data.cover_image) {
              updatedLecturer.cover_image = response.data.data.cover_image;
            } else if (response.data.data.cover_image_path) {
              updatedLecturer.cover_image_path_server = response.data.data.cover_image_path;
            }

            setLecturer({...updatedLecturer});
          }
        };

        setLecturer(updatedLecturer);
        showNotification('success', 'Success', 'Cover image updated successfully');
        return updatedLecturer;
      } else if (response?.status === 401 || response?.status === 403) {
        setTokenExpired(true);
        return null;
      } else {
        showNotification('error', 'Error', response?.data?.detail || 'Failed to upload cover image');
        return null;
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setTokenExpired(true);
      } else {
        destroyNotifications();
        showNotification('error', 'Error', error?.response?.data?.detail || 'An error occurred while uploading your cover image');
      }
      return null;
    }
  };

  return {
    fetchTeacherProfile,
    updateTeacherProfile,
    uploadProfileImage,
    uploadCoverImage,
    loading,
    tokenExpired,
    form
  };
};
