'use client'
import React, { useEffect, useState } from 'react'
import { useApi } from '@/hooks/useRequest'
import { useAutoLogout } from '@/hooks/useAutoLogout'
import { Loader } from './loader'
import { useUserStore } from '@/store/userStore'
import Cookies from "js-cookie"
import { useLecturerStore } from '@/store/lecturerStore'
import { processImageSource } from '@/utils/imageUtils'
import NotFoundPage from './not-found'

export const TeacherLayout = ({ children }: { children: React.ReactNode }) => {
  const [loading, setLoading] = useState(true)
  const [tokenExpired, setTokenExpired] = useState(false)
  const { setUser } = useUserStore()
  const { setLecturer } = useLecturerStore()

  const { request } = useApi()
  const { } = useAutoLogout() 

  useEffect(() => {
    const fetchTeacherData = async () => {
      try {
        const token = Cookies.get('access_token')
        if (!token) {
          setTokenExpired(true)
          return
        }

        setLoading(true)
        const response = await request("GET", "/protected/me", null, "")

        if (response && response.status === 200) {
          setUser(response.data.data.user)

          // Check user role
          if (response.data.data.user.role === 3) {
            if (response.data.data.teacher) {
              const lecturerData = response.data.data.teacher;

              // Process profile and cover images if they exist in the response
              if (response.data.data.profile_image) {
                lecturerData.profile_image = response.data.data.profile_image;
                // Set both profile_image and profile_image_path for consistency
                lecturerData.profile_image_path = processImageSource(response.data.data.profile_image);
              } else if (lecturerData.profile_image) {
                // Ensure profile_image_path is always set if profile_image exists
                lecturerData.profile_image_path = processImageSource(lecturerData.profile_image);
              }

              if (response.data.data.cover_image) {
                lecturerData.cover_image = response.data.data.cover_image;
                lecturerData.cover_image_path = processImageSource(response.data.data.cover_image);
              } else if (lecturerData.cover_image) {
                lecturerData.cover_image_path = processImageSource(lecturerData.cover_image);
              }

              setLecturer(lecturerData)
              setLoading(false)
            } else {
              setTokenExpired(true)
            }
          } else {
            setTokenExpired(true)
          }
        } else if (response?.status === 401 || response?.status === 403) {
          // Token expired or invalid
          setTokenExpired(true)
        } else {
          setTokenExpired(true)
        }
      } catch (error: any) {
        console.error('Error fetching teacher data:', error)
        // Check if it's an authentication error
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          setTokenExpired(true)
        } else {
          setTokenExpired(true)
        }
      }
    }

    fetchTeacherData()
  }, [])

  // Show not found page if token is expired
  if (tokenExpired) {
    return <NotFoundPage code="401" value="Your session has expired. Please sign in again." />
  }

  // Show loader while data is being fetched
  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white">
        <Loader />
      </div>
    )
  }

  return <>{children}</>
}
