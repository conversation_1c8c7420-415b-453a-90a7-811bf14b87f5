
// hooks/useAutoLogout.ts
import { useEffect, useState } from 'react';
import Cookies from "js-cookie";

const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

interface AutoLogoutOptions {
  timeoutDuration?: number; // Total time until logout in milliseconds
  warningTime?: number;     // Time until warning appears in milliseconds
}

export const useAutoLogout = ({
  timeoutDuration = 363 * 1000,  // 63 seconds total (3s inactivity + 60s countdown)
  warningTime = 300 * 1000,      // 3 seconds until warning
}: AutoLogoutOptions = {}) => {
  const [showWarning, setShowWarning] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(0); // Countdown in seconds
  let logoutTimer: NodeJS.Timeout;
  let warningTimer: NodeJS.Timeout;
  let countdownInterval: NodeJS.Timeout;

  const resetTimer = (): void => {
    clearTimeout(logoutTimer);
    clearTimeout(warningTimer);
    clearInterval(countdownInterval);
    setShowWarning(false);
    setCountdown(0);

    warningTimer = setTimeout(() => {
      const shouldAutoLogout = Cookies.get("auto-logout") === 'true';
      console.log('auto logging cookie', Cookies.get("auto-logout"));
      
      if (shouldAutoLogout) {
        setShowWarning(true);
        // Set countdown to 60 seconds (remaining time after warning)
        const remainingSeconds = Math.ceil((timeoutDuration - warningTime) / 1000);
        setCountdown(remainingSeconds);
        
        // Start countdown interval
        countdownInterval = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    }, warningTime); // Warning appears after warningTime
    
    logoutTimer = setTimeout(logout, timeoutDuration); // Logout after total timeoutDuration
  };

  const logout = (): void => {
    console.log('logging out');
    clearInterval(countdownInterval);
    setCountdown(0);
    setShowWarning(false);
  };

  useEffect(() => {
    const events: string[] = [
      'mousemove',
      'keydown',
      'wheel',
      'touchstart',
      'touchmove'
    ];

    events.forEach((event: string) => {
      window.addEventListener(event, resetTimer);
    });

    resetTimer();

    return () => {
      clearTimeout(logoutTimer);
      clearTimeout(warningTimer);
      clearInterval(countdownInterval);
      events.forEach((event: string) => {
        window.removeEventListener(event, resetTimer);
      });
    };
  }, [timeoutDuration, warningTime]);

   return { showWarning, countdown, resetTimer };

}