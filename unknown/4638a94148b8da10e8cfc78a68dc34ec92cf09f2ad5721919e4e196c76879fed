"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useApi } from "@/hooks/useRequest";
import { useNotification } from "@/hooks/useNotifs";
import { Spin } from 'antd';
import {
  Typography,
  Card,
  Form
} from 'antd';
import {
  ArrowLeftOutlined,
  MailOutlined
} from '@ant-design/icons';
import InputTemplate from '@/components/ui/input-template';

const { Title, Text } = Typography;

const AccountRecoveryPage = () => {
  const router = useRouter();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [emailError, setEmailError] = useState('');

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAccountRecover = async () => {
    setEmailError('');
    destroyNotifications();

    try {
      await form.validateFields();
      const email = form.getFieldValue('recoveryEmail');

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      setLoading(true);
      showNotification('info', 'Recovering Account', 'Attempting to restore your account...', true, <Spin />);

      try {
        const recoveryResponse = await request("POST", "/auth/recover_account",
          JSON.stringify({ email }),
          "application/json"
        );

        destroyNotifications();

        if (recoveryResponse?.status === 200) {
          showNotification('success', 'Account Recovery', 'A recovery email has been sent. Please check your inbox.');
          setTimeout(() => router.push("/auth/signin"), 2000);
        } else {
          showNotification('success', 'Error', 'Your email account is still active ');
          setTimeout(() => router.push("/auth/signin"), 2000);
        }
      } catch (error: any) {
        destroyNotifications();
      } finally {
        setLoading(false);
      }
    } catch (validationError) {
      setLoading(false);
    }
  };

  const handleReturnToSite = () => router.push('/auth/signin');

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 50 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { type: "spring", stiffness: 120, damping: 15, duration: 0.5 }
    },
    hover: { scale: 1.02, transition: { type: "spring", stiffness: 300 } }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-teal-50 p-4">
      <motion.div initial="hidden" animate="visible" whileHover="hover" variants={cardVariants}>
        <Card className="shadow-2xl rounded-2xl overflow-hidden border-2 border-teal-200" bordered={false}>
          <div className="text-center py-12 bg-white">
            <Image src="/logo.png" alt="LearnKonnect logo" width={100} height={100} className="mx-auto mb-8" priority />
            
            <Title className="text-teal-700 mb-4 font-bold text-3xl" style={{ color: '#0d9488' }}>
              Your account has been deactivated
            </Title>

            <Text className="block text-gray-600 text-lg mb-10 px-6 font-medium">
              Thanks for flying with LearnKonnect. You have 30 days to restore your account if you change your mind.
            </Text>

            <Form form={form} onFinish={handleAccountRecover} className="px-6 mb-6">
              <InputTemplate
                inputType="email"
                placeHolder="Enter recovery email"
                label=""
                fieldName="recoveryEmail"
                prefix={<MailOutlined className="text-gray-400" />}
                className={emailError ? 'border-red-500' : ''}
                rules={[
                  { required: true, message: 'Please input your email' },
                  { 
                    validator: (_, value) => 
                      value && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) 
                        ? Promise.resolve() 
                        : Promise.reject(new Error('Please enter a valid email address')) 
                  }
                ]}
              />

              {emailError && (
                <div className="text-red-500 text-xs mt-1 mb-2">
                  {emailError}
                </div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full mt-4 py-3 bg-teal-600 hover:bg-teal-700 text-white rounded-lg font-bold text-lg transition-colors duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg"
              >
                {loading ? 'Recovering...' : 'Restore My Account'}
              </button>
            </Form>

            <button
              onClick={handleReturnToSite}
              className="text-gray-500 hover:text-gray-700 flex items-center justify-center mx-auto text-base font-semibold"
            >
              <ArrowLeftOutlined className="mr-2" />
              Return to learnkonnect login page
            </button>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default AccountRecoveryPage;