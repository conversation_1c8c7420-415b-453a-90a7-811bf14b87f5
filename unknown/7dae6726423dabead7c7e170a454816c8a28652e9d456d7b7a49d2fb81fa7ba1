import React, {  } from 'react';
import { CloseCircleOutlined } from '@ant-design/icons';
import { Tag } from 'antd';


type TagTemplateProps ={
    className?: string
    value: string
    closeIcon?: boolean
    handleClick?: () => void
}

const TagTemplate = ({className, value, closeIcon, handleClick}:TagTemplateProps) => {
  

  return (
    <>
       <Tag onClick={handleClick} className={`bg-primaryColor text-white rounded-full p-1 px-4 hover:cursor-pointer ${className}`} closeIcon={closeIcon? <CloseCircleOutlined className='text-white bg-white rounded-full' />:<></>} onClose={console.log}>
      {value}
    </Tag>
    </>
  );
};

export default TagTemplate;