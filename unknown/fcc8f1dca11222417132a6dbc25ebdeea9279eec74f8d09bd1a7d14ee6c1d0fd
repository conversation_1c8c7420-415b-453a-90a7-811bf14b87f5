import { coursesData } from '@/components/general/dummy-data/coursesData';
import { ArrowRightOutlined, EyeOutlined, HeartOutlined, ShoppingCartOutlined, StarFilled, UserOutlined } from '@ant-design/icons';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useState } from 'react';

import Image from 'next/image';

interface Course {
    id: number;
    image: string;
    title: string;
    category: string;
    label: string;
    description: string;
    hours: number;
    students: number;
    discountPrice: number;
    originalPrice: number;
    categoryType: string;
  }


interface CourseCardProps {
    course: Course;
    index: number;
    navAnimation: { left: boolean; right: boolean };
  }
  
  const CourseCard = ({ course, index, navAnimation }: CourseCardProps) => {
    return (
      <motion.div 
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        whileHover={{ 
          y: -8, 
          transition: { duration: 0.2 },
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)"
        }}
        className="bg-white rounded-lg overflow-hidden shadow-md h-full flex flex-col relative"
      >
        {/* Course Image */}
        <div className="relative w-full overflow-hidden" style={{height: '30em'}}>
          <Image 
            src={course.image} 
            alt={course.title}
            fill
            style={{ objectFit: 'cover' }}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          {/* Sale Label */}
          {course.label === "NEW" && (
            <div className="absolute top-2 left-2 bg-text-teal-700 text-white text-xs py-0.5 px-2 rounded">
              Sale
            </div>
          )}
          
          {/* Action Icons replacing Rating Stars */}
          <div className="absolute bottom-3 left-0 right-0 flex justify-center gap-3">
            <motion.div 
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.2, backgroundColor: "#0D9488" }}
              className="w-9 h-9 rounded-full flex items-center justify-center bg-white shadow-md cursor-pointer"
            >
              <HeartOutlined className="text-text-teal-700 text-lg" />
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 + 0.1 }}
              whileHover={{ scale: 1.2, backgroundColor: "#0D9488" }}
              className="w-9 h-9 rounded-full flex items-center justify-center bg-white shadow-md cursor-pointer"
            >
              <ShoppingCartOutlined className="text-text-teal-700 text-lg" />
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 + 0.2 }}
              whileHover={{ scale: 1.2, backgroundColor: "#0D9488" }}
              className="w-9 h-9 rounded-full flex items-center justify-center bg-white shadow-md cursor-pointer"
            >
              <EyeOutlined className="text-text-teal-700 text-lg" />
            </motion.div>
          </div>
        </div>
  
        {/* Course Content */}
        <div className="p-4 flex-grow flex flex-col">
          {/* Category */}
          <div className="flex justify-between items-center mb-2">
            <p className="text-xs font-medium text-teal-700">{course.category}</p>
            
            {/* Display rating with star icon */}
            {course.category.includes("online courses") && (
              <div className="flex items-center bg-teal-100 text-teal-700 text-xs py-0.5 px-2 rounded-full">
                <StarFilled className="mr-1 text-yellow-500" />
                <span>5.0</span>
              </div>
            )}
            
            {course.categoryType === "training" && !course.category.includes("online courses") && (
              <div className="flex items-center bg-teal-100 text-teal-700 text-xs py-0.5 px-2 rounded-full">
                <StarFilled className="mr-1 text-yellow-500" />
                <span>4.8</span>
              </div>
            )}
            
            {course.categoryType === "expert" && (
              <div className="flex items-center bg-teal-100 text-teal-700 text-xs py-0.5 px-2 rounded-full">
                <StarFilled className="mr-1 text-yellow-500" />
                <span>4.9</span>
              </div>
            )}
          </div>
  
          {/* Title */}
          <h3 className="font-semibold text-lg text-teal-700 mb-3">{course.title}</h3>
  
          {/* Description */}
          <p className="text-sm text-gray-600 mb-4 flex-grow">{course.description}</p>
  
          {/* Meta Information - Number of Sales */}
          <div className="flex items-center text-xs text-gray-500 mb-3">
            <UserOutlined className="mr-1" />
            <span>{course.categoryType === "expert" ? "15 Sales" : "18 Sales"}</span>
          </div>
  
          {/* Price and Enroll now */}
          <div className="flex justify-between items-center">
            <div>
              <span className="text-base font-medium text-teal-700">${course.discountPrice.toFixed(2)}</span>
              <span className="text-sm text-gray-400 line-through ml-2">${course.originalPrice.toFixed(2)}</span>
            </div>
            <motion.button 
              whileHover={{ scale: 1.05, backgroundColor: "#0D9488", color: "white" }}
              whileTap={{ scale: 0.95 }}
              className="text-teal-700 bg-white border border-teal-700 rounded-full px-3 py-1 text-sm flex items-center transition-colors duration-300"
            >
              Enroll now <ArrowRightOutlined className="ml-1" />
            </motion.button>
          </div>
        </div>
  
        {/* Edge Indicators at the Bottom */}
        <AnimatePresence>
          {navAnimation.left && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: '5%' }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute left-0 bottom-0 h-2 bg-gradient-to-r from-teal-500/20 to-transparent z-10"
            />
          )}
        </AnimatePresence>
  
        <AnimatePresence>
          {navAnimation.right && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: '5%' }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute right-0 bottom-0 h-2 bg-gradient-to-l from-teal-500/20 to-transparent z-10"
            />
          )}
        </AnimatePresence>
      </motion.div>
    );
  };
  

// const CourseCard = ({ icon, category, title, description, rating, sales, originalPrice, discountedPrice }: any) => (
   
//     <div className="bg-white rounded-sm shadow-md hover:shadow-xl transition-shadow ">
//         {/* Course Image with Sale Label */}
//         <div className="relative w-full h-[20rem] overflow-hidden rounded-sm mb-4">
//             <img src={"/stud3.jpg"} alt={title} className="w-full h-full object-cover" />
//             <span className="absolute top-2 left-2 bg-primaryColor text-white text-xs px-3 py-1 rounded-md">Sale</span>
//             <div className="absolute left-0 right-0 bottom-2 flex justify-center space-x-4 mb-4 text-teal-500">
//                 <span className="cursor-pointer bg-white rounded-full h-[2rem] flex justify-center items-center w-[2rem]"><HeartOutlined /></span> {/* Replace with actual icons (e.g., react-icons) */}
//                 <span className="cursor-pointer bg-white rounded-full h-[2rem] flex justify-center items-center w-[2rem]"><ShoppingCartOutlined /></span>
//                 <span className="cursor-pointer bg-white rounded-full h-[2rem] flex justify-center items-center w-[2rem] "><EyeOutlined /></span>
//             </div>
//         </div>

//         {/* Interaction Icons (Heart, Cart, Eye) */}


//         <div className='px-3'>

//             {/* Category and Rating */}
//             <div className="flex justify-between items-center mb-2 ">
//                 <span className="text-teal-700 text-sm font-medium">{category}</span>
//                 <span className="flex items-center text-teal-700 text-sm">
//                     ★ {rating} {/* Replace with actual star icon or component */}
//                 </span>
//             </div>

//             {/* Course Title and Description */}
//             <h3 className="text-lg font-semibold text-teal-700 mb-2">{title}</h3>
//             <p className="text-sm text-teal-600 mb-2">{description}</p>

//             {/* Sales and Enroll Button */}
//             <div className="flex justify-between items-center mb-4">
//                 <span className="text-teal-600 text-sm">↓ {sales} Sales</span>
//                 <button className="bg-teal-600 text-white text-sm px-4 py-2 rounded-full hover:bg-teal-700 transition-colors">
//                     Enroll Now →
//                 </button>
//             </div>

//             {/* Pricing */}
//             <div className="flex items-center space-x-2">
//                 <span className="text-lg font-bold text-teal-700">${discountedPrice}</span>
//                 <span className="text-teal-600 line-through text-sm">${originalPrice}</span>
//             </div>
//         </div>
//     </div>
// );

const TopRatedCourses = () => {
    const [activeCategory, setActiveCategory] = useState('all');

    // Filter courses based on active category
    const filteredCourses = activeCategory === 'all' 
      ? coursesData 
      : coursesData.filter(course => course.categoryType === activeCategory);
      const [currentSlide, setCurrentSlide] = useState(0);
    // Calculate visible courses (3 at a time)
    const visibleCourses = filteredCourses.slice(currentSlide, currentSlide + 3);

  const [navAnimation, setNavAnimation] = useState({ left: false, right: false });
  
    
    const courses = [
        {
            icon: "https://via.placeholder.com/300x200?text=Programming", // Replace with actual image URL
            category: "Training Courses",
            title: "Programming Fundamentals",
            description: "Introduction to programming using languages like Python, Java, or C++. Covers algorithms, data structures, and problem-solving techniques.",
            rating: "4.9",
            sales: "15",
            originalPrice: "16.48",
            discountedPrice: "6.48",
        },
        {
            icon: "https://via.placeholder.com/300x200?text=Business", // Replace with actual image URL
            category: "Business and Management",
            title: "Business and Management",
            description: "Teaches financial reporting, auditing, budgeting, and investment strategies.",
            rating: "4.9",
            sales: "15",
            originalPrice: "16.48",
            discountedPrice: "6.48",
        },
        {
            icon: "https://via.placeholder.com/300x200?text=Medicine", // Replace with actual image URL
            category: "Medicine and Health Sciences",
            title: "Expert Instruction Medicine and Health Sciences",
            description: "Focuses on diagnosing and treating diseases, including anatomy, pathology, and pharmacology.",
            rating: "4.9",
            sales: "15",
            originalPrice: "16.48",
            discountedPrice: "6.48",
        },
    ];

    return (
        <div className="container mx-auto py-6">
            {/* Top Rated Courses Header */}
            <div className="bg-primaryColor   pt-4 rounded-md  w-full">
                <div className='flex justify-between items-center hover:cursor-pointer'>
                <h2 className="text-lg px-6 mb-6 font-semibold text-white ">Top Rated Courses</h2>
                <h2 className="text-sm px-6 mb-6 font-normal text-white flex space-x-4 ">See all <ArrowRightOutlined className='ml-2' /></h2>
                </div>

                {/* Course Grid */}
                <div className="">
                <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            animate={{ 
              x: navAnimation.left ? 10 : navAnimation.right ? -10 : 0 
            }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {visibleCourses.map((course, index) => (
              <CourseCard 
                key={course.id} 
                course={course} 
                index={index} 
                navAnimation={navAnimation} 
              />
            ))}
          </motion.div>
                </div>
            </div>
        </div>
    );
};

export default TopRatedCourses;