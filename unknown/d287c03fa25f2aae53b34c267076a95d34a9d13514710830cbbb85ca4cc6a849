"use client";
import { useState, useEffect } from "react";
import emailjs from "@emailjs/browser";
import Image from "next/image";
import Navbar from "@/components/general/indexPagelayout/navbar";
import Footer from "@/components/general/indexPagelayout/footer";
import customer from "@/public/customer.png";
import {
  ArrowDownOutlined,
  ArrowRightOutlined,
  FacebookOutlined,
  InstagramOutlined,
  RightOutlined,
  SearchOutlined,
  UserOutlined,
  XOutlined,
} from "@ant-design/icons";
import InputTemplate from "@/components/ui/input-template";
import TextAreaTemplateInterface from "@/components/ui/TextArea-template";
import ButtonTemplate from "@/components/ui/button-template";
import {
  Button,
  ConfigProvider,
  Form,
  FormProps,
  message,
  Radio,
  Space,
  ThemeConfig,
} from "antd";
// import { useState } from "react";
import { request } from "http";
import { ContactLogics } from "./contact_logic";

const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};

function ContactInformation() {
  // const [messageLength, setMessageLength] = useState(0);
  const [position, setPosition] = useState<"start" | "end">("end");
  const { sendMessage, contactForm } = ContactLogics();
  const [message, setMessage] = useState("");
  const [number, setNumber] = useState("");

  const MAX_MESSAGE_LENGTH = 255;
  const max_number_length = 10;

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const inputValue = e.target.value;
    if (inputValue.length > MAX_MESSAGE_LENGTH) return;
    setMessage(inputValue);
  };

  useEffect(() => {
    emailjs.init("S5wMXl5_EqEs1k7NT");
  }, []);

  const sendEmail = async (values: any) => {
    try {
      const response = await emailjs.send("contact_service", "contact_form", {
        name: values.name,
        email: values.email,
        phone: values.phone,
        message: values.message,
      });
      console.log("Email sent successfully!", response);
    } catch (error) {
      console.error("Email sending failed!", error);
    }
  };

  const onFinish: FormProps<any>["onFinish"] = (info) => {
    sendMessage(info);
    sendEmail(info);
  };

  const validateAlphaNumeric = (_: any, value: string) => {
    if (value && !/^[a-zA-Z0-9\s]+$/.test(value)) {
      return Promise.reject(new Error("Only alphanumeric characters allowed"));
    }
    return Promise.resolve();
  };

  const validateEmail = (_: any, value: string) => {
    if (
      value &&
      !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)
    ) {
      return Promise.reject(new Error("Invalid email address"));
    }
    return Promise.resolve();
  };

  const validatePhoneNumber = (_: any, value: string): Promise<void> => {
    if (!value) {
      return Promise.reject(new Error(""));
    }
    if (/\s/.test(value)) {
      return Promise.reject(new Error("Spaces are not allowed"));
    }
    if (!/^\d+$/.test(value)) {
      return Promise.reject(new Error("Only numbers are allowed"));
    }
    if (value.length < 10) {
      return Promise.reject(
        new Error("Phone number must be at least 10 digits")
      );
    }
    return Promise.resolve();
  };

  return (
    <>
      <div>
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
          <div className="flex flex-row justify-between mb-12">
            <div className="font-bold ">
              <h3 className="font-normal mb-4 text-base">Get Started</h3>
              <div className="text-4xl text-base/9 ">
                <h1>Get in touch with us.</h1>
                <h1>We are here to assist you.</h1>
              </div>
            </div>
            <div className="flex flex-col gap-4 mt-6 mr-10">
              <div className="w-12 h-12 flex items-center justify-center border-2  rounded-full">
                <FacebookOutlined className="" />
              </div>

              <div className="w-12 h-12 flex items-center justify-center border-2  rounded-full">
                <InstagramOutlined className=" " />
              </div>

              <div className="w-12 h-12 flex items-center justify-center border-2  rounded-full">
                <XOutlined className=" " />
              </div>
            </div>
          </div>
          {/**inputs */}
          <Form name="contactUsForm" onFinish={onFinish} form={contactForm}>
            <div className="flex flex-row gap-1 sm:gap-8 mt-10 flex-wrap ">
              <Form.Item
                name="name"
                validateTrigger={["onBlur", "onChange"]}
                className=""
              >
                <InputTemplate
                  fieldName="name"
                  rules={[{ validator: validateAlphaNumeric }]}
                  label="Your Name"
                  className=" w-[22rem] sm:w-[20rem] rounded-lg border-gray-200 focus:border-teal-500 "
                />
              </Form.Item>

              <Form.Item className="">
                <InputTemplate
                  rules={[{ validator: validateEmail }]}
                  fieldName="email"
                  label={"Email Address"}
                  className="w-[22rem] sm:w-[20rem]   rounded-lg border-gray-200 focus:border-teal-500"
                />
              </Form.Item>

              <InputTemplate
                rules={[{ validator: validatePhoneNumber }]}
                fieldName="phone"
                label={"Phone Number"}
                className="w-[22rem] sm:w-[20rem]  rounded-lg border-gray-200 focus:border-teal-500"
                maxLength={max_number_length}
                value={number}
              />
            </div>
            <div className="flex flex-col sm:flex-row flex-wrap gap-1 sm:gap-6 mt-1 sm:mt-10 mb-8">
              <Form.Item name="message" className="">
                <div className="relative">
                  <TextAreaTemplateInterface
                    label="Message"
                    fieldName="message"
                    className="w-full sm:w-[28rem] md:w-[36rem] lg:w-[42rem] h-auto"
                    value={message}
                    onChange={handleMessageChange}
                    maxLength={MAX_MESSAGE_LENGTH}
                  />
                  {/* Character Counter */}
                  <span className="text-gray-500 text-sm absolute bottom-1 right-4">
                    {MAX_MESSAGE_LENGTH - message.length} characters left
                  </span>
                </div>
              </Form.Item>
              ;{/* Submit Button */}
              <ConfigProvider theme={theme}>
                <Button
                  icon={<ArrowRightOutlined />}
                  iconPosition={position}
                  className="rounded-full px-8 py-6 text-primaryColor mt-1 sm:mt-4 md:mt-10 lg:mt-20  "
                  htmlType="submit"
                >
                  Leave us a message
                </Button>
              </ConfigProvider>
            </div>
          </Form>
        </div>
        <div className="bg-[#f3f3f3]">
          <div className="flex flex-row justify-between mt-20 mb-20  mx-auto px-4 pt-0 max-w-7xl mx-auto ">
            <div>
              <h3 className="font-normal mb-4 mt-4 text-base">Contact Info</h3>
              <div className="text-base/9">
                <h1>We are always</h1>
                <h1>happy to assist you.</h1>
              </div>
            </div>
            <div>
              <Image
                src={customer}
                alt="Image 2-1"
                className="mb-8"
                width={300}
              />
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </>
  );
}

export default ContactInformation;
