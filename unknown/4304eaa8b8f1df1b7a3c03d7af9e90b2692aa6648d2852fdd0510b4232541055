'use client'
import React, { useEffect, useState } from 'react';
import { Button, Card, Input, Modal, Form, Upload, message } from 'antd';
import { UserOutlined, CameraOutlined, PictureOutlined } from '@ant-design/icons';
import { useUserStore } from '@/store/userStore';
import { useLecturerStore } from '@/store/lecturerStore';
import { Loader } from '@/components/general/loader';
import NotFoundPage from '@/components/general/not-found';
import { useTeacherProfile } from '@/logics/useTeacherProfile';
import { useFetch } from '@/hooks/useFetch';

export default function LecturerProfile() {
   const { data, error } = useFetch('/protected/me')
  const { user } = useUserStore();
  const { Lecturer } = useLecturerStore();
  const { fetchTeacherProfile, updateTeacherProfile, uploadProfileImage, uploadCoverImage, loading, tokenExpired, form } = useTeacherProfile();
  const [, contextHolder] = Modal.useModal();
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewCoverImage, setPreviewCoverImage] = useState<string | null>(null);

  // Fetch teacher profile data on component mount
  useEffect(() => {
    fetchTeacherProfile();
  }, []);

  // Show not found page if token is expired
  if (loading) return <Loader />
  if (error) return <NotFoundPage code={error.status} value={error.data.detail} />

  // Show loader while data is being fetched
  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white">
        <Loader />
      </div>
    )
  }

  return (
    <div className="p-6">
      {contextHolder}
      <div className="flex justify-between items-center mb-6">
        {/* <h1 className="text-2xl font-semibold">
          {Lecturer && Lecturer.name ? Lecturer.name : (user ? `${user.first_name} ${user.last_name}` : 'Lecturer Profile')}
        </h1> */}
      </div>

      <div className="max-w-5xl mx-auto">
        {/* Combined Profile Card and Edit Form */}
        <Card className="shadow-lg border-teal-100 rounded-xl overflow-hidden hover:shadow-xl transition-shadow duration-300">
          {/* Cover Image */}
          <div className="relative h-48 bg-gradient-to-r from-teal-600 to-teal-800 overflow-hidden group">
            {/* Display cover image or preview */}
            {previewCoverImage ? (
              <img
                src={previewCoverImage}
                alt="Cover Preview"
                className="w-full h-full object-cover"
              />
            ) : Lecturer?.cover_image_path ? (
              <img
                src={String(Lecturer.cover_image_path)}
                alt="Cover"
                className="w-full h-full object-cover"
              />
            ) : Lecturer?.cover_image ? (
              <img
                src={Lecturer.cover_image.startsWith('data:') ? Lecturer.cover_image : `data:image/jpeg;base64,${Lecturer.cover_image}`}
                alt="Cover"
                className="w-full h-full object-cover"
              />
            ) : null}
            <div className="absolute inset-0 bg-teal-700 opacity-10"></div>

            {/* Cover Image Upload Overlay */}
            <Upload
              name="file"
              showUploadList={false}
              beforeUpload={(file) => {
                // Check file type and size
                if (!file.type.startsWith('image/')) {
                  message.error('You can only upload image files!');
                  return Upload.LIST_IGNORE;
                }

                if (file.size / 1024 / 1024 > 5) {
                  message.error('Image must be smaller than 5MB!');
                  return Upload.LIST_IGNORE;
                }

                // Create preview
                const reader = new FileReader();
                reader.onload = (e) => {
                  if (e.target) {
                    setPreviewCoverImage(e.target.result as string);
                  }
                };
                reader.readAsDataURL(file);

                // Handle upload
                uploadCoverImage(file).then((result) => {
                  if (result) {
                    setTimeout(() => setPreviewCoverImage(null), 500);
                  }
                });

                return false; // Prevent default upload behavior
              }}
            >
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center cursor-pointer">
                <PictureOutlined className="text-white text-2xl" />
                <span className="text-white text-base ml-2">Change Cover Image</span>
              </div>
            </Upload>
          </div>

          {/* Profile Header */}
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6 pb-6 border-b border-gray-100 relative">
            {/* Profile Image - Positioned to overlap with cover image */}
            <div className="-mt-16 ml-8 z-10">
              <div className="w-28 h-28 md:w-32 md:h-32 rounded-full bg-gray-100 overflow-hidden border-4 border-white shadow-md relative group">
                {/* Display profile image or preview */}
                {previewImage ? (
                  <img
                    src={previewImage}
                    alt="Profile Preview"
                    className="w-full h-full object-cover"
                  />
                ) : Lecturer?.profile_image_path ? (
                  <img
                    src={String(Lecturer.profile_image_path)}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : Lecturer?.profile_image ? (
                  <img
                    src={Lecturer.profile_image.startsWith('data:') ? Lecturer.profile_image : `data:image/jpeg;base64,${Lecturer.profile_image}`}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-teal-600 bg-teal-50">
                    <UserOutlined style={{ fontSize: '48px' }} />
                  </div>
                )}

                {/* Upload overlay */}
                <Upload
                  name="file"
                  showUploadList={false}
                  beforeUpload={(file) => {
                    // Check file type and size
                    if (!file.type.startsWith('image/')) {
                      message.error('You can only upload image files!');
                      return Upload.LIST_IGNORE;
                    }

                    if (file.size / 1024 / 1024 > 5) {
                      message.error('Image must be smaller than 5MB!');
                      return Upload.LIST_IGNORE;
                    }

                    // Create preview
                    const reader = new FileReader();
                    reader.onload = (e) => {
                      if (e.target) {
                        setPreviewImage(e.target.result as string);
                      }
                    };
                    reader.readAsDataURL(file);

                    // Handle upload
                    uploadProfileImage(file).then((result) => {
                      if (result) {
                        setTimeout(() => setPreviewImage(null), 500);
                      }
                    });

                    return false; // Prevent default upload behavior
                  }}
                >
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center cursor-pointer">
                    <CameraOutlined className="text-white text-2xl" />
                    <span className="text-white text-xs ml-1">Change</span>
                  </div>
                </Upload>
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 text-center md:text-left md:ml-4 mt-4 md:mt-0">
              <h2 className="text-2xl font-semibold text-teal-700 mb-1">
                {Lecturer && Lecturer.name ? Lecturer.name : (user ? `${user.first_name} ${user.last_name}` : 'Lecturer')}
              </h2>

              <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 mt-2 text-gray-700 font-medium">
                <p className="text-sm font-medium">
                  {/* Mask email: show first 3 chars, then *** then @ and domain */}
                  {(Lecturer?.email || user?.email)?.replace(/(?<=^.{3}).*(?=@)/g, '******')}
                </p>

                {(Lecturer?.phone || user?.phone) && (
                  <p className="text-sm font-medium flex items-center">
                    <span className="inline-block w-1 h-1 rounded-full bg-gray-500 mx-2 hidden md:block"></span>
                    {/* Show first 3 digits, mask middle, show last 2 digits */}
                    {(Lecturer?.phone || user?.phone)?.replace(/(?<=^.{3}).*(?=.{2}$)/g, '*****')}
                  </p>
                )}
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                {/* Rating Badge */}
                {Lecturer?.rating && (
                  <div className="inline-block bg-yellow-50 px-3 py-1 rounded-full">
                    <p className="text-yellow-700 text-sm font-medium">Rating: {Lecturer.rating} ⭐</p>
                  </div>
                )}

                {/* Availability Badge */}
                {Lecturer?.availability !== undefined && (
                  <div className={`inline-block px-3 py-1 rounded-full ${Lecturer.availability === true ? 'bg-green-50' : 'bg-red-50'}`}>
                    <p className={`text-sm font-medium ${Lecturer.availability === true ? 'text-green-700' : 'text-red-700'}`}>
                      Availability: {String(Lecturer.availability)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Edit Profile Form */}
          <div className="mt-8 px-2">
            <Form
              form={form}
              layout="vertical"
              onFinish={updateTeacherProfile}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                {/* Personal Information Section */}
                <div className="md:col-span-2">
                  <h3 className="text-lg font-medium text-teal-700 mb-3 border-b pb-2 border-teal-100">Personal Information</h3>
                </div>

                <Form.Item
                  name="first_name"
                  label={<span className="text-gray-900 font-medium">First Name</span>}
                >
                  <Input
                    placeholder="First Name"
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item>

                <Form.Item
                  name="last_name"
                  label={<span className="text-gray-900 font-medium">Last Name</span>}
                >
                  <Input
                    placeholder="Last Name"
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item>

                <Form.Item
                  name="email"
                  label={<span className="text-gray-900 font-medium">Email</span>}
                >
                  <Input
                    placeholder="joh******@example.com"
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item>

                <Form.Item
                  name="phone"
                  label={<span className="text-gray-900 font-medium">Phone Number</span>}
                >
                  <Input
                    placeholder="123*****89"
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item>

                {/* Professional Information Section */}
                <div className="md:col-span-2 mt-4">
                  <h3 className="text-lg font-medium text-teal-700 mb-3 border-b pb-2 border-teal-100">Professional Information</h3>
                </div>

                <Form.Item
                  name="specialization"
                  label={<span className="text-gray-900 font-medium">Specialization</span>}
                  className="md:col-span-2"
                >
                  <Input
                    placeholder={String(Lecturer?.specialization || "Your area of specialization")}
                    className="border-teal-200 hover:border-teal-400 focus:border-teal-500 text-black"
                    style={{ color: '#000000', fontWeight: 500 }}
                  />
                </Form.Item>

                <Form.Item
                  name="availability"
                  label={<span className="text-gray-900 font-medium">Availability</span>}
                  className="md:col-span-2"
                >
                  <Input
                    placeholder={Lecturer?.availability !== undefined ? String(Lecturer.availability) : ""}
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item>

                {/* <Form.Item
                  name="teacher_id"
                  label={<span className="text-gray-900 font-medium">Teacher ID</span>}
                  className="md:col-span-2"
                >
                  <Input
                    placeholder={Lecturer?.id || ""}
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item> */}

                {/* <Form.Item
                  name="created_at"
                  label={<span className="text-gray-900 font-medium">Joined</span>}
                  className="md:col-span-1"
                >
                  <Input
                    placeholder={Lecturer?.created_at ? new Date(Lecturer.created_at).toLocaleDateString() : ""}
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item> */}

                {/* <Form.Item
                  name="updated_at"
                  label={<span className="text-gray-900 font-medium">Last Updated</span>}
                  className="md:col-span-1"
                >
                  <Input
                    placeholder={Lecturer?.updated_at ? new Date(Lecturer.updated_at).toLocaleDateString() : ""}
                    disabled
                    className="bg-gray-50 text-black"
                    style={{ color: '#000000', opacity: 1, fontWeight: 500 }}
                  />
                </Form.Item> */}

                <Form.Item
                  name="bio"
                  label={<span className="text-gray-900 font-medium">Introduction</span>}
                  className="md:col-span-2"
                >
                  <Input.TextArea
                    rows={4}
                    placeholder={String(Lecturer?.introduction || "Tell us about yourself")}
                    className="border-teal-200 hover:border-teal-400 focus:border-teal-500 text-black"
                    style={{ color: '#000000', fontWeight: 500 }}
                  />
                </Form.Item>
              </div>

              <div className="text-sm bg-teal-50 p-4 rounded-md my-6 border-l-4 border-teal-500">
                <p className="text-teal-800 font-medium">Note: Some fields are disabled and can only be changed by an administrator.</p>
              </div>

              <div className="flex justify-end mt-4">
                <Button
                  type="primary"
                  htmlType="submit"
                  className="bg-teal-700 hover:bg-teal-800"
                >
                  Save Changes
                </Button>
              </div>
            </Form>
          </div>


        </Card>
      </div>
    </div>
  );
}
