import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest";
import { Spin, message } from "antd";
import { useState } from "react";
import { ReactNode } from "react";
import { processImageSource } from "@/utils/imageUtils";
import { Course } from "@/types";

export interface CourseType {
  [x: string]: any;
  id: string;
  courseCode: string;
  title: string;
  program: string;
  instructor: string;
  level: string;
  duration: string;
  price: string;
  status: string;
  description?: string | ReactNode;
}


export interface CourseData {
  name: string;
  code: string;
  credits: number;
  description: string;
  base_price: number;
  topics: Record<string, string> | string[];
  duration: number;
  level: string;
  category: string;
  tags: string[];
  cover_image?: File;
  has_cohorts?: boolean;
  has_curriculum: boolean;
  is_self_paced: boolean;
  cohort_duration_weeks: number | null;
  max_students_per_cohort: number;
  auto_create_cohorts: boolean;
  days_between_cohorts: number;
  is_published?: boolean;
  [key: string]: any;
}

interface CourseResponse {
  success: boolean;
  data?: any;
  error?: any;
  response?: any;
  exception?: any;
}

export const useCourseLogic = () => {
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);
  const [courses, setCourses] = useState<CourseType[]>([]);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedCourse, setSelectedCourse] = useState<CourseType | null>(null);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [descriptionChars, setDescriptionChars] = useState(500);

  // Function to handle description field changes
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const currentLength = e.target.value.length;
    setDescriptionChars(500 - currentLength);
  };

  // Filter courses based on search text
  const filteredCourses = courses.filter(course => {
    if (!course) return false;

    const searchLower = searchText.toLowerCase();
    return (
      (course.name && course.name.toLowerCase().includes(searchLower)) ||
      (course.code && course.code.toLowerCase().includes(searchLower)) ||
      (course.level && course.level.toLowerCase().includes(searchLower))
    );
  });

  console.log('Filtered courses:', filteredCourses);

  /**
   * Creates a new course with the provided data
   * @param courseData -
   * @returns
   */
  async function createCourse(courseData: CourseData): Promise<CourseResponse> {
    try {
      showNotification('success', 'Creating Course', 'Creating your course...', true, <Spin />);
      setLoading(true);

      // Prepare form data
      const formData = new FormData();

      // Handle the has_cohorts/has_cohortss field correctly
      if (courseData.has_cohorts !== undefined) {
        // Use has_cohorts (singular) for the API
        formData.append('has_cohorts', courseData.has_cohorts.toString());
      }

      // Handle cover image file
      if (courseData.cover_image) {
        console.log('Processing cover image:', courseData.cover_image);

        // Handle array of files from Ant Design Upload component
        if (Array.isArray(courseData.cover_image) && courseData.cover_image.length > 0) {
          const fileObj = courseData.cover_image[0];
          console.log('File object:', fileObj);

          // Handle the file object from Ant Design Upload
          if (fileObj.originFileObj) {
            console.log('Using originFileObj');
            // Append the file directly to the FormData with the field name 'cover_image'
            formData.append('cover_image', fileObj.originFileObj);

            // Process the image for preview if needed
            if (fileObj.thumbUrl) {
              setImageUrl(fileObj.thumbUrl);
            }
          } else if (fileObj.url) {
            console.log('Using URL from fileObj');
            setImageUrl(fileObj.url);
          }
        }
        // Handle direct File object
        else if (courseData.cover_image instanceof File) {
          console.log('Using direct File object');
          formData.append('cover_image', courseData.cover_image);
        } else {
          console.log('Unrecognized cover_image format:', typeof courseData.cover_image);
        }
      } else {
        console.log('No cover_image provided');
      }

      // Log the form data entries for debugging
      console.log('Form data entries:');
      for (const pair of formData.entries()) {
        console.log(pair[0], pair[1]);
      }

      // Add all other text fields
      Object.keys(courseData).forEach(key => {
        if (key !== 'cover_image' && key !== 'file' && key !== 'has_cohorts' && key !== 'has_cohortss' && courseData[key] !== undefined) {
          // Convert boolean values to strings
          if (typeof courseData[key] === 'boolean') {
            formData.append(key, courseData[key].toString());
          } else if (Array.isArray(courseData[key])) {
            formData.append(key, JSON.stringify(courseData[key]));
          } else if (key === 'topics' && typeof courseData[key] === 'object') {
            // Handle topics as an object
            formData.append(key, JSON.stringify(courseData[key]));
          } else {
            formData.append(key, courseData[key]);
          }
        }
      });

      // Ensure is_published is always included, even if it's false
      if (courseData.is_published === undefined || courseData.is_published === null) {
        formData.append('is_published', 'false');
      }

      // Create a clean copy of the course data for logging (without file objects)
      const logData = { ...courseData };
      delete logData.cover_image;
      delete logData.file;

      // Log the form data for debugging
      console.log('Creating course with data:', logData);

      // Make the API request
      const requestResponse = await request(
        "POST",
        "/course/",
        formData,
        "multipart/form-data"
      );

      setLoading(false);
      destroyNotifications();


      // Check for successful response (status 201 or 200)
      if (requestResponse && (requestResponse?.status === 201 || requestResponse?.status === 200)) {
        // Success case - course created successfully
        showNotification('success', 'Success', 'Course created successfully');
        console.log('Course created successfully with data:', requestResponse.data);

        // Course created successfully

        return {
          success: true,
          data: requestResponse.data.data || requestResponse.data
        };
      }
      // Special case: If the response contains a detail that mentions "SSL connection" or "scheduler",
      // it's likely the course was created but there was an issue with the notification scheduler
      else if (requestResponse?.data?.detail &&
              (typeof requestResponse.data.detail === 'string') &&
              (requestResponse.data.detail.includes('SSL connection') ||
               requestResponse.data.detail.includes('apscheduler') ||
               requestResponse.data.detail.includes('scheduler'))) {
        // Partial success case - course created but notification failed
        showNotification('success', 'Success', 'Course created successfully');
        return {
          success: true,
          data: { message: 'Course created but notification scheduling failed' }
        };
      }
      // Handle actual errors
      else {
        // Handle different error responses
        let errorMessage = 'An error occurred while creating the course';

        if (requestResponse?.data?.detail) {
          if (typeof requestResponse.data.detail === 'string') {
            errorMessage = requestResponse.data.detail;

            // Check for duplicate course errors
            if (errorMessage.includes('already exists') ||
                errorMessage.includes('duplicate') ||
                errorMessage.includes('unique constraint')) {
              errorMessage = 'This course already exists. Please use a different course code or name.';
            }
          } else if (Array.isArray(requestResponse.data.detail)) {
            errorMessage = requestResponse.data.detail[0]?.msg || 'Validation error';
          }
        } else if (requestResponse?.data?.message) {
          errorMessage = requestResponse.data.message;

          // Check for duplicate course errors in message
          if (errorMessage.includes('already exists') ||
              errorMessage.includes('duplicate') ||
              errorMessage.includes('unique constraint')) {
              errorMessage = 'This course already exists. Please use a different course code or name.';
          }
        } else if (typeof requestResponse?.data === 'string') {
          errorMessage = requestResponse.data;
        }

        showNotification('error', 'Error creating course', errorMessage);

        return {
          success: false,
          error: requestResponse?.data?.detail || errorMessage,
          response: requestResponse
        };
      }
    } catch (error: any) {
      setLoading(false);
      destroyNotifications();
      const errorMessage = error.message || 'An unexpected error occurred';
      showNotification('error', 'Error creating course', errorMessage);

      return {
        success: false,
        error: errorMessage,
        exception: error
      };
    }
  }

// Fetch teacher courses with pagination to prevent loading too many courses at once
const fetchTeacherCourses = async (page = 1, limit = 10) => {
  try {
    setLoading(true);
    // No notification, just use the main loader
    console.log(`Fetching teacher courses (page ${page}, limit ${limit})...`);

    // Use the paginated endpoint for courses
    // If the API doesn't have a dedicated paginated endpoint, we can add query parameters
    const response = await request(
      "GET",
      "/protected/me",
      null,
      "",
      { page, limit } // Add pagination parameters as query params
    );

    console.log('Teacher courses response:', response);
    setLoading(false);

    if (response && response.status === 200) {
      console.log('Response data:', response.data);

      // Check if courses exist in the response
      if (response.data.data.courses && Array.isArray(response.data.data.courses)) {
        console.log('Courses found:', response.data.data.courses);

        // Process courses to ensure duration fields are properly set
        const processedCourses = response.data.data.courses.map((course: Course) => {
          // If neither duration field is set, default to a reasonable value
          if (course.cohort_duration_weeks === null && course.duration === null) {
            return {
              ...course,
              duration: 8 // Default to 8 weeks if no duration is specified
            };
          }
          return course;
        });

        console.log('Processed courses:', processedCourses);

        // Update the courses state with the fetched courses
        setCourses(processedCourses);

        // Update pagination state if the API provides total count
        if (response.data.data.total_count) {
          // If the API returns total count, update the pagination state
          const totalCourses = response.data.data.total_count;
          console.log(`Total courses: ${totalCourses}`);
        }

        return {
          courses: processedCourses,
          totalCount: response.data.data.total_count || processedCourses.length,
          currentPage: page,
          pageSize: limit
        };
      } else {
        console.log('No courses found in response');
        setCourses([]);
        return {
          courses: [],
          totalCount: 0,
          currentPage: page,
          pageSize: limit
        };
      }
    } else {
      console.error('Course Logic - Error fetching courses:', response?.data);
      showNotification('error', 'Error', 'Failed to fetch courses');
      return {
        courses: [],
        totalCount: 0,
        currentPage: page,
        pageSize: limit
      };
    }
  } catch (error) {
    console.error('Course Logic - Exception:', error);
    setLoading(false);
    destroyNotifications();
    showNotification('error', 'Error', 'An unexpected error occurred');
    return {
      courses: [],
      totalCount: 0,
      currentPage: page,
      pageSize: limit
    };
  }
};

// Edit a course with enhanced functionality
const editCourse = async (courseId: string, values: any) => {
  if (!courseId) {
    console.error('No course ID provided');
    showNotification('error', 'Error', 'Course ID is missing');
    return null;
  }

  try {
    setLoading(true);

    // Prepare form data for multipart/form-data submission
    const formData = new FormData();

    // Create a JSON object for the API request
    const jsonData: any = {};

    Object.keys(values).forEach(key => {
      // Skip teacher_id to avoid foreign key constraint violation
      if (key === 'teacher_id') {
        return;
      }
      // Skip the courseImage field as it's handled separately
      if (key !== 'courseImage') {
        // Handle arrays (like topics)
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else if (typeof values[key] === 'boolean') {
          // Convert boolean to string
          formData.append(key, values[key].toString());
        } else if (values[key] !== null && values[key] !== undefined) {
          // Add other fields
          formData.append(key, values[key]);
        }
      }
    });

    // Ensure required fields are present
    if (!formData.has('name')) {
      showNotification('error', 'Error', 'Course name is required');
      setLoading(false);
      return null;
    }

    // Ensure boolean fields are correctly formatted
    ['has_cohorts', 'has_curriculum', 'is_self_paced', 'auto_create_cohorts'].forEach(field => {
      if (!formData.has(field)) {
        formData.append(field, 'false');
      }
    });

    // Handle image upload separately if needed
    if (values.courseImage && values.courseImage instanceof File) {
      // We'll handle image upload in a separate request if the course update succeeds
    } else if (values.cover_image_path) {
      jsonData.cover_image_path = values.cover_image_path;
    }

    // Extract data from formData to create the JSON payload
    for (let [key, value] of formData.entries()) {
      // Skip teacher_id to avoid foreign key constraint violation
      if (key === 'teacher_id') {
        continue;
      }
      // Handle arrays (like topics)
      if (key === 'topics') {
        try {
          jsonData[key] = JSON.parse(value as string);
        } catch (e) {
          jsonData[key] = [value];
        }
      }
      // Handle booleans
      else if (['has_cohorts', 'has_curriculum', 'is_self_paced', 'auto_create_cohorts'].includes(key)) {
        jsonData[key] = value === 'true';
      }
      // Handle numbers
      else if (['credits', 'base_price', 'duration', 'cohort_duration_weeks', 'max_students_per_cohort', 'days_between_cohorts'].includes(key)) {
        jsonData[key] = Number(value);
      }
      // Handle strings
      else {
        jsonData[key] = value;
      }
    }

    // Remove teacher_id from the update request to avoid foreign key constraint violation
    if (jsonData.teacher_id) {
      delete jsonData.teacher_id;
    }

    // Make a direct API request with JSON data
    const response = await request("PUT", `/course/${courseId}`, jsonData, "application/json");

    if (response && response.status === 200) {
      // Course update succeeded
      destroyNotifications();
      showNotification('success', 'Success', 'Course updated successfully');

      // Refresh the courses list
      await fetchTeacherCourses();

      // If there's a new image to upload, do it now (after the main update is complete)
      if (values.courseImage && values.courseImage instanceof File) {
        try {
          const imageFormData = new FormData();
          imageFormData.append('cover_image', values.courseImage);

          // Make a separate request to update just the image
          const imageResponse = await request(
            "PUT",
            `/course/${courseId}/image`,
            imageFormData,
            "multipart/form-data"
          );

          if (!(imageResponse && imageResponse.status === 200)) {
            console.error('Failed to upload image:', imageResponse?.data?.detail || 'Unknown error');
            // Don't show a notification for image upload failure to prevent double notification
          }
        } catch (imageError: any) {
          console.error('Error uploading image:', imageError);
          // Don't show a notification for image upload failure
        }
      }

      return response.data.data;
    } else {
      // Show error notification
      const errorMessage = response?.data?.detail || 'Failed to update course';
      console.error('Update course error:', errorMessage);
      showNotification('error', 'Error', errorMessage);
      return null;
    }
  } catch (error: any) {
    console.error('Error updating course:', error);
    const errorMessage = error.response?.data?.detail || error.message || 'An unexpected error occurred';
    showNotification('error', 'Error', errorMessage);
    setLoading(false);
    return null;
  } finally {
    setLoading(false);
  }
};

// Delete a course
const deleteCourse = async (courseId: string) => {
  if (!courseId) {
    console.error('No course ID provided');
    showNotification('error', 'Error', 'Course ID is missing');
    return false;
  }

  try {
    // Show notification with spinner
    showNotification(
      'info',
      'Deleting Course',
      'Deleting your course...',
      true,
      <Spin />
    );

    // Make the API request directly to the /course/{course_id} endpoint
    const response = await request("DELETE", `/course/${courseId}`, null, "");

    // Clear the notification
    destroyNotifications();

    if (response && response.status === 200) {
      // Show success notification
      showNotification('success', 'Success', 'Course deleted successfully');

      // Refresh the courses list without setting loading state
      // This prevents the full-page loader from appearing
      try {
        console.log('Fetching teacher courses after deletion...');
        // Use the paginated fetch to avoid loading too many courses at once
        await fetchTeacherCourses(currentPage, pageSize);
      } catch (refreshError) {
        console.error('Error refreshing courses after deletion:', refreshError);
        // Don't show an error notification here, as the delete was successful
      }

      return true;
    } else if (response && (response.status === 401 || response.status === 403)) {
      // Handle token expiration
      console.error('Token expired or unauthorized during delete:', response.status);
      showNotification('error', 'Session Expired', 'Your session has expired.');
      setTokenExpired(true);
      return false;
    } else {
      // Show error notification
      const errorMessage = response?.data?.detail || 'Failed to delete course';
      console.error('Delete course error:', errorMessage);
      showNotification('error', 'Error', errorMessage);
      return false;
    }
  } catch (error: any) {
    destroyNotifications();

    // Check if the error is due to token expiration
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.error('Token expired or unauthorized:', error.response.status);
      showNotification('error', 'Session Expired', 'Your session has expired.');
      setTokenExpired(true);
    } else {
      console.error('Error deleting course:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'An unexpected error occurred';
      showNotification('error', 'Error', errorMessage);
    }
    return false;
  }
};

  // Open edit modal with course data
  const openEditModal = (course: CourseType) => {
    setSelectedCourse(course);
    setIsEditModalVisible(true);
  };

  // Open view modal with course data
  const openViewModal = (course: CourseType) => {
    setSelectedCourse(course);
    setIsViewModalVisible(true);
  };

  // Open delete modal with course data
  const openDeleteModal = (course: CourseType) => {
    setSelectedCourse(course);
    setIsDeleteModalVisible(true);
  };

  /**
   * Publish or unpublish a course
   * @param courseId
   * @param publish
   * @returns
   */
  // Image upload functionality removed

  const publishCourse = async (courseId: string, publish: boolean): Promise<CourseResponse> => {
    try {
      // Don't set loading state when using the spinner notification
      // This prevents double spinners from appearing
      showNotification(
        'info',
        publish ? 'Publishing Course' : 'Unpublishing Course',
        publish ? 'Publishing your course...' : 'Unpublishing your course...',
        true,
        <Spin />
      );

      // Log the request details for debugging
      console.log(`Publishing course ${courseId} with status: ${publish}`);

      // First, get the current course data to ensure we have all required fields
      const courseResponse = await request(
        "GET",
        `/course/${courseId}`,
        null,
        "application/json"
      );

      if (!courseResponse || courseResponse.status !== 200) {
        console.error('Failed to fetch course data for publishing:', courseResponse?.data);
        throw new Error('Failed to fetch course data');
      }

      // Extract the course data
      const courseData = courseResponse.data.data;
      console.log('Current course data:', courseData);

      // Update only the is_published field
      const updateData = {
        is_published: publish
      };

      console.log('Sending update with data:', updateData);

      // Make the API request to update the course
      const response = await request(
        "PUT",
        `/course/${courseId}`,
        updateData,
        "application/json"
      );

      destroyNotifications();

      // Check for successful response
      if (response && response.status === 200) {
        showNotification(
          'success',
          'Success',
          publish ? 'Course published successfully' : 'Course unpublished successfully'
        );

        // Update the course in the local state
        const updatedCourses = courses.map(course => {
          if (course.id === courseId) {
            return {
              ...course,
              is_published: publish,
              published: publish,
              status: publish ? 'Published' : 'Draft'
            };
          }
          return course;
        });

        console.log('Updating courses in state with new publish status');
        setCourses(updatedCourses);

        return {
          success: true,
          data: response.data
        };
      } else {
        // Handle error
        const errorMessage = response?.data?.detail ||
          (publish ? 'Failed to publish course' : 'Failed to unpublish course');

        showNotification('error', 'Error', errorMessage);

        return {
          success: false,
          error: errorMessage,
          response: response
        };
      }
    } catch (error: any) {
      destroyNotifications();

      const errorMessage = error.message || 'An unexpected error occurred';
      showNotification('error', 'Error', errorMessage);

      return {
        success: false,
        error: errorMessage,
        exception: error
      };
    }
  };

  return {
    // Course data and state
    courses,
    setCourses,
    filteredCourses,
    selectedCourse,
    setSelectedCourse,
    fetchTeacherCourses,
    editCourse,
    deleteCourse,
    tokenExpired,

    // Search and pagination
    searchText,
    setSearchText,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,

    // Modal visibility
    isAddModalVisible,
    setIsAddModalVisible,
    isEditModalVisible,
    setIsEditModalVisible,
    isViewModalVisible,
    setIsViewModalVisible,
    isDeleteModalVisible,
    setIsDeleteModalVisible,

    // Image handling
    imageUrl,
    setImageUrl,

    // Description character counter
    descriptionChars,
    setDescriptionChars,
    handleDescriptionChange,

    // Course operations
    createCourse,
    publishCourse,
    openEditModal,
    openViewModal,
    openDeleteModal,

    // Loading state
    loading
  };
};
