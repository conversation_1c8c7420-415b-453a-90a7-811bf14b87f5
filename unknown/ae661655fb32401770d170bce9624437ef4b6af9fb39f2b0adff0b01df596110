import { ConfigProvider, Form, Input, ThemeConfig } from 'antd';
import React, { ReactNode } from 'react';

const { TextArea } = Input;

interface TextAreaTemplateInterface {
  className?: string;
  outerClassName?: string;
  placeHolder?: string;
  label: string;
  fieldName: string;
  errorMessage?: string;
  required?: boolean;
  disabled?: boolean;
  rows?: number;
  prefix?: ReactNode;
  suffix?: ReactNode;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  maxLength?: number;
  height?: string;
}

const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080",
  },
};

export default function TextAreaTemplate({
  className,
  outerClassName,
  placeHolder,
  label,
  fieldName,
  required = true,
  disabled,
  rows = 6,
  // Unused parameters but kept for interface compatibility
  prefix: _prefix,
  suffix: _suffix,
  value,
  onChange,
  maxLength = 255,
}: TextAreaTemplateInterface) {
  // Custom onChange handler to update character counter
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const currentLength = e.target.value.length;
    const remainingChars = maxLength - currentLength;

    // Update the character counter if it exists
    const charCounter = document.getElementById('char-counter');
    if (charCounter) {
      charCounter.textContent = remainingChars.toString();
    }

    // Call the original onChange handler if provided
    if (onChange) {
      onChange(e);
    }
  };
  return (
    <ConfigProvider theme={theme}>
      <div className={`mb-8 ${outerClassName}`}>
        <div className='text-xs text-textColor font-semibold mb-2'>{label}</div>
        <Form.Item
          className='my-0 py-0'
          name={fieldName}
          rules={[{ required: required, message: `${label} is required` }]}
        >
          <TextArea
            disabled={disabled}
            className={`mt-0 bg-transparent text-xs rounded-md border-primaryColor w-full course-form-field ${className}`} // Increased width with `w-full`
            placeholder={placeHolder}
            rows={rows} // Increased height with more rows
            style={{ minHeight: '180px', maxHeight:'180px', resize: 'none' }} // Fixed height with no resize option
            // value={value}
            onChange={handleChange}
            maxLength={maxLength}
            value={value?.slice(0, maxLength)}
          />
        </Form.Item>
      </div>
    </ConfigProvider>
  );
}