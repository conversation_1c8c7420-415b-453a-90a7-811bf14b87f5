'use client';

import { useState } from 'react';
import { motion, useMotionTemplate } from 'framer-motion';
import { getPricingPlans, PlanInterface } from '../../components/general/dummy-data/pricing-data';
import Link from 'next/link';
import Image from 'next/image';
import Layout from '@/components/general/indexPagelayout/layout';

interface PricingCardProps extends PlanInterface {
  lanyardColor?: string;
  lanyardPattern?: 'stripes' | 'dots' | 'solid';
}

const PricingCard = ({ 
  title, 
  description, 
  price, 
  features, 
  disabledFeatures, 
  recommended,
  lanyardColor = '#059669',
  lanyardPattern = 'stripes'
}: PricingCardProps) => {
  const [mouseX, setMouseX] = useState(0);
  const [mouseY, setMouseY] = useState(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
    setMouseX((e.clientX - left - width/2) / 25);
    setMouseY((e.clientY - top - height/2) / 25);
  };

  const rotateX = useMotionTemplate`${-mouseY}deg`;
  const rotateY = useMotionTemplate`${mouseX}deg`;
  const lanyardRotate = useMotionTemplate`${45 + mouseX * 0.5}deg`;

  const patternStyles = {
    stripes: `repeating-linear-gradient(-45deg, transparent, transparent 3px, ${lanyardColor} 3px, ${lanyardColor} 6px)`,
    dots: `radial-gradient(circle at 50% 50%, ${lanyardColor} 25%, transparent 26%)`,
    solid: lanyardColor,
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => { setMouseX(0); setMouseY(0); }}
      style={{
        transformStyle: 'preserve-3d',
        rotateX,
        rotateY,
      }}
      transition={{ 
        duration: 0.3,
        type: "spring",
        stiffness: 300
      }}
      className={`relative flex flex-col justify-between rounded-3xl p-8 ring-1 transform transition-all duration-300 ${
        recommended
          ? 'bg-teal-600/10 ring-teal-600 hover:bg-teal-600/20'
          : 'bg-white/5 ring-white/10 hover:bg-teal-600/10'
      }`}
    >
      {/* 3D Lanyard */}
      <motion.div
        className="absolute top-0 left-1/2 w-32 h-12 origin-bottom -translate-x-1/2 -translate-y-1/2"
        style={{
          transform: lanyardRotate,
          transformStyle: 'preserve-3d',
          background: patternStyles[lanyardPattern],
          clipPath: 'polygon(0 0, 100% 0, 90% 100%, 10% 100%)',
        }}
      >
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute bottom-0 left-1/2 w-6 h-6 bg-yellow-400 rounded-full -translate-x-1/2 translate-y-1/3 ring-2 ring-white" />
      </motion.div>

      <div className="relative" style={{ transformStyle: 'preserve-3d' }}>
        <div className="flex items-center justify-between gap-x-4">
          <h3 className="text-lg font-semibold leading-8 text-teal-700">
            {title}
          </h3>
          {recommended && (
            <p className="rounded-full bg-teal-600 px-2 py-1 text-xs font-semibold leading text-white">
              Most Popular
            </p>
          )}
        </div>
        <p className="mt-4 text-sm leading-6 text-gray-600">
          {description}
        </p>
        <p className="mt-6 flex items-baseline gap-x-1">
          <span className="text-4xl font-bold tracking-tight text-teal-700">
          GHS {price}
          </span>
          <span className="text-sm font-semibold leading-6 text-gray-500">
            /month
          </span>
        </p>
        <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-700">
          {features.map((feature) => (
            <li key={feature} className="flex gap-x-3 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-teal-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              {feature}
            </li>
          ))}
          {disabledFeatures?.map((feature) => (
            <li key={feature} className="flex gap-x-3 text-gray-400 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              {feature}
            </li>
          ))}
        </ul>
      </div>
      <div className="mt-8">
        <Image
          src="/pricing.png"
          alt="Pricing"
          width={200}
          height={200}
          className="rounded-lg"
        />
      </div>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`mt-8 block w-full rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 transition-all duration-300 ${
          recommended
            ? 'bg-teal-600 text-white hover:bg-teal-700 focus:outline-teal-600'
            : 'bg-teal-500/10 text-teal-700 hover:bg-teal-500/20 focus:outline-teal-500'
        }`}
      >
        Subscribe
      </motion.button>
    </motion.div>
  );
};

export default function PricingPage() {
  const [isYearly, setIsYearly] = useState(false);

  const plans = getPricingPlans(isYearly).map(plan => ({
    ...plan,
    lanyardColor: plan.recommended ? '#059669' : '#3B82F6',
    lanyardPattern: plan.recommended ? 'stripes' : 'dots' as const
  }));

  return (
    <Layout>
      <div className="py-24 sm:py-32 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="mx-auto max-w-7xl px-6 lg:px-8" style={{ perspective: 2000 }}>
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-teal-700 sm:text-4xl">
              Credential Verification Made Secure
            </h2>
            <p className="mt-2 text-lg leading-8 text-gray-600">
             {"Protect your organization's integrity with our advanced credential verification platform"}
            </p>
          </div>

          <div className="mt-6 flex justify-center">
            <div className="flex items-center gap-x-4">
              <span className={`text-sm ${!isYearly ? 'text-teal-700' : 'text-gray-400'}`}>
                Monthly
              </span>
              <button
                type="button"
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${
                  isYearly ? 'bg-teal-600' : 'bg-gray-300'
                }`}
                onClick={() => setIsYearly(!isYearly)}
              >
                <span className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  isYearly ? 'translate-x-5' : 'translate-x-0'
                }`} />
              </button>
              <span className={`text-sm ${isYearly ? 'text-teal-700' : 'text-gray-400'}`}>
                Yearly
                <span className="ml-2 text-xs text-teal-500">(Save up to 15%)</span>
              </span>
            </div>
          </div>

          <div className="mt-16 grid max-w-md grid-cols-1 gap-8 mx-auto lg:max-w-none lg:grid-cols-3">
            {plans.map((plan) => (
              <PricingCard key={plan.title} {...plan} lanyardPattern={plan.lanyardPattern as 'stripes' | 'dots' | 'solid'} />
            ))}
          </div>

          <div className="mt-16 text-center">
            <p className="text-sm text-gray-600">
              Need a custom solution? 
              <Link href="/customerSolution" className="ml-1 text-teal-700 hover:underline font-semibold">
                Contact our enterprise team
              </Link>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
}