"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { MailOutlined, LockOutlined } from "@ant-design/icons";
import { Form, FormProps } from "antd";
import { motion } from "framer-motion";
import InputTemplate from "@/components/ui/input-template";
import ButtonTemplate from "@/components/ui/button-template";
import CheckboxTemplate from "@/components/ui/checkbox-template";
import { AuthLogics } from "../_logics/auth-logics";
import { useEffect } from "react";
import Cookies from "js-cookie";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { type: "spring", stiffness: 120 }
  }
};

function SignInStudent() {
  const router = useRouter();
  const { loginUser, loading, authForm } = AuthLogics();

  const onFinish: FormProps<any>['onFinish'] = (info) => {
    if (info.remember) {
      Cookies.set("username", info.username, { secure: true });
      Cookies.set("password", info.password, { secure: true });
    } else {
      Cookies.remove("username");
    }
    loginUser(info);
  };

  useEffect(() => {
    const savedUsername = Cookies.get("username");
    const savedPassword = Cookies.get("password");
    if (savedUsername && savedPassword) {
      authForm.setFieldsValue({ 
        username: savedUsername, 
        password: savedPassword, 
        remember: true 
      });
    }
  }, []);

  return (
    <div className="min-h-dvh bg-gradient-to-br from-teal-50 to-teal-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-6xl bg-white rounded-2xl shadow-xl flex flex-col md:flex-row overflow-hidden"
      >
        {/* Left Form Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="flex-1 p-8 md:p-12 lg:p-16 flex flex-col justify-center"
        >
          {/* Logo Animation */}
          <motion.div variants={itemVariants} className="flex justify-center mb-8">
            <motion.div
              initial={{ scale: 0.8, rotate: -15 }}
              animate={{ 
                scale: 1,
                rotate: 0,
                y: [0, -15, 0],
              }}
              transition={{
                scale: { duration: 0.5 },
                rotate: { duration: 0.8, type: "spring" },
                y: {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
              className="shadow-lg rounded-full"
            >
              <Image 
                src="/logo.png" 
                alt="logo" 
                width={80} 
                height={80} 
                className="rounded-full"
              />
            </motion.div>
          </motion.div>

          {/* Welcome Title */}
          <motion.div variants={itemVariants} className="mb-10 text-center">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-teal-600 to-teal-400 bg-clip-text text-transparent">
              Welcome Back!
            </h1>
          </motion.div>

          {/* Login Form */}
          <Form name="signInForm" onFinish={onFinish} form={authForm}>
            <motion.div 
              variants={containerVariants}
              className="space-y-6 max-w-md mx-auto"
            >
              <motion.div variants={itemVariants}>
                <InputTemplate
                  fieldName="username"
                  prefix={<MailOutlined className="text-teal-600" />}
                  placeHolder="<EMAIL>"
                  label="Email"
                  className="rounded-lg border-gray-200 focus:border-teal-500"
                />
              </motion.div>

              <motion.div variants={itemVariants}>
                <InputTemplate
                  fieldName="password"
                  inputType="password"
                  prefix={<LockOutlined className="text-teal-600" />}
                  placeHolder="Enter your password"
                  label="Password"
                  className="rounded-lg border-gray-200 focus:border-teal-500"
                />
              </motion.div>

              <motion.div 
                variants={itemVariants}
                className="flex justify-between items-center"
              >
                <CheckboxTemplate fieldName="remember" />
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  onClick={() => router.push('/auth/forgot-password')}
                  className="text-teal-600 hover:text-teal-700 cursor-pointer text-sm font-medium"
                >
                  Forgot Password?
                </motion.div>
              </motion.div>

              <motion.div variants={itemVariants} className="text-center">
                <ButtonTemplate
                  htmlType="submit"
                  label={loading ? 'Loading...' : 'Login'}
                  className="w-full bg-teal-600 text-white py-3 rounded-xl font-semibold hover:teal-700  transition-all duration-300"
                  disabled={loading}
                />
              </motion.div>
            </motion.div>
          </Form>

          {/* Footer Links */}
          <motion.div 
            variants={itemVariants}
            className="text-center space-y-4 mt-12"
          >
            <p className="text-gray-600 text-sm">
              Don't have an account?{" "}
              <motion.span
                whileHover={{ scale: 1.05 }}
                onClick={() => router.push('/auth/signup')}
                className="text-teal-600 font-semibold cursor-pointer hover:underline"
              >
                Sign up
              </motion.span>{" "}
              •{" "}
              <motion.span
                whileHover={{ scale: 1.05 }}
                onClick={() => router.push('/auth/restore')}
                className="text-teal-600 font-semibold cursor-pointer hover:underline"
              >
                Restore account
              </motion.span>
            </p>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>

            <motion.div 
              className="flex justify-center gap-6"
              variants={itemVariants}
            >
              {['/google.png', '/faceb.png', '/apple.png'].map((src) => (
                <motion.div
                  key={src}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Image
                    src={src}
                    alt={src.split('/')[1].split('.')[0]}
                    width={40}
                    height={40}
                    className="cursor-pointer"
                  />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Right Image Section */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="hidden md:flex flex-1 bg-gradient-to-br from-teal-600 to-teal-500 relative min-h-[600px]"
        >
          {/* <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, repeat: Infinity, repeatType: "mirror" }}
            className="w-full h-full flex items-center justify-center p-8"
          > */}
            <Image
              src="/login2.gif"
              alt="Sign In Illustration"
              width={1200}
              height={800}
              className="object-contain object-center shadow-xl"
              priority
            />
          </motion.div>
        </motion.div>
      {/* </motion.div> */}
    </div>
  );
}

export default SignInStudent;