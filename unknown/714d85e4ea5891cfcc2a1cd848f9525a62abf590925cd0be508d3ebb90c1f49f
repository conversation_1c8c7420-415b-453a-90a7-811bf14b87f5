'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

// Define theme types and context
type ThemeMode = 'light' | 'dark';

interface ThemeContextType {
  theme: ThemeMode;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider component
export const TeacherThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Check local storage for saved theme preference or default to light
  const [theme, setTheme] = useState<ThemeMode>('light');
  
  useEffect(() => {
    // Get saved theme from local storage on component mount
    const savedTheme = localStorage.getItem('teacherDashboardTheme') as ThemeMode | null;
    if (savedTheme) {
      setTheme(savedTheme);
      applyTheme(savedTheme);
    } else {
      // Check system preference as fallback
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const defaultTheme = prefersDark ? 'dark' : 'light';
      setTheme(defaultTheme);
      applyTheme(defaultTheme);
    }
  }, []);

  // Apply theme classes to document
  const applyTheme = (newTheme: ThemeMode) => {
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // Save theme preference to local storage
    localStorage.setItem('teacherDashboardTheme', newTheme);
  };

  // Toggle theme function
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    applyTheme(newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, isDark: theme === 'dark' }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTeacherTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTeacherTheme must be used within a TeacherThemeProvider');
  }
  return context;
};

// Theme toggle button component
export const ThemeToggler: React.FC = () => {
  const { theme, toggleTheme } = useTeacherTheme();
  
  return (
    <motion.button
      onClick={toggleTheme}
      className={`p-3 rounded-lg cursor-pointer flex items-center justify-center
                  ${theme === 'dark' ? 'bg-gray-700 text-yellow-300' : 'bg-[#006060] text-white'}`}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      aria-label="Toggle theme"
    >
      {theme === 'dark' ? <SunOutlined className="text-lg" /> : <MoonOutlined className="text-lg" />}
    </motion.button>
  );
};

// CSS variables and theme application helper
export const getThemeStyles = (isDark: boolean) => {
  return {
    // Background colors
    bgPrimary: isDark ? '#252B42' : '#F4F8F7',
    bgSecondary: isDark ? '#252B42' : '#f8f9fa',
    bgTertiary: isDark ? '#3a3a3a' : '#E6F6F6',
    bgSidebar: isDark ? '#252B42' : '#006060',
    bgSidebarHover: isDark ? '#1e232e' : '#004645',
    bgHeader: isDark ? '#252B42' : '#0a6b6a', 
    bgCard: isDark ? '#2d3445' : '#ffffff',
    bgInput: isDark ? '#323845' : '#004645',
    
    // Text colors
    textPrimary: isDark ? '#ffffff' : '#333333',
    textSecondary: isDark ? '#c0c0c0' : '#666666',
    textMuted: isDark ? '#a0a0a0' : '#888888',
    
    // Border colors
    borderColor: isDark ? '#384058' : '#e0e0e0',
    borderHover: isDark ? '#4a5573' : '#006060',
    
    // Component-specific colors  
    primaryColor: isDark ? '#6d7fae' : '#006060',
    accentColor: isDark ? '#5d6e99' : '#006060',
    
    // Shadow
    boxShadow: isDark 
      ? '0 4px 6px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2)' 
      : '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
  };
};

export default ThemeToggler;