"use client";
import React, { useEffect, useRef, useState } from 'react';
import { Table, Button, Pagination, Select, Spin, Empty, Input, Modal } from 'antd';
import { ColumnsType, SorterResult } from 'antd/es/table/interface';
import dynamic from 'next/dynamic';
import { EyeOutlined, BellOutlined, DeleteOutlined, SearchOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useNotificationsLogic } from './_logics/notifications-logic';

const MotionDiv = dynamic(() => import('framer-motion').then(mod => mod.motion.div), { ssr: false });

interface UINotification {
  timestamp: any;
  key: string;
  sender: string;
  type: string;
  content: string;
  date: string;
  time: string;
  status: 'viewed' | 'unviewed';
}

export default function NotificationsPage() {
  const {
    fetchNotifications,
    markNotificationAsRead,
    deleteNotification,
    notifications,
    loading,
    pagination,
    // unreadCount is available but not used in this component
  } = useNotificationsLogic();

  // Use a ref to track if the initial fetch has been done
  const initialFetchDone = useRef(false);

  // Track loading state for each action type and notification ID separately
  const [actionLoading, setActionLoading] = React.useState<Record<string, boolean>>({});

  // Add state for search
  const [searchText, setSearchText] = useState('');

  // Add state for table sorting
  const [sortedInfo, setSortedInfo] = useState<SorterResult<UINotification>>({});

  // Add state for responsive design
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Add state for modal
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<UINotification | null>(null);

  useEffect(() => {
    // Only fetch notifications once when the component mounts
    if (!initialFetchDone.current) {
      fetchNotifications(1, pagination.pageSize, true);
      initialFetchDone.current = true;
    }
  }, [fetchNotifications, pagination.pageSize]);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 640);
      setIsTablet(width >= 640 && width < 1024);
    };

    // Check initially
    handleResize();

    // Listen for resize events
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handlePageChange = (page: number | undefined, pageSize: number | undefined) => {
    fetchNotifications(page, pageSize, false);
  };

  // Function to handle marking a notification as read with isolated loading state
  const handleMarkAsRead = async (notificationId: string) => {
    const actionKey = `view_${notificationId}`;
    setActionLoading(prev => ({ ...prev, [actionKey]: true }));

    await markNotificationAsRead(notificationId, true);

    const event = new CustomEvent('notification', {
      detail: { type: 'success', title: 'Success', message: 'Notification marked as read!' }
    });
    window.dispatchEvent(event);

    setActionLoading(prev => ({ ...prev, [actionKey]: false }));
    
    // If this was done from the modal, update the selected notification status
    if (selectedNotification && selectedNotification.key === notificationId) {
      setSelectedNotification(prev => prev ? {...prev, status: 'viewed'} : null);
    }
  };

  // Function to handle deleting a notification with isolated loading state
  const handleDelete = async (notificationId: string) => {
    const actionKey = `delete_${notificationId}`;
    setActionLoading(prev => ({ ...prev, [actionKey]: true }));

    await deleteNotification(notificationId, true);

    const event = new CustomEvent('notification', {
      detail: { type: 'success', title: 'Success', message: 'Notification deleted successfully!' }
    });
    window.dispatchEvent(event);

    setActionLoading(prev => ({ ...prev, [actionKey]: false }));
    
    // If this was done from the modal, close the modal
    if (selectedNotification && selectedNotification.key === notificationId) {
      setModalVisible(false);
      setSelectedNotification(null);
    }
  };

  // Function to open notification detail modal
  const openNotificationModal = (notification: UINotification) => {
    setSelectedNotification(notification);
    setModalVisible(true);
  };

  // Added defensive check to ensure notifications is an array
  const transformedNotifications = Array.isArray(notifications)
    ? notifications.map(n => ({
      key: n.id,
      sender: n.sender_name,
      type: n.type,
      content: n.content,
      date: new Date(n.created_at).toLocaleDateString(),
      time: new Date(n.created_at).toLocaleTimeString(),
      timestamp: new Date(n.created_at).getTime(),
      status: n.is_read ? ('viewed' as const) : ('unviewed' as const)
    }))
    : [];

  // Filter notifications based on search text
  const filteredNotifications = transformedNotifications.filter(notification =>
    notification.content.toLowerCase().includes(searchText.toLowerCase()) ||
    notification.sender.toLowerCase().includes(searchText.toLowerCase()) ||
    notification.type.toLowerCase().includes(searchText.toLowerCase())
  );

  // Handle table change for sorting
  const handleTableChange = (
    _pagination: any,
    _filters: any,
    sorter: SorterResult<UINotification> | SorterResult<UINotification>[],
    _extra: any
  ) => {
    if (Array.isArray(sorter)) {
      setSortedInfo(sorter[0] || {});
    } else {
      setSortedInfo(sorter);
    }
  };

  // Sort the filtered notifications based on the current sort configuration
  const sortedNotifications = React.useMemo(() => {
    if (!sortedInfo.column) return filteredNotifications;

    return [...filteredNotifications].sort((a, b) => {
      const columnKey = sortedInfo.column?.key as keyof UINotification;

      if (sortedInfo.order === 'ascend') {
        return a[columnKey] > b[columnKey] ? 1 : -1;
      }

      if (sortedInfo.order === 'descend') {
        return a[columnKey] < b[columnKey] ? 1 : -1;
      }

      return 0;
    });
  }, [filteredNotifications, sortedInfo]);

  // Get visible columns based on screen size
  const getColumns = (): ColumnsType<UINotification> => {
    // For mobile, only show sender, type, and actions (removed status)
    if (isMobile) {
      return [
        {
          title: 'Sender',
          dataIndex: 'sender',
          key: 'sender',
          sorter: (a, b) => a.sender.localeCompare(b.sender),
          sortOrder: sortedInfo.columnKey === 'sender' ? sortedInfo.order : null,
          width: 120,
          fixed: 'left',
          render: (text, record) => (
            <div className="cursor-pointer hover:text-teal-600" onClick={() => openNotificationModal(record)}>
              {text}
            </div>
          ),
        },
        {
          title: 'Type',
          dataIndex: 'type',
          key: 'type',
          sorter: (a, b) => a.type.localeCompare(b.type),
          sortOrder: sortedInfo.columnKey === 'type' ? sortedInfo.order : null,
          width: 120,
          render: (text, record) => (
            <div className="cursor-pointer hover:text-teal-600" onClick={() => openNotificationModal(record)}>
              {text}
            </div>
          ),
        },
        {
          title: 'Actions',
          key: 'actions',
          width: 60,
          fixed: 'right',
          render: (_: any, record: { key: string | number; }) => (
            <div className="flex justify-center">
              <DeleteOutlined 
                className="text-lg text-red-500 cursor-pointer hover:text-red-700"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(String(record.key));
                }}
              />
            </div>
          ),
        },
      ];
    }

    // For tablet and desktop, show all columns
    const baseColumns: ColumnsType<UINotification> = [
      {
        title: 'Sender',
        dataIndex: 'sender',
        key: 'sender',
        sorter: (a, b) => a.sender.localeCompare(b.sender),
        sortOrder: sortedInfo.columnKey === 'sender' ? sortedInfo.order : null,
        width: isTablet ? 120 : 'auto',
        fixed: isTablet ? 'left' : undefined,
        render: (text, record) => (
          <div 
            className={`${isTablet ? 'cursor-pointer hover:text-teal-600' : ''}`}
            onClick={() => isTablet ? openNotificationModal(record) : null}
          >
            {text}
          </div>
        ),
      },
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        sorter: (a, b) => a.type.localeCompare(b.type),
        sortOrder: sortedInfo.columnKey === 'type' ? sortedInfo.order : null,
        width: isTablet ? 120 : 'auto',
        render: (text, record) => (
          <div 
            className={`${isTablet ? 'cursor-pointer hover:text-teal-600' : ''}`}
            onClick={() => isTablet ? openNotificationModal(record) : null}
          >
            {text}
          </div>
        ),
      },
      {
        title: 'Content',
        dataIndex: 'content',
        key: 'content',
        sorter: (a, b) => a.content.localeCompare(b.content),
        sortOrder: sortedInfo.columnKey === 'content' ? sortedInfo.order : null,
        ellipsis: true,
        width: isTablet ? 200 : 'auto',
        render: (text, record) => (
          <div 
            className={`max-w-[150px] md:max-w-[300px] truncate ${isTablet ? 'cursor-pointer hover:text-teal-600' : ''}`}
            onClick={() => isTablet ? openNotificationModal(record) : null}
          >
            {text}
          </div>
        ),
      },
      {
        title: 'Date',
        dataIndex: 'date',
        key: 'date',
        sorter: (a, b) => a.timestamp - b.timestamp,
        sortOrder: sortedInfo.columnKey === 'date' ? sortedInfo.order : null,
        width: isTablet ? 120 : 'auto',
      },
      {
        title: 'Time',
        dataIndex: 'time',
        key: 'time',
        sorter: (a, b) => a.time.localeCompare(b.time),
        sortOrder: sortedInfo.columnKey === 'time' ? sortedInfo.order : null,
        width: isTablet ? 120 : 'auto',
      },
      {
        title: 'Status',
        key: 'status',
        width: isTablet ? 100 : 'auto',
        render: (_: any, record: { key: string | number; status: string }) => (
          isTablet ? (
            <div className="flex justify-center">
              <EyeOutlined 
                className={`text-lg ${record.status === 'viewed' ? 'text-gray-400 cursor-not-allowed' : 'text-teal-600 cursor-pointer hover:text-teal-800'}`}
                onClick={(e) => {
                  if (record.status !== 'viewed') {
                    e.stopPropagation();
                    handleMarkAsRead(String(record.key));
                  }
                }}
              />
            </div>
          ) : (
            <Button
              type="default"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleMarkAsRead(String(record.key));
              }}
              disabled={record.status === 'viewed'}
              loading={actionLoading[`view_${record.key}`] || false}
              className="rounded-md whitespace-nowrap"
              style={{
                backgroundColor: 'transparent',
                borderColor: 'transparent',
                color: 'inherit',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#0f766e';
                e.currentTarget.style.color = '#ffffff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'inherit';
              }}
            >
              {record.status}
            </Button>
          )
        ),
      },
      {
        title: 'Actions',
        key: 'actions',
        width: isTablet ? 60 : 'auto',
        fixed: isTablet ? 'right' : undefined,
        render: (_: any, record: { key: string | number; }) => (
          isTablet ? (
            <div className="flex justify-center">
              <DeleteOutlined 
                className="text-lg text-red-500 cursor-pointer hover:text-red-700"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(String(record.key));
                }}
              />
            </div>
          ) : (
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(String(record.key));
              }}
              loading={actionLoading[`delete_${record.key}`] || false}
              className="whitespace-nowrap"
            >
              Delete
            </Button>
          )
        ),
      },
    ];

    return baseColumns;
  };

  // Get the columns based on current screen size
  const columns = getColumns();

  return (
    <div className="w-full p-2 sm:p-4 md:p-6">
      <MotionDiv
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative w-full flex flex-col items-center"
      >
        <div className="w-full relative">
          <img src="/notification.png" alt="Notifications Header" className="w-full h-[100px] sm:h-[150px] md:h-auto object-cover rounded-lg" />
          <div className="absolute inset-0 flex items-center justify-center">
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white text-center">My Notifications</h1>
          </div>
        </div>
        <div className="absolute top-[100px] sm:top-[150px] md:top-[220px] bg-white p-3 rounded-full shadow-lg">
          <BellOutlined className="text-xl sm:text-2xl text-gray-600" />
        </div>
      </MotionDiv>

      <div className="mt-12 sm:mt-16 md:mt-20">
        <MotionDiv initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-5">
            <h1 className="text-xl font-semibold sm:text-2xl md:text-3xl" style={{ color: 'var(--buttonColor)' }}>
              All Notifications
            </h1>
            <Input
              prefix={<SearchOutlined />}
              placeholder="Search notifications"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{
                width: '88%',
                maxWidth: isMobile ? '100%' : '200px',
                borderColor: '#0f766e',
                color: '#0f766e',
              }}
              className="text-teal-700"
            />
          </div>

          {loading && initialFetchDone.current === false ? (
            <div className="text-center py-8">
              <Spin tip="Loading notifications..." size="large" />
            </div>
          ) : (
            <>
              <div className="overflow-x-auto overflow-y-auto max-h-[60vh]">
                <Table
                  columns={columns}
                  dataSource={sortedNotifications}
                  pagination={false}
                  bordered
                  onChange={handleTableChange}
                  locale={{
                    emptyText: searchText
                      ? <Empty description="No notifications match your search" />
                      : <Empty description="No notifications found" />
                  }}
                  rowKey="key"
                  loading={loading && initialFetchDone.current === true}
                  scroll={{ x: 'max-content', y: 500 }}
                  size={isMobile ? "small" : "middle"}
                  sticky
                  onRow={(record) => ({
                    onClick: () => {
                      if (isMobile || isTablet) {
                        openNotificationModal(record);
                      }
                    },
                    style: (isMobile || isTablet) ? { cursor: 'pointer' } : {}
                  })}
                />
              </div>

              <div className="flex flex-col sm:flex-row justify-between sm:justify-end mt-4 items-center gap-4">
                <Select
                  value={pagination.pageSize}
                  onChange={(value) => handlePageChange(1, value)}
                  options={[
                    { value: 5, label: '5 / page' },
                    { value: 10, label: '10 / page' },
                    { value: 20, label: '20 / page' },
                  ]}
                  style={{ width: isMobile ? '100%' : 120 }}
                  className="w-full sm:w-auto"
                />
                <Pagination
                  current={pagination.page}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  size={isMobile ? "small" : "default"}
                  className="mt-2 sm:mt-0"
                />
              </div>
            </>
          )}
        </MotionDiv>
      </div>

      {/* Notification Detail Modal - Modified layout for date, time, and status to be in a row */}
      <Modal
        title={<div className="text-center text-teal-700 font-semibold">Notification Details</div>}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        centered
        maskClosable={true}
        closeIcon={<CloseCircleOutlined className="text-teal-700" />}
        width={isMobile ? '90%' : 500}
      >
        {selectedNotification && (
          <div className="p-2">
            <div className="grid grid-cols-1 gap-4">
              {/* Sender and type in a row */}
              <div className="flex flex-row justify-start items-center mb-2 gap-8">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-500 mb-1">Sender</label>
                  <div className="text-base">{selectedNotification.sender}</div>
                </div>
                
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-500 mb-1">Type</label>
                  <div className="text-base">{selectedNotification.type}</div>
                </div>
              </div>
              
              {/* Content with justify-start */}
              <div className="mb-2 text-left">
                <label className="block text-sm font-medium text-gray-500 mb-1">Content</label>
                <div className="text-base overflow-y-auto max-h-32 px-3 py-2 bg-gray-50 rounded-md text-left">
                  {selectedNotification.content}
                </div>
              </div>
              
              {/* Date, time, and status combined in a flex row */}
              <div className="flex flex-row justify-start items-center mb-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Date</label>
                  <div className="text-base">{selectedNotification.date}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Time</label>
                  <div className="text-base">{selectedNotification.time}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                  <div className="text-base">{selectedNotification.status}</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-6 mt-2">
                <Button
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={() => handleMarkAsRead(String(selectedNotification.key))}
                  disabled={selectedNotification.status === 'viewed'}
                  loading={actionLoading[`view_${selectedNotification.key}`] || false}
                  style={{ 
                    backgroundColor: '#0f766e',
                    width: '140px',
                  }}
                >
                  Mark as Read
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(String(selectedNotification.key))}
                  loading={actionLoading[`delete_${selectedNotification.key}`] || false}
                  style={{ 
                    width: '140px',
                  }}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}