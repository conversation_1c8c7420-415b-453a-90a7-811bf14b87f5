import { useState, useCallback, useEffect } from "react";
import { useApi } from "@/hooks/useRequest";
import { Spin } from "antd";
import { useNotification } from "@/hooks/useNotifs";
import Cookies from "js-cookie";

interface ApiNotification {
  id: string;
  user_id: string;
  sender_id: string;
  sender_name: string;
  type: string;
  content: string;
  created_at: string;
  is_read: boolean;
}

interface Pagination {
  page: number;
  pageSize: number;
  total: number;
}

export const useNotificationsLogic = () => {
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification();
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState<ApiNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [deletedNotificationIds, setDeletedNotificationIds] = useState<string[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 5,
    total: 0,
  });

  // Get the current user ID from cookies or other auth sources
  const getCurrentUserId = () => {
    let userId = Cookies.get("user_id");
    
    if (!userId) {
      const accessToken = Cookies.get("access_token");
      
      if (accessToken) {
        try {  
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
  
          const payload = JSON.parse(jsonPayload);
          userId = payload.sub || payload.user_id || payload.id;
        } catch (e) {
          console.error("Error decoding access token:", e);
        }
      }
    }
    
    return userId;
  };

  // Fetch notifications method
  const fetchNotifications = useCallback(async (page?: number, pageSize?: number, showLoadingUI: boolean = true) => {
    try {
      setLoading(true);
      
      if (showLoadingUI) {
        destroyNotifications();
        showNotification("info", "Fetching Notifications", "Loading your notifications...", true, <Spin />);
      }

      const userId = getCurrentUserId();
      
      const currentPage = page ?? pagination.page;
      const currentPageSize = pageSize ?? pagination.pageSize;
      
      const response = await request(
        "GET", 
        `/notification/user/${userId}`, 
        {
          params: {
            page: currentPage,
            page_size: currentPageSize
          }
        }
      );
      
      const apiResponse = response.data;

      if (!apiResponse) {
        throw new Error("No response data received from API");
      }

      if (apiResponse.status === "success" && apiResponse.data) {
        let notificationsData = [];
        let totalItems = 0;
        
        if (apiResponse.data.notifications) {
          notificationsData = apiResponse.data.notifications;
          totalItems = apiResponse.data.total || notificationsData.length;
        } else if (Array.isArray(apiResponse.data)) {
          notificationsData = apiResponse.data;
          totalItems = apiResponse.total || notificationsData.length;
        } else {
          notificationsData = apiResponse.data;
          totalItems = apiResponse.total || 0;
        }
        
        // Filter out deleted notifications
        const filteredNotifications = notificationsData.filter(
          (n: ApiNotification) => !deletedNotificationIds.includes(n.id)
        );
        
        // Calculate unread count
        const unreadNotifications = filteredNotifications.filter((n: { is_read: any; }) => !n.is_read).length;
        setUnreadCount(unreadNotifications);
        
        setNotifications(filteredNotifications);
        
        setPagination(prev => ({
          ...prev,
          page: currentPage,
          pageSize: currentPageSize,
          total: filteredNotifications.length,
        }));

        // Only show loading success if there are notifications
        if (showLoadingUI) {
          destroyNotifications();
          if (filteredNotifications.length > 0) {
            showNotification("success", "Notifications", "Notifications retrieved!");
          } else {
            showNotification('info', 'No Notifications', 'You have no notifications.');
          }
        }
      } else {
        console.error("API returned unsuccessful response:", apiResponse);
        setNotifications([]);
        setUnreadCount(0);
        
        if (showLoadingUI) {
          destroyNotifications();
          showNotification('info', 'No Notifications', 'You have no notifications.');
        }
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setNotifications([]);
      setUnreadCount(0);
      
      if (showLoadingUI) {
        destroyNotifications();
        showNotification("error", "Error", "Failed to fetch notifications. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  }, [request, pagination.page, pagination.pageSize, showNotification, destroyNotifications, deletedNotificationIds]);

  // Mark notification as read method (updated)
  const markNotificationAsRead = async (notification_id: string, silent: boolean = false) => {
    try {
      // Always set loading to true and destroy previous notifications
      setLoading(true);
      destroyNotifications();

      // Show loading notification if not in silent mode
      if (!silent) {
        showNotification("info", "Updating Notification", "Marking notification as read...", true, <Spin />);
      }

      const response = await request("PUT", `/notification/${notification_id}`, { is_read: true });
      
      if (response && response.data && response.data.status === "success") {
        setNotifications(prev => {
          const updatedNotifications = prev.map(n => 
            n.id === notification_id ? { ...n, is_read: true } : n
          );
          
          // Recalculate unread count
          const newUnreadCount = updatedNotifications.filter(n => !n.is_read).length;
          setUnreadCount(newUnreadCount);
          
          return updatedNotifications;
        });
        
        // Always show a success toast (even if silent was true)
        destroyNotifications();
        showNotification("success", "Notification", "Notification marked as read!");
      } else {
        throw new Error("Failed to mark notification as read");
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      
      // Always show an error toast
      destroyNotifications();
      showNotification("error", "Error", "Failed to mark notification as read. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Delete notification method (updated)
  const deleteNotification = async (notification_id: string, silent: boolean = false) => {
    try {
      // Always set loading to true and destroy previous notifications
      setLoading(true);
      destroyNotifications();

      // Show loading notification if not in silent mode
      if (!silent) {
        showNotification("info", "Deleting Notification", "Deleting notification...", true, <Spin />);
      }

      const response = await request("DELETE", `/notification/${notification_id}`);
      
      if (response && response.data && response.data.status === "success") {
        // Add the deleted notification ID to prevent re-fetching
        setDeletedNotificationIds(prev => [...prev, notification_id]);

        setNotifications(prev => {
          const updatedNotifications = prev.filter(n => n.id !== notification_id);
          
          // Recalculate unread count
          const newUnreadCount = updatedNotifications.filter(n => !n.is_read).length;
          setUnreadCount(newUnreadCount);
          
          return updatedNotifications;
        });
        
        // Always show a success toast (even if silent was true)
        destroyNotifications();
        showNotification("success", "Notification", "Notification deleted successfully!");
        
        // Refresh pagination if last item on page is deleted
        if (notifications.length === 1 && pagination.page > 1) {
          fetchNotifications(pagination.page - 1, pagination.pageSize, false);
        }
      } else {
        throw new Error("Failed to delete notification");
      }
    } catch (error) {
      console.error("Error deleting notification:", error);
      
      // Always show an error toast
      destroyNotifications();
      showNotification("error", "Error", "Failed to delete notification. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return {
    fetchNotifications,
    markNotificationAsRead,
    deleteNotification,
    notifications,
    loading,
    pagination,
    unreadCount,
  };
};