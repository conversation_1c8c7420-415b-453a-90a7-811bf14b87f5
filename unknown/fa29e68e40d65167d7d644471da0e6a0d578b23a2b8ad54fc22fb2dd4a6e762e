


"use client";

import { use, useEffect } from 'react';
import CoursePageDetails from './page-details';

type Params = Promise<{ id: string }>

export default function CourseDetails(props: { params: Params }) {
  const params = use(props.params);
  const rcdId = params.id;
  useEffect(()=>{
    console.log('ricd', params)
      }, [])

  return (
        <CoursePageDetails id={rcdId} />
      );
    

}

// export default async function CourseDetails(props: { params: Params }) {
//   const params = await props.params;
//   const rcdId = params.rcdId;
  
//   useEffect(()=>{
// console.log('ricd', rcdId)
//   }, [])

//   return (
//     <CoursePageDetails id={rcdId} />
//   );
// }

// interface PageProps {
//   params: { id: string };
// }
// const CourseDetails = ({params}:PageProps) => {
  
   

//   return (
//     <CoursePageDetails id={params.id} />
//   );
// };

// export default CourseDetails;
