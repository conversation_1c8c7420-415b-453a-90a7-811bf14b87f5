import { useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);


  const request = async (method: "POST" | "PUT" | "DELETE" | "GET", endpoint: string, data?: any, contentType?: any, params?:any) => {
    setLoading(true);
    setError(null);
    try {
      // Debug: Check if token exists
      const token = Cookies.get("access_token");
      console.log(`useRequest - Token exists: ${!!token}`, token ? token.substring(0, 15) + '...' : 'No token');
      console.log(`useRequest - Calling ${method} ${endpoint}`);

      const response = await axios({
        method,
        url: `${API_BASE_URL}${endpoint}`,
        params: params,
        data: data || null,
        headers: {
          'Content-Type': contentType??'application/json',
          Authorization: token ? `Bearer ${token}` : "",
        },
      });

      // Debug: Log response status
      console.log(`useRequest - ${method} ${endpoint} response status:`, response.status);
      return response;
    } catch (err:any) {
      setError("Request failed");
      console.error(err);
      return err?.response

    } finally {
      setLoading(false);
      }
  };

  return { request, loading, error };
};