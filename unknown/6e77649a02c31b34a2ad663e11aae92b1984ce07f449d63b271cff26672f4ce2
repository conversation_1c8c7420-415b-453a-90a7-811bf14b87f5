.loader {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bar {
  display: inline-block;
  width: 6px;
  height: 30px;
  background-color: rgba(255, 255, 255, .7);
  border-radius: 10px;
  animation: scale-up4 1s linear infinite;
}

.bar:nth-child(2) {
  height: 45px;
  margin: 0 8px;
  animation-delay: .25s;
}

.bar:nth-child(3) {
  animation-delay: .5s;
}

@keyframes scale-up4 {
  20% {
    background-color: #ffffff;
    transform: scaleY(1.5);
  }

  40% {
    transform: scaleY(1);
  }
}