import { ReactNode } from 'react';
import { LaptopOutlined, BookOutlined, MedicineBoxOutlined, WindowsOutlined, SecurityScanOutlined, RiseOutlined } from '@ant-design/icons';

interface LearningPath {
  title: ReactNode;
  subtitle: string;
  courses: number;
  icon: ReactNode;
  iconBg: string;
  link: string;
}

export const learningPaths: LearningPath[] = [
  { 
    title: <span style={{ color: '#008080' }}>Computer Science</span>, 
    subtitle: 'From coding basics to advanced algorithms',
    courses: 156, 
    icon: <LaptopOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/computer-science' 
  },
  { 
    title: <span style={{ color: '#008080' }}>Law and Humanitarian Studies</span>, 
    subtitle: 'Study legal systems and human rights principles',
    courses: 1689, 
    icon: <BookOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/law-humanitarian-studies'
  },
  { 
    title: <span style={{ color: '#008080' }}>Medical Nursing</span>, 
    subtitle: 'Learn essential patient care and clinical procedures',
    courses: 2006, 
    icon: <MedicineBoxOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/medical-nursing'
  },
  { 
    title: <span style={{ color: '#008080' }}>Human-Computer Interaction</span>, 
    subtitle: 'Design user-friendly interfaces and digital experiences',
    courses: 1234, 
    icon: <WindowsOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/human-computer-interaction'
  },
  { 
    title: <span style={{ color: '#008080' }}>Computer Security</span>, 
    subtitle: 'Develop advanced security protocols and protections',
    courses: 2560, 
    icon: <SecurityScanOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/computer-security'
  },
  { 
    title: <span style={{ color: '#008080' }}>Business and Management</span>, 
    subtitle: 'Develop core business and leadership capabilities',
    courses: 1236, 
    icon: <RiseOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />,
    iconBg: '#fadbd8',
    link: '/business-management'
  },
];