'use client';
import ButtonTemplate from "@/components/ui/button-template";
import { ClockCircleOutlined, FileTextOutlined } from "@ant-design/icons";
import { Progress, Rate, Tag } from "antd";
import { useRouter } from "next/navigation";

const RecordingCard = () => {
    const router = useRouter();

    return (
        <div  className=" bg-white text-sm w-full rounded-lg overflow-hidden shadow-lg px-4  border border-gray-200 hover:cursor-pointer">
            <div className="relative pt-4">
                <img
                    className="w-full h-[9rem] object-cover rounded-lg"
                    src="/design.png" // Replace with actual image path
                    alt="Recording Image"
                />

            </div>


            <div className=" py-2">
                <div className="font-bold text-sm mb-2 text-black">
                    Design Thinking
                </div>
                <div className="flex gap-4 mb-2">
                    <span className="flex gap-1">
                        <ClockCircleOutlined />
                        <div>1:30 hrs</div>
                    </span>
                    <span className="flex gap-1">
                        <FileTextOutlined />
                        <div>02 Lessons</div>
                    </span>
                </div>
                <div className="">
                    <ButtonTemplate className="bg-primaryColor text-white" label={'Watch Now'}  />
                </div>
               


            </div>
        </div>
    );
};

export default RecordingCard;
