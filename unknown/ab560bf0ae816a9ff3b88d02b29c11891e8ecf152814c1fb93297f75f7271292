// import {ConfigProvider, Input, ThemeConfig } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { ConfigProvider,  Form,  GetProp,  Input, ThemeConfig, Upload, UploadProps } from 'antd'
import TextArea from 'antd/es/input/TextArea'
import React, { ReactNode, useState } from 'react'

interface InputTemplateInterface {
  prefix?: ReactNode
  visible?: boolean
  suffix?: ReactNode
  className?: string
  placeHolder?: string
  label: string
  fieldName: string
  errorMessage?: string
  textarea?: boolean
  required?: boolean
  disabled?: boolean
  textareaRows?: number
  inputType?: 'button' | 'checkbox' | 'color' | 'date' | 'datetime-local' | 'email' | 'file' | 'hidden' | 'image' | 'month' | 'number' | 'password' | 'radio' | 'range' | 'reset' | 'search' | 'submit' | 'tel' | 'text' | 'time' | 'url' | 'week'
  listType?: "picture-circle"|"picture-card"


}

const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", 
  },
};


export default function UploadTemplate({ label, fieldName, listType, required=true}: InputTemplateInterface) {

  // This function ensures the file is properly passed to the form
  const normFile = (e: any) => {
    console.log('normFile called with:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList || (e?.file ? [e.file] : []);
  };
  type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const getBase64 = (img: FileType, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  const handleChange: UploadProps['onChange'] = (info) => {
    console.log('changing', info);

    // Set loading state
    if (info.file.status === 'uploading') {
      setLoading(true);
    }

    // Process the file and update the image URL
    if (info.file && info.file.originFileObj) {
      getBase64(info.file.originFileObj as FileType, (url) => {
        setLoading(false);
        setImageUrl(url);

        // Log the file info for debugging
        console.log('File processed:', {
          name: info.file.name,
          size: info.file.size,
          type: info.file.type,
          url: url.substring(0, 50) + '...' 
        });
      });
    }
  };

  return (
    <ConfigProvider theme={theme}>
      <div className='mb-8'>
        <div className='text-xs text-textColor font-semibold mb-2'>{label}</div>

        <Form.Item
          valuePropName="fileList"
          getValueFromEvent={normFile}
          className='my-0 py-0'
          name={fieldName}
          rules={[{ required: required, message: `${label} is required` }]}
        >
          <div className="flex justify-center">
            <Upload
              action="/upload.do"
              listType={listType}
              maxCount={1}
              beforeUpload={() => false} // Prevent auto upload
              showUploadList={false}
              onChange={handleChange}
              className="flex justify-center items-center"
              accept="image/*" // Only accept image files
            >
              {imageUrl ? (
                <div className="relative w-full max-w-xs mx-auto">
                  <img
                    src={imageUrl}
                    alt={label}
                    className="w-full h-40 object-cover rounded-md border border-gray-200"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-md">
                    <div className="text-white text-center">
                      <PlusOutlined className="text-xl" />
                      <div className="mt-1 text-sm">Change Image</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center hover:border-teal-500 transition-colors duration-200 w-64 h-40">
                  <PlusOutlined className="text-2xl text-gray-400" />
                  <div className="mt-2 text-sm text-gray-500">Click or drag to upload</div>
                  <div className="mt-1 text-xs text-gray-400">Recommended: 16:9 ratio</div>
                </div>
              )}
            </Upload>
          </div>
        </Form.Item>
      </div>
    </ConfigProvider>
  )
}
