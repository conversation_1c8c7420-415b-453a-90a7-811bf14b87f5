'use client';
import { motion } from 'framer-motion';
import { 
  LaptopOutlined, 
  BookOutlined, 
  MedicineBoxOutlined, 
  WindowsOutlined, 
  SecurityScanOutlined, 
  RiseOutlined 
} from '@ant-design/icons';
import Link from 'next/link';
import { learningPaths } from '../dummy-data/popularData';
import { ReactElement, JSXElementConstructor, ReactNode, ReactPortal, Key } from 'react';
import { UrlObject } from 'url';

interface LearningPathsInterface{
  className?:string
}

export default function LearningPaths({className}:LearningPathsInterface) {
  return (
    <div className={`bg-gray-50 py-10 sm:py-16  w-full ${className}`}>
      <div className="max-w-6xl mx-auto">
        <motion.h2 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-2xl sm:text-3xl font-bold text-teal-700 mb-6 sm:mb-10 text-left sm:text-center md:text-left md:ml-4 lg:ml-0"
          style={{fontFamily: 'var(--font-primary)', marginTop:'3em'}}
        >
          Popular Learning Paths
        </motion.h2>
        
        {/* Responsive Grid Container for Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {learningPaths.map((path: { 
            link: string | UrlObject; 
            iconBg: any; 
            icon: string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | null | undefined> | null | undefined; 
            title: string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | null | undefined> | null | undefined; 
            subtitle: string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | null | undefined> | null | undefined; 
            courses: { 
              toLocaleString: () => string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | null | undefined> | null | undefined; 
            }; 
          }, index: Key | null | undefined) => (
            <Link href={path.link} key={index} passHref>
              <motion.div
                whileHover={{
                  scale: 1.03,
                  boxShadow: '0 10px 25px -5px rgba(0, 128, 128, 0.3)'
                }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: typeof index === 'number' ? index * 0.1 : 0 }}
                className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer p-4 sm:p-6 hover:bg-teal-50 transition h-full"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0" style={{ backgroundColor: path.iconBg }}>
                    {path.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-800 truncate">{path.title}</h3>
                    <p className="text-xs text-gray-500 mt-1 line-clamp-2">{path.subtitle}</p>
                    <p className="text-xs sm:text-sm font-bold mt-2">{path.courses.toLocaleString()} courses</p>
                  </div>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}