"use client";
import Image from "next/image";
import image1 from "@/public/kindelmedia1.png";
import image2 from "@/public/kindel2.png";
import image3 from "@/public/kindel3.png";
import image4 from "@/public/kindel4.png";
import vector from "@/public/Vector.png";
import vector1 from "@/public/vector1.png";
import vector2 from "@/public/vector2.png";
import coil from "@/public/coil.png";
import kid from "@/public/kid.png";
import polygon1 from "@/public/polygon1.png";
import worldmap from "@/public/worldmap.png";
import vector3 from "@/public/vector3.png";
import col from "@/public/col.png"
import {
  BookOutlined,
  FacebookOutlined,
  CameraFilled,
  RightOutlined,
  SearchOutlined,
  SolutionOutlined,
  TrophyOutlined,
  XOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  LeftCircleFilled,
  RightCircleFilled,
} from "@ant-design/icons";
import Link from "next/link";
import { <PERSON><PERSON>, Card, ConfigProvider, ThemeConfig } from "antd";
import Footer from "@/components/general/indexPagelayout/footer";
import Navbar from "@/components/general/indexPagelayout/navbar";
import Layout from "@/components/general/indexPagelayout/layout";
import {useRef, useState, useEffect } from "react";
import { useFetch } from "@/hooks/useFetch";
import { useCallback } from 'react';

const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};



function AboutPage() {

  interface Teacher {
    id: string;
    name: string;
    email: string;
    phone: string;
    specialization: string;
    school_id: string;
    rating: number;
    availability: boolean;
    profile_image_path: string | null;
    profile_image: string | null;
    cover_image_path: string | null;
    cover_image: string | null;
    created_at: string;
    updated_at: string;
  }

  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  const response = useFetch("/unprotected/teachers");

  // Number of teachers to show at once
  const visibleTeachers = 3;

  // Fetch teacher data
  const fetchTeachers = async () => {
    try {
      if (response?.data?.data.teachers) {
        setTeachers(response.data.data.teachers);
      }
    } catch (error) {
      console.error("Error fetching teachers:", error);
    }
  };

  useEffect(() => {
    fetchTeachers();
  }, [response]);

  // Auto-scroll effect
  useEffect(() => {
    if (teachers.length <= visibleTeachers) {
      return; // Don't auto-scroll if there aren't enough teachers
    }

    if (autoScrollEnabled) {
      const id = setInterval(() => {
        setCurrentIndex(prevIndex => 
          (prevIndex + 1) % teachers.length
        );
      }, 3000); // Change every 3 seconds

      setIntervalId(id);
      return () => {
        if (intervalId) clearInterval(intervalId);
      };
    } else if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [autoScrollEnabled, teachers.length]);

  const handleLeftClick = () => {
    pauseAutoScroll();
    setCurrentIndex(prevIndex => 
      (prevIndex + 1) % teachers.length
    );
  };
  
  const handleRightClick = () => {
        pauseAutoScroll();
    setCurrentIndex(prevIndex => 
      prevIndex === 0 ? teachers.length - 1 : prevIndex - 1
    );
  };

  // Toggle auto-scroll
  const toggleAutoScroll = useCallback(() => {
    setAutoScrollEnabled(prev => !prev);
  }, []);

  // Pause auto-scroll when user interacts
  const pauseAutoScroll = useCallback(() => {
    setAutoScrollEnabled(false);
  }, []);

  // Resume auto-scroll after pause
  const resumeAutoScroll = useCallback(() => {
    setAutoScrollEnabled(true);
  }, []);

  // Get the teachers to display (current index and next two)
  const getDisplayedTeachers = () => {
    if (teachers.length <= visibleTeachers) {
      return teachers;
    }
    
    const displayed = [];
    // We'll show current-1, current, current+1 to center the current teacher
    for (let i = -1; i <= 1; i++) {
      const index = (currentIndex + i + teachers.length) % teachers.length;
      displayed.push(teachers[index]);
    }
    return displayed;
  };

  const displayedTeachers = getDisplayedTeachers();




  

  return (
    <Layout>
      <>
        <div className="m-2">
          {/* <Navbar /> */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3 w-full p-2 relative">
  {/* Banner Image - visible on all screens (z-0 on large screens) */}
  <div className="relative col-span-3 w-full h-40 z-10 sm:z-0">
    <Image
      src={image1}
      alt="Image 5"
      className="w-full h-40 object-cover rounded-lg sm:rounded-none"
    />
    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg sm:rounded-none">
      <h1 className="text-white sm:text-5xl text-4xl font-medium mb-16 mt-10">
        About Our Company
      </h1>
    </div>
  </div>

  {/* Column with Image 1 and Image 4 - hidden on small screens */}
  <div className="hidden sm:flex flex-col gap-4 -mt-14 z-20">
    <Image
      src={image1}
      alt="Image 1"
      className="w-full h-[14rem] shadow-lg rounded-lg"
    />
    <Image
      src={image4}
      alt="Image 4"
      className="w-full h-[15rem] shadow-lg rounded-lg"
    />
  </div>

  {/* Image 2 - hidden on small screens, appears above banner on large */}
  <div className="hidden sm:block sm:z-10">
    <Image
      src={image2}
      alt="Image 2"
      className="w-full md:w-[40rem] h-[30rem] object-cover shadow-lg rounded-lg -mt-14 mr-2"
    />
  </div>

  {/* Image 3 - hidden on small screens, appears above banner on large */}
  <div className="hidden sm:block sm:z-10">
    <Image
      src={image3}
      alt="Image 3"
      className="w-[60rem] sm:w-full h-[30rem] object-cover shadow-lg rounded-lg -mt-14"
    />
  </div>
</div>


          <div className="text-black mt-[2rem] ml-4 mr-4 text-[16px]">
            <span>
              Empowering minds, transforming futures our innovative learning platform bridges education
              and  technology to create seamless, personalised and impact learning experiences for all.{" "}
            </span>
            <span>
              At LearnKonnect we are commited to unlocking potential, inspiring growth
              and reshaping the future of education.{" "}
            </span>
          </div>

          <div className="flex flex-row flex-wrap justify-around mt-20 p-5">
            <div className="mt-8">
              <div className="w-20 h-1 bg-[#008080] mb-5 "></div>
              <h1 className="text-primaryColor text-3xl mb-3 font-semibold">
                Transforming Customer Engagements <br /> With Learn Connect
              </h1>
              <p className="text-blackColor">
                Problems trying to resolve the conflict between <br /> the major
                realms of classical physics <br /> Newtonian mechanics
              </p>
              <div className="flex flex-row">
                <h3 className="text-[#008080] text-l mt-3">Learn more</h3>
                <RightOutlined
                  className=" text-[#008080] ml-3 mt-3 "
                  size={20}
                />
              </div>
            </div>
            <div>
              <Image
                className=""
                width={400}
                height={300}
                src={col}
                alt={"image"}
              />
            </div>
          </div>

          <div className="bg-studentSideMenuColor mt-[4rem] p-10">
            <h2 className="text-4xl mb-4 text-center text-black">
              Why Learn Connect Works
            </h2>
            <div className="m-3 flex flex-row justify-evenly flex-wrap ">
              <Card className="w-80 shadow-md rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#008080] p-2 rounded-full">
                    <TrophyOutlined className="text-white text-xl" />
                  </div>
                </div>
                <div>
                  <h1 className="text-lg font-semibold relative text-primaryColor">
                    Expert Instruction
                    <div className="w-1/2 h-[2px] bg-primaryColor mt-1"></div>
                  </h1>
                </div>

                <p className="mt-2 text-gray-600">
                  This is a sample description for the card. Customize it as
                  needed.
                </p>
              </Card>
              <Card className="w-80 shadow-md rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#008080] p-2 rounded-full">
                    <SolutionOutlined className="text-white text-xl" />
                  </div>
                </div>
                <div>
                  <h1 className="text-lg font-semibold relative text-primaryColor">
                    Training Courses
                    <div className="w-1/2 h-[2px] bg-primaryColor mt-1"></div>
                  </h1>
                </div>

                <p className="mt-2 text-gray-600">
                  This is a sample description for the card. Customize it as
                  needed.
                </p>
              </Card>
              <Card className="w-80 shadow-md rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#008080] p-2 rounded-full">
                    <BookOutlined className="text-white text-xl" />
                  </div>
                </div>
                <div>
                  <h1 className="text-lg font-semibold relative text-primaryColor">
                    Certificate Verification
                    <div className="w-1/2 h-[2px] bg-primaryColor mt-1"></div>
                  </h1>
                </div>

                <p className="mt-2 text-gray-600">
                  This is a sample description for the card. Customize it as
                  needed.
                </p>
              </Card>
            </div>

            <div className="flex flex-col items-center justify-center text-center mt-40">
              <h2 className="text-1xl mb-4 mt-4">
                Preparing Students to Achieve Success
              </h2>
              <Image
                src={vector}
                alt="Success Image"
                className="w-64 h-auto  bg-studentSideMenuColor"
              />
            </div>

            <div className="flex flex-row flex-wrap justify-around mt-20 p-5">
              {/* Text Section */}
              <div className="mt-8">
                <h1 className="text-blackColor text-3xl mb-3 font-semibold">
                  Developing Confident and <br /> Successful Learners
                </h1>
                <div className="w-40 h-1 bg-[#008080] mb-5"></div>
                <p className="text-primaryColor">
                  Problems trying to resolve the conflict between <br /> the
                  major realms of classical physics <br /> Newtonian mechanics
                </p>
                <div className="">
                  <button className="relative px-10 py-2 text-white bg-teal-700 rounded-lg shadow-lg mt-4 overflow-hidden">
                    View More
                    <span className="absolute bottom-0 right-0 w-7 h-6 bg-white rounded-tl-full"></span>
                    <span className="absolute bottom-0 right-0 w-6 h-5 bg-gray-900 rounded-tl-full "></span>
                  </button>
                </div>
              </div>

              {/* Image Section */}
              <div className="relative w-[310px] h-[280px]">
                {/* Main Image */}
                <Image
                  className="w-full h-full "
                  width={300}
                  height={200}
                  src={vector1}
                  alt="Main Image"
                />

                <Image
                  className="absolute bottom-0 right-0 w-60 h-50 object-cover  mr-6"
                  width={180}
                  height={160}
                  src={vector2}
                  alt="Image"
                />

                <Image
                  className="absolute bottom-0 right-0 left-44 w-40 h-8 "
                  width={180}
                  height={160}
                  src={coil}
                  alt="Image"
                />
              </div>
            </div>

            {/*second part */}

            <div className="flex flex-row flex-wrap justify-around mt-20 p-5">
              {/* Image Section */}
              <div className="relative w-[310px] h-[280px]">
                {/* Main Image */}
                <Image
                  className="w-full h-full "
                  width={300}
                  height={200}
                  src={vector1}
                  alt="Main Image"
                />

                <Image
                  className="absolute bottom-0 right-0 w-60 h-50 object-cover  mr-6"
                  width={180}
                  height={160}
                  src={kid}
                  alt="Image"
                />

                <Image
                  className="absolute bottom-0 right-0 left-44 w-40 h-8 "
                  width={180}
                  height={160}
                  src={coil}
                  alt="Image"
                />
              </div>

              {/* Text Section */}
              <div className="mt-8">
                <h1 className="text-blackColor text-3xl mb-3 font-semibold">
                  Enjoy Learning With a Unique <br /> Classroom Experience
                </h1>
                <div className="w-40 h-1 bg-[#008080] mb-5"></div>
                <p className="text-primaryColor">
                  Problems trying to resolve the conflict between <br /> the
                  major realms of classical physics <br /> Newtonian mechanics
                </p>
                <div className="flex flex-row">
                  <button className="relative px-10 py-2 text-white bg-teal-700 rounded-lg shadow-lg mt-4 overflow-hidden">
                    View More
                    <span className="absolute bottom-0 right-0 w-7 h-6 bg-white rounded-tl-full"></span>
                    <span className="absolute bottom-0 right-0 w-6 h-5 bg-gray-900 rounded-tl-full "></span>
                  </button>
                </div>
              </div>
            </div>

            {/*third part */}
            <div className="flex flex-row flex-wrap justify-around mt-20 p-5">
              {/* Text Section */}
              <div className="mt-8">
                <h1 className="text-blackColor text-3xl mb-3 font-semibold">
                  Passionate Teachers That <br /> Make a Difference
                </h1>
                <div className="w-40 h-1 bg-[#008080] mb-5"></div>
                <p className="text-primaryColor">
                  Problems trying to resolve the conflict between <br /> the
                  major realms of classical physics <br /> Newtonian mechanics
                </p>
                <div className="flex flex-row">
                  <button className="relative px-10 py-2 text-white bg-teal-700 rounded-lg shadow-lg mt-4 overflow-hidden">
                    View More
                    <span className="absolute bottom-0 right-0 w-7 h-6 bg-white rounded-tl-full"></span>
                    <span className="absolute bottom-0 right-0 w-6 h-5 bg-gray-900 rounded-tl-full "></span>
                  </button>
                </div>
              </div>

              {/* Image Section */}
              <div className="relative w-[310px] h-[280px]">
                {/* Main Image */}
                <Image
                  className="w-full h-full "
                  width={300}
                  height={200}
                  src={vector1}
                  alt="Main Image"
                />

                <Image
                  className="absolute bottom-0 right-0 w-60 h-50 object-cover  mr-6"
                  width={180}
                  height={160}
                  src={polygon1}
                  alt="Image"
                />

                <Image
                  className="absolute bottom-0 right-0 left-44 w-40 h-8 "
                  width={180}
                  height={160}
                  src={coil}
                  alt="Image"
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col items-center justify-center text-center mt-10">
            <h2 className="text-1xl mb-4 mt-4">Teachers</h2>
            <Image
              src={vector}
              alt="Success Image"
              className="w-64 h-auto  bg-studentSideMenuColor"
              width={256}
              height={256}
            />
          </div>

          {/**teachers */}
          <div className="flex flex-wrap items-center justify-evenly mt-5 mx-2">
          <ConfigProvider theme={theme}>
              {teachers.length > visibleTeachers && (
                <LeftCircleFilled 
                  className="text-3xl text-primaryColor flex-shrink-0  cursor-pointer" 
                  onClick={handleLeftClick} 
                />
              )}
            </ConfigProvider>

            {/* Teacher Profiles (Wrapped on Small Screens) */}
            {/* {displayedTeachers.length > 0 && ( */}
            <div 
  // className="flex flex-wrap items-center justify-evenly mt-10 mx-10"
  onMouseEnter={pauseAutoScroll}
  onMouseLeave={resumeAutoScroll}
>
            {/* <div className="flex flex-wrap justify-center gap-x-6 gap-y-6"> */}
              {/* Teacher 1 */}
               {/* <div className="relative w-48 mx-auto">
                <div className="ml-4 mr-4">
                  <Image
                    src={teacher1}
                    height={200}
                    width={350}
                    alt="Person pointing at a world map"
                    className="w-full h-auto"
                  />
                </div>
                <div className="relative mt-[-30px]">
                  <Image
                    src={vector3}
                    height={100}
                    width={700}
                    alt="Vector Background"
                    className="sm:w-[300px] h-auto"
                  />
                  <div className="absolute inset-0 flex items-center justify-center text-black text-sm font-bold">
                  {teachers[currentIndex]?.name || "Teacher Name"}
                  </div>
                </div>
              </div>  */}


              {/* Teacher 2 */}
              <div className="flex flex-wrap justify-center items-end gap-x-6 gap-y-2 transition-all duration-500 ease-in-out mb-4">
            {displayedTeachers.map((teacher: Teacher, index: number) => {
              const hasProfileImage = teacher.profile_image !== null;
              const imageSrc = hasProfileImage 
                ? `data:image/png;base64,${teacher.profile_image}`
                : '/clock.png';

              return (
                <div 
                  key={index} 
                  className={`relative w-84 mx-auto mt-20 mx-16 sm:pt-2 transition-all duration-300 ${
                    index === 1 ? "scale-110 mt-20" : "scale-90 opacity-90 mb-20 "
                  }`}
                >
                  <div className="overflow-hidden sm:ml-10 ml-1">
                    <img 
                      src={imageSrc}
                      alt={`${teacher.name || 'Teacher'}`}
                      width={150}
                      height={150}
                      className={`w-[200px] h-[250px] object-cover ${
                        index === 1 ? "shadow-lg" : "shadow-md"
                      }`}
                      onError={(e) => {
                        e.currentTarget.src = '/default_teacher.png';
                      }}
                    />
                  </div>
                  <div className="relative mt-[-56px] mr-[10px]">
                    <Image
                      src={vector3}
                      height={100}
                      width={100}
                      alt="Vector Background"
                      className="sm:w-[280px]  w-[300px] h-auto"
                    />
                    <div className="absolute inset-0 flex items-center justify-center text-black text-lg font-bold">
                      {teacher?.name || "Teacher Name"}  
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
  

              {/* Teacher 3 */}
              {/* <div className="relative w-48 mx-auto">
                <div className="ml-4 mr-4">
                  <Image
                    src={teacher3}
                    height={100}
                    width={500}
                    alt="Person pointing at a world map"
                    className="w-full h-auto"
                  />
                </div>
                <div className="relative mt-[-30px]">
                  <Image
                    src={vector3}
                    alt="Vector Background"
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 flex items-center justify-center text-black text-sm font-bold">
                  {teachers[currentIndex]?.name || "Teacher Name"}
                  </div>
                </div>
              </div> */}
              
            {/* </div> */}
            </div>
            
          {/* )} */}
          <ConfigProvider theme={theme}>
              {teachers.length > visibleTeachers && (
                <RightCircleFilled 
                  className="text-3xl text-primaryColor flex-shrink-0  cursor-pointer" 
                  onClick={handleRightClick} 
                />
              )}
            </ConfigProvider>
          </div>

          {/*trying out*/}
          <div className="flex items-center space-x-4">


        {/* Display teacher details */}
        {/* {teachers.length > 0 && (
          <div className="text-center">
            <Image src={teachers[currentIndex]?.profile_image_path || "/default_teacher.png"} alt="Teacher" width={150} height={150} />
            <h2 className="text-xl font-medium mt-2">{teachers[currentIndex]?.name || "Teacher Name"}</h2>
            <p className="text-gray-600">{teachers[currentIndex]?.subject || "Subject"}</p>
          </div>
        )} */}



      
      </div>
        </div>
        <div className="mt-20 w-full">
          <Image src={worldmap} alt={""} />
        </div>
        {/* <Footer /> */}
      </>
    </Layout>
  );
}

export default AboutPage;
