"use client";
import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';
import { FileTextOutlined } from '@ant-design/icons';
import { ConfigProvider, Tabs, TabsProps } from 'antd';
import { useCartStore } from '@/store/cartStore';
import { useUserStore } from '@/store/userStore';
import { CartLogics } from '../cart/_logics/cart_logics';
import OngoingPage from './quizzes-page';
import CompletedPage from './tests-page';
import AssignmentsPage from './assignments-page';
import ExamsPage from './exams-page';
import QuizzesPage from './quizzes-page';
import TestsPage from './tests-page';

const MotionDiv = dynamic(() => import('framer-motion').then(mod => mod.motion.div), { ssr: false });

interface UINotification {
    timestamp: any;
    key: string;
    sender: string;
    type: string;
    content: string;
    date: string;
    time: string;
    status: 'viewed' | 'unviewed';
}

export default function LearningPage() {
    const { checkoutCart, removeFromCart } = CartLogics()
    const { cart } = useCartStore()
    const { user } = useUserStore()

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: 'Exams',
            children: <ExamsPage />,
        },
        {
            key: '2',
            label: 'Quizzes',
            children: <QuizzesPage />,
        },
        {
            key: '3',
            label: 'Tests',
            children: <TestsPage />,
        },
        {
            key: '4',
            label: 'Assignments',
            children: <AssignmentsPage />,
        },
    ];

    useEffect(() => {
        //   getCart();
    }, []);




    return (
        <div className="w-full p-4 h-full overflow-y-scroll">
            <MotionDiv
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="relative w-full mb-8"
            >
                <img src="/notification.png" alt="Notifications Header" className="w-full rounded-lg" />
                <div className="absolute inset-0 flex items-center justify-center">
                    <h1 className="text-2xl md:text-3xl font-bold text-white">Assessments</h1>
                </div>
                <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 bg-white p-3 rounded-full shadow-lg">
                <FileTextOutlined  className="text-2xl text-gray-600" />
                </div>
            </MotionDiv>

            <ConfigProvider

                theme={{
                    components: {
                        Tabs: {
                            /* here is your component tokens */
                            inkBarColor: "#008080",
                            itemActiveColor: "#008080",
                            itemColor: "#6e6d6b",
                            itemHoverColor: "#000000",
                            itemSelectedColor: "#008080"
                        },
                    },
                }}
            >

                <Tabs defaultActiveKey="1" items={items} onChange={(onChange) => { }} />

            </ConfigProvider>
        </div>
    );
}