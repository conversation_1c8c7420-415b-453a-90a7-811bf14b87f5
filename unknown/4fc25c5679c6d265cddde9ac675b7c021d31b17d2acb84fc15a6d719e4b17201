'use client';

import React from 'react';
import { Modal, Button, Descriptions, Tag, Divider } from 'antd';
import { LiveSessionData } from '../dummydata/livesessiondata';
import { format } from 'date-fns';
import { PlayCircleOutlined, ClockCircleOutlined, UserOutlined, BookOutlined, CalendarOutlined } from '@ant-design/icons';

interface ViewSessionProps {
  session: LiveSessionData;
  isVisible: boolean;
  onClose: () => void;
  onStartSession: (session: LiveSessionData) => void;
  parseDate: (dateString: string) => Date;
}

const ViewSession: React.FC<ViewSessionProps> = ({
  session,
  isVisible,
  onClose,
  onStartSession,
  parseDate
}) => {
  const sessionDate = parseDate(session.scheduledDate);
  const isPastSession = new Date() > sessionDate;
  const canStart = session.status === 'scheduled' && !isPastSession;

  // Map status to color
  const statusMap: { [key: string]: { color: string, text: string } } = {
    'scheduled': { color: 'blue', text: 'Scheduled' },
    'in-progress': { color: 'green', text: 'In Progress' },
    'completed': { color: 'gray', text: 'Completed' },
    'cancelled': { color: 'red', text: 'Cancelled' }
  };

  // Get appropriate enrollment display text based on session status
  const getEnrollmentDisplay = () => {
    if (session.status === 'completed') {
      return `${session.enrolledStudents} of ${session.maxCapacity} students attended`;
    } else if (session.status === 'cancelled') {
      return `${session.enrolledStudents} of ${session.maxCapacity} students enrolled`;
    } else if (session.status === 'in-progress') {
      return `${session.enrolledStudents} students expected`;
    } else {
      return `${session.enrolledStudents} students enrolled`;
    }
  };

  return (
    <Modal
      title="Session Details"
      open={isVisible}
      onCancel={onClose}
      footer={[
        <Button
          key="close"
          onClick={onClose}
          className="bg-teal-600 hover:bg-teal-700 text-white border-none"
          style={{
            backgroundColor: '#0d9488', 
            // borderColor: '#0d9488', 
            color: 'white', 
          }}
        >
          Close
        </Button>,
        canStart && (
          <Button
            key="start"
            icon={<PlayCircleOutlined />}
            onClick={() => onStartSession(session)}
            className="bg-teal-600 hover:bg-teal-700 text-white border-none"
            style={{
              backgroundColor: '#0d9488', 
              // borderColor: '#0d9488', 
              color: 'white', 
            }}
          >
            Start Session
          </Button>
        ),
      ]}
      width={700}
    >
      <div className="p-2">
        <h2 className="text-xl font-light mb-2">{session.title}</h2>
        
        <div className="flex items-center mb-4">
          <Tag color={statusMap[session.status].color} className="mr-2">
            {statusMap[session.status].text}
          </Tag>
          <span className="text-sm text-gray-500">
            Session ID: {session.id}
          </span>
        </div>

        <p className="text-sm font-light mb-4">{session.description}</p>

        <Divider orientation="left" className="text-sm font-light">Session Information</Divider>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <BookOutlined className="text-gray-500 mr-2" />
            <div>
              <div className="text-sm font-normal">Course</div>
              <div className="text-sm font-light">{session.courseCode} - {session.programName}</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <UserOutlined className="text-gray-500 mr-2" />
            <div>
              <div className="text-sm font-normal">Instructor</div>
              <div className="text-sm font-light">{session.instructorName}</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <CalendarOutlined className="text-gray-500 mr-2" />
            <div>
              <div className="text-sm font-normal">Date</div>
              <div className="text-sm font-light">{format(sessionDate, 'MMMM dd, yyyy')}</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <ClockCircleOutlined className="text-gray-500 mr-2" />
            <div>
              <div className="text-sm font-normal">Time</div>
              <div className="text-sm font-light">{session.startTime} ({session.duration})</div>
            </div>
          </div>
        </div>

        <Divider orientation="left" className="text-sm font-light">Enrollment</Divider>
        
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-normal">Students</span>
            <span className="text-sm font-light">{getEnrollmentDisplay()}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="h-2 rounded-full bg-teal-700"
              // style={{ width: `${(session.enrolledStudents / session.maxCapacity) * 100}%` }}
            />
          </div>
        </div>

        {session.materials && session.materials.length > 0 && (
          <>
            <Divider orientation="left" className="text-sm font-light">Materials</Divider>
            <div className="flex flex-wrap gap-2">
              {session.materials.map((material, index) => (
                <Tag key={index} color="blue" className="mb-1">{material}</Tag>
              ))}
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default ViewSession;