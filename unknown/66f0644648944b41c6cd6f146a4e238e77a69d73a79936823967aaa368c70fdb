'use client';
import { motion } from 'framer-motion';
import { 
  BookOutlined, 
  SafetyCertificateOutlined, 
  UserOutlined,
  SmileOutlined,
  EyeOutlined,
  GlobalOutlined,
  TeamOutlined
} from '@ant-design/icons';
import LearningPaths from './popular';
import CourseCard from './coursesCards';
import Testimonial from './testimonial';
import Mentors from './mentors';
// import Pricing from './NavBarMenus/pricing';
import { useEffect, useState, useRef } from 'react';

// Counter animation component
interface AnimatedCounterProps {
  value: number;
  label: string;
  icon: React.ReactNode;
}

const AnimatedCounter = ({ value, label, icon }: AnimatedCounterProps) => {
  const [count, setCount] = useState(0);
  const nodeRef = useRef(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          let startValue = 0;
          const duration = 2000;
          const increment = Math.ceil(value / (duration / 16));
          
          const timer = setInterval(() => {
            startValue += increment;
            if (startValue >= value) {
              setCount(value);
              clearInterval(timer);
            } else {
              setCount(startValue);
            }
          }, 16);
          
          return () => clearInterval(timer);
        }
      },
      { threshold: 0.2 }
    );
    
    if (nodeRef.current) {
      observer.observe(nodeRef.current);
    }
    
    return () => {
      if (nodeRef.current) {
        observer.unobserve(nodeRef.current);
      }
    };
  }, [value]);
  
  return (
    <motion.div 
      ref={nodeRef}
      initial={{ y: 50, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center"
    >
      <div className="text-text-teal-700 text-4xl mb-2">{icon}</div>
      <h3 className="text-4xl font-bold text-teal-700">{count}{value === 100 ? '+' : value >= 1000 ? 'K' : ''}</h3>
      <p className="text-gray-600 text-sm">{label}</p>
    </motion.div>
  );
};

export default function LandingPage() {
  return (
    <div className="flex flex-col items-center justify-between w-full overflow-x-hidden">
      {/* Main Content Section */}
      <div className="flex flex-col md:flex-row items-center justify-between p-4 sm:p-6 md:p-10 lg:p-16 xl:p-20 max-w-7xl mx-auto w-full mt-20 sm:mt-16 md:mt-24 lg:mt-16 relative mb-24 md:mb-48">
        
        {/* Text Section */}
        <motion.div 
          initial={{ x: -100, opacity: 0 }} 
          animate={{ x: 0, opacity: 1 }} 
          transition={{ duration: 1, delay: 0.7 }} 
          className="w-full md:w-1/2 text-center md:text-left px-2 sm:px-4 md:px-6 relative z-10"
        >
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-extrabold text-teal-700 mb-4" 
              style={{ fontFamily: 'var(--font-primary)', marginTop: '-2em' }}>
            <span className="font-black">25K+</span> STUDENTS TRUST US
          </h1>
          <p className="text-sm sm:text-sm md:text-base lg:text-sm text-gray-600 mb-6" 
             style={{ fontFamily: 'var(--font-primary)' }}>
            For every student, every classroom. Real results, and powered by accountability<br className="hidden md:block" />
            and secure resource tracking.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 justify-center md:justify-start">
            <button className="bg-teal-700 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md shadow-lg hover:bg-text-teal-700 transition" 
                    style={{ fontFamily: 'var(--font-primary)' }}>
              Get a Demo
            </button>
            
            <button className="border border-teal-700 text-teal-700 px-4 sm:px-6 py-2 sm:py-3 rounded-md shadow-sm hover:bg-teal-700 hover:text-white transition" 
                    style={{ fontFamily: 'var(--font-primary)' }}>
              Learn More
            </button>
          </div>
        </motion.div>

        {/* Image Section - Hidden on mobile */}
        <motion.div 
          initial={{ y: 50, opacity: 0 }} 
          animate={{ y: 0, opacity: 1 }} 
          transition={{ type: 'spring', stiffness: 50, delay: 0.5 }}
          className="w-full md:w-1/2 hidden md:flex flex-col items-center relative"
        >
          <img src="/none.png" alt="Student" 
               className="w-full max-w-[200px] sm:max-w-[300px] md:max-w-[200px] lg:max-w-[500px] xl:max-w-[600px] h-auto" />
        </motion.div>

        {/* Cards Section - Revised positioning */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="w-full md:absolute mt-8 md:mt-0 md:bottom-0 md:left-0 md:right-0 md:transform md:translate-y-1/2 px-4 sm:px-6"
        >
          <div className="flex flex-col md:flex-row justify-center gap-6 max-w-7xl mx-auto">
            {/* Expert Instruction Card */}
            <motion.div
              whileHover={{ y: -10, scale: 1.03 }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
              className="w-full md:max-w-[250px] bg-white p-6 rounded-lg shadow-xl flex flex-col items-start justify-center min-h-[12rem] mx-auto"
            >
              <div className="bg-teal-700 text-white p-2 rounded-full mb-3">
                <UserOutlined className="text-xl" />
              </div>
              <h3 className="text-lg font-bold text-teal-700 mb-2 relative">
                Expert Instruction
                <div className="absolute bottom-[-8px] left-0 w-8 h-1 bg-teal-700 rounded-full"></div>
              </h3>
              <p className="text-xs text-gray-600 text-left">
                Gain invaluable knowledge from top-tier professionals with real-world experience.
              </p>
            </motion.div>

            {/* Training Courses Card */}
            <motion.div
              whileHover={{ y: -10, scale: 1.03 }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
              className="w-full md:max-w-[250px] bg-white p-6 rounded-lg shadow-xl flex flex-col items-start justify-center min-h-[12rem] mx-auto"
            >
              <div className="bg-teal-700 text-white p-2 rounded-full mb-3">
                <BookOutlined className="text-xl" />
              </div>
              <h3 className="text-lg font-bold text-teal-700 mb-2 relative">
                Training Courses
                <div className="absolute bottom-[-8px] left-0 w-8 h-1 bg-teal-700 rounded-full"></div>
              </h3>
              <p className="text-xs text-gray-600 text-left">
                Dive into a wide range of expertly designed courses, offering interactive learning.
              </p>
            </motion.div>

            {/* Certificate Verification Card */}
            <motion.div
              whileHover={{ y: -10, scale: 1.03 }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
              className="w-full md:max-w-[250px] bg-white p-6 rounded-lg shadow-xl flex flex-col items-start justify-center min-h-[12rem] mx-auto"
            >
              <div className="bg-teal-700 text-white p-2 rounded-full mb-3">
                <SafetyCertificateOutlined className="text-xl" />
              </div>
              <h3 className="text-lg font-bold text-teal-700 mb-2 relative">
                Certificate Verification
                <div className="absolute bottom-[-8px] left-0 w-8 h-1 bg-teal-700 rounded-full"></div>
              </h3>
              <p className="text-xs text-gray-600 text-left">
                Ensure authenticity and trust with our credential validation process.
              </p>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Rest of the components */}
      <div className="w-full mt-16 md:mt-0" style={{marginTop: 'calc(2rem + 5vw)'}}>
        <LearningPaths />
      </div>

      {/* Stats Section */}
      <div className="w-full bg-gray-50 py-16 mt-16">
        <div className="max-w-7xl mx-auto">
          {/* Stats Counters */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 px-4 mb-20">
            <AnimatedCounter value={500} label="Happy clients" icon={<SmileOutlined />} />
            <AnimatedCounter value={500} label="Monthly Visitors" icon={<EyeOutlined />} />
            <AnimatedCounter value={500} label="Countries Worldwide" icon={<GlobalOutlined />} />
            <AnimatedCounter value={500} label="Top Partners" icon={<TeamOutlined />} />
          </div>

          <section className="py-10 sm:py-16 px-4 sm:px-6 max-w-6xl mx-auto">
      {/* Every Client Matters Section */}
      <div className="flex flex-col md:flex-row items-center gap-8">
        {/* Text Content */}
        <div className="w-full md:w-1/2">
          <div className="w-16 h-1 bg-teal-700 mb-4 sm:mb-6"></div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-teal-700 mb-3 sm:mb-4">Every Client Matters</h2>
          <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
            Problems trying to resolve the conflict between the two major realms of Classical physics: Newtonian mechanics
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            className="flex items-center text-teal-700 font-medium"

          >
            Learn More 
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.button>
        </div>
        
        {/* Image with Animation */}
        <motion.div 
          className="w-full md:w-1/2 mt-6 md:mt-0"
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="relative mx-auto max-w-sm md:max-w-none">
            {/* Background elements - adjusted sizes for responsiveness */}
            <div className="absolute -z-10 top-4 left-4 w-2/3 h-2/3 bg-pink-100 rounded-lg"></div>
            <div className="absolute -z-10 -top-3 -left-3 w-12 h-12 sm:w-16 sm:h-16 bg-pink-200 rounded-full"></div>
            <div className="absolute -z-10 bottom-8 right-8 w-6 h-6 sm:w-8 sm:h-8 bg-pink-200 rounded-full"></div>
            
            <img 
              src="/thumb-concept-7.png" 
              alt="Client Support" 
              className="relative z-10 rounded-lg shadow-lg w-full h-auto"
            />
          </div>
        </motion.div>
      </div>
    </section>
        </div>
      </div>
      <br/> 
      {/* Testimonial */}
      {/* Learning Paths Section - Added below with proper spacing */}
      <div className="w-full" style={{marginTop: 'calc(2rem + 5vw)'}}>
        <CourseCard />
      </div>

      {/* Testimonial COMPONENT INCPUDED */}
      <div className="w-full" style={{marginTop: '15em'}}>
        <Testimonial />
      </div>


    {/* Mentos COMPONENT INCPUDED */}
    <div className="w-full" style={{marginTop: '15em'}}>
        <Mentors />
      </div>

         {/* Mentos COMPONENT INCPUDED */}
    {/* <div className="w-full" style={{marginTop: '15em'}}>
        <Pricing />
      </div> */}

    </div>
  );
}