// components/ui/edit-modal-template.tsx
import React, { useEffect, useState } from 'react';
import { Modal, Form, Button, Upload, message, Spin } from 'antd';
import InputTemplate from './input-template';
import ButtonTemplate from './button-template';
import TextAreaTemplate from './TextArea-template';
import { Course, CourseResponseData } from '@/types';
import { CourseType } from '@/logics/course';
import SelectTemplate from './select-template';
import { useApi } from '@/hooks/useRequest';

interface EditModalTemplateProps {
  isVisible: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  item: Course | CourseType | null;
  title?: string;
  loading?: boolean;
}

const EditModalTemplate: React.FC<EditModalTemplateProps> = ({
  isVisible,
  onCancel,
  onSubmit,
  item,
  title = 'Edit Course',
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [descriptionChars, setDescriptionChars] = useState<number>(500);
  const { request } = useApi();
  const [isLoadingImage, setIsLoadingImage] = useState<boolean>(false);

  // Reset state when modal is closed
  useEffect(() => {
    if (!isVisible) {
      setImageUrl(null);
      setIsLoadingImage(false);
    }
  }, [isVisible]);

  // Handle description character count
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const remaining = 500 - value.length;
    setDescriptionChars(remaining >= 0 ? remaining : 0);
  };

  // Fetch course image directly from the API
  const fetchCourseImage = async (courseId: string) => {
    if (!courseId) return;
    
    try {
      setIsLoadingImage(true);
      console.log(`Fetching image for course ${courseId}...`);
      
      const response = await request("GET", `/course/${courseId}`, null, "");
      console.log('Course API response status:', response?.status);
      
      if (response?.status === 200 && response.data?.data) {
        // Check for base64 image in response
        const courseData = response.data.data;
        
        if (courseData.cover_image) {
          console.log('Found cover_image in API response');
          const imageData = courseData.cover_image;
          
          // Check if this is already a data URL
          if (imageData.startsWith('data:image/')) {
            console.log('Image is already a data URL');
            setImageUrl(imageData);
          } 
          // Check if it's a base64 PNG
          else if (imageData.startsWith('iVBOR')) {
            console.log('Image is a base64 PNG');
            setImageUrl(`data:image/png;base64,${imageData}`);
          } 
          // Handle other base64 images (likely JPEG)
          else if (imageData.match(/^[A-Za-z0-9+/=]+$/) && imageData.length > 100) {
            console.log('Image is a generic base64 string');
            setImageUrl(`data:image/jpeg;base64,${imageData}`);
          } 
          // Handle URLs
          else if (imageData.startsWith('http')) {
            console.log('Image is a URL');
            setImageUrl(imageData);
          }
          else {
            console.log('Unknown image format:', imageData.substring(0, 20) + '...');
            setImageUrl(null);
          }
        } else {
          console.log('No cover_image found in response');
          setImageUrl(null);
        }
      } else {
        console.log('API request failed or returned empty data');
        setImageUrl(null);
      }
    } catch (error) {
      console.error('Error fetching course image:', error);
      setImageUrl(null);
    } finally {
      setIsLoadingImage(false);
    }
  };

  const handleSubmit = (values: any) => {
    console.log('EditModalTemplate - Form values before processing:', values);

    // Create a new object with the correct field names
    const processedValues: any = {};

    // Basic fields
    if (values.name) processedValues.name = values.name;
    if (values.code) processedValues.code = values.code;
    if (values.credits) processedValues.credits = Number(values.credits);
    if (values.base_price) processedValues.base_price = Number(values.base_price);
    if (values.duration) {
      // Use duration as cohort_duration_weeks for consistency
      processedValues.cohort_duration_weeks = Number(values.duration);
      processedValues.duration = Number(values.duration);
    }
    if (values.level) processedValues.level = values.level;
    if (values.description) processedValues.description = values.description;

    // Process topics from comma-separated string to array
    if (values.topics) {
      processedValues.topics = values.topics.split(',').map((topic: string) => topic.trim()).filter(Boolean);
    }

    // Convert string boolean values to actual booleans
    // Note: Using has_cohorts (plural) as that's the field name in the API
    processedValues.has_cohorts = values.has_cohort === 'true';
    processedValues.has_curriculum = values.has_curriculum === 'true';
    processedValues.is_self_paced = values.is_self_paced === 'true';

    // Convert numeric fields
    if (values.cohort_duration_weeks) {
      processedValues.cohort_duration_weeks = Number(values.cohort_duration_weeks);
    }
    if (values.max_students_per_cohort) {
      processedValues.max_students_per_cohort = Number(values.max_students_per_cohort);
    }
    processedValues.auto_create_cohorts = values.auto_create_cohorts === 'true';
    if (values.days_between_cohorts) {
      processedValues.days_between_cohorts = Number(values.days_between_cohorts);
    }

    // Store the image file separately, don't include it in the main update payload
    const imageFile = values.courseImage instanceof File ? values.courseImage : null;
    
    // Show debug info about the image file
    if (imageFile) {
      console.log('Image file info:', {
        name: imageFile.name,
        type: imageFile.type,
        size: `${(imageFile.size / 1024).toFixed(2)} KB`
      });
    } else {
      console.log('No new image file to upload');
    }
    
    // Process other fields but don't include image in the main update payload
    console.log('Processed values for course update:', processedValues);

    // Submit the form data
    console.log('EditModalTemplate - Sending processed values:', processedValues);
    
    // Just call the original onSubmit directly without image handling
    onSubmit(processedValues);
  };

  useEffect(() => {
    if (item && isVisible) {
      console.log('EditModalTemplate - Setting form values for course:', item.name, 'ID:', item.id);
      
      // Reset image when switching courses
      setImageUrl(null);
      
      // Convert topics to string if it exists (handle both array and string cases)
      let topicsString = '';
      if (item.topics) {
        if (Array.isArray(item.topics)) {
          topicsString = item.topics.join(', ');
        } else {
          topicsString = String(item.topics);
        }
      }

      // Set all form values from the item data
      const formValues = {
        name: item.name,
        code: item.code,
        credits: item.credits,
        base_price: item.base_price,
        has_cohort: item.has_cohorts === true ? 'true' : 'false',
        has_curriculum: item.has_curriculum === true ? 'true' : 'false',
        is_self_paced: item.is_self_paced === true ? 'true' : 'false',
        // Use duration for the form field, but it maps to cohort_duration_weeks in the API
        duration: item.cohort_duration_weeks || item.duration,
        description: item.description,
        topics: topicsString,
        level: item.level,
        cohort_duration_weeks: item.cohort_duration_weeks,
        max_students_per_cohort: item.max_students_per_cohort,
        auto_create_cohorts: item.auto_create_cohorts === true ? 'true' : 'false',
        days_between_cohorts: item.days_between_cohorts,
        cover_image_path: item.cover_image_path,
      };

      form.setFieldsValue(formValues);

      // Update description character count
      if (item.description && typeof item.description === 'string') {
        setDescriptionChars(Math.max(0, 500 - item.description.length));
      } else {
        setDescriptionChars(500);
      }

      // Fetch course image from API if we have an ID
      if (item.id) {
        fetchCourseImage(item.id);
      }
    }
  }, [form, item, isVisible]);

  return (
    <>
      {/* Styles moved to globals.css */}
      
      <Modal
        title={<span className="text-xl font-semibold">{title}</span>}
        open={isVisible}
        onCancel={onCancel}
        footer={null}
        width={800}
        bodyStyle={{
          padding: '24px',
          maxHeight: '80vh',
          overflowY: 'auto'
        }
        }
      >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        {/* Course Image Upload Centered */}
        <div className="mb-8 flex justify-center items-center w-full border-b pb-6">
          <Form.Item
            name="courseImage"
            label={<span className="text-center block w-full font-medium text-gray-700">Course Image</span>}
            rules={[{ required: !imageUrl, message: <div className="text-center">Please upload a course image</div> }]}
            className="flex flex-col items-center w-full max-w-xs"
          >
            <div className="flex flex-col items-center w-full">
              <Upload
                listType="picture-card"
                showUploadList={false}
                beforeUpload={(file) => {
                  // Validate file type
                  const isImage = file.type.startsWith('image/');
                  if (!isImage) {
                    message.error('You can only upload image files!');
                    return false;
                  }

                  // Validate file size (5MB max)
                  const isLt5M = file.size / 1024 / 1024 < 5;
                  if (!isLt5M) {
                    message.error('Image must be smaller than 5MB!');
                    return false;
                  }

                  // Create a data URL for preview
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    if (e.target && typeof e.target.result === 'string') {
                      // Set the image URL for preview
                      setImageUrl(e.target.result);

                      // Store the actual file object in form values
                      form.setFieldsValue({
                        courseImage: file
                      });
                    }
                  };
                  reader.readAsDataURL(file);
                  return false;
                }}
                className="w-full max-w-xs aspect-video"
                accept="image/*"
              >
                {imageUrl ? (
                  <div className="relative w-full h-full overflow-hidden rounded-md">
                    <img 
                      src={imageUrl} 
                      alt="Course" 
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error('Image failed to load:', imageUrl?.substring(0, 50) + '...');
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-teal-600 bg-opacity-70 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                      <span className="text-white font-medium">Change Image</span>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center p-4 w-full h-full hover:bg-teal-50 hover:border-teal-500 transition-colors duration-200 border-2 border-dashed rounded-md">
                    {isLoadingImage ? (
                      <div className="flex flex-col items-center justify-center h-full">
                        <Spin size="large" className="mb-2" />
                        <span className="text-sm text-gray-500">Loading image...</span>
                      </div>
                    ) : (
                      <>
                        <div className="text-3xl mb-2 text-teal-500">+</div>
                        <div className="text-sm text-black hover:text-teal-600">Upload Image</div>
                      </>
                    )}
                  </div>
                )}
              </Upload>
            </div>
          </Form.Item>
        </div>

        {/* using flex to align fields side by side */}
        <div className="space-y-6">
          {/* Basic Course Information Section */}
          <div className="mb-2">
            <h3 className="text-gray-700 font-medium mb-4">Basic Course Information</h3>
          </div>

          {/* Row 1: Name and Code */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="name"
              label={<span className="font-medium text-gray-700">Course Name</span>}
              rules={[{ required: true, message: 'Please enter course name' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'name'} className="font-regular-text text-black course-name-placeholder" />
            </Form.Item>

            <Form.Item
              name="code"
              label={<span className="font-medium text-gray-700">Course Code</span>}
              rules={[{ required: true, message: 'Please enter course code' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'code'} className="font-regular-text text-black" />
            </Form.Item>
          </div>

          {/* Row 2: Credits and Base Price */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="credits"
              label={<span className="font-medium text-gray-700">Credits</span>}
              rules={[{ required: true, message: 'Please enter credits' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'credits'} className="font-regular-text text-black" />
            </Form.Item>

            <Form.Item
              name="base_price"
              label={<span className="font-medium text-gray-700">Base Price</span>}
              rules={[{ required: true, message: 'Please enter base price' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'base_price'} className="font-regular-text text-black" />
            </Form.Item>
          </div>

          {/* Row 3: Duration and Topics */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="duration"
              label={<span className="font-medium text-gray-700">Duration (weeks)</span>}
              rules={[{ required: true, message: 'Please enter duration' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'duration'} className="font-regular-text text-black" />
            </Form.Item>

            <Form.Item
              name="topics"
              label={<span className="font-medium text-gray-700">Course tags</span>}
              rules={[{ required: true, message: 'Please enter topics' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate
                placeHolder='Enter topics separated by commas'
                label={''}
                fieldName={'topics'}
                className="font-regular-text text-black"
              />
            </Form.Item>
          </div>

          {/* Row 4: Level */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="level"
              label={<span className="font-medium text-gray-700">Course Level</span>}
              rules={[{ required: true, message: 'Please select level' }]}
              className="mb-0 w-full"
            >
              <SelectTemplate
                placeHolder="Select Course Level"
                options={[
                  { value: 'high_school', label: 'High School' },
                  { value: 'undergraduate', label: 'Undergraduate' },
                  { value: '100', label: 'Level 100' },
                  { value: '200', label: 'Level 200' },
                  { value: '300', label: 'Level 300' },
                  { value: '400', label: 'Level 400' },
                  { value: 'masters', label: 'Masters' },
                  { value: 'phd', label: 'PhD' },
                  { value: 'other', label: 'Other' }
                ]}
                label={''}
                fieldName="level"
                className="font-regular-text text-black"
              />
            </Form.Item>
          </div>

          {/* Row 6: Description - Full Width */}
          <div className="flex flex-col gap-4">
            <Form.Item
              name="description"
              label={<span className="font-medium text-gray-700">Description</span>}
              rules={[{ required: true, message: 'Please enter description' }]}
              className="mb-0 w-full"
            >
              <div className="relative">
                <TextAreaTemplate
                  label={''}
                  fieldName="description"
                  className="font-regular-text text-black"
                  rows={4}
                  maxLength={500}
                  onChange={handleDescriptionChange}
                />
                {/* Character counter that shows remaining characters */}
                <span className="text-gray-500 text-xs absolute bottom-2 right-4">
                  <span id="char-counter">{descriptionChars}</span> characters left
                </span>
              </div>
            </Form.Item>
          </div>

          {/* Add some extra spacing */}
          <div className="mb-4"></div>

          {/* Cohort Settings Section */}
          <div className="border-t pt-4 mt-2 mb-2">
            <h3 className="text-gray-700 font-medium mb-4">Cohort Settings</h3>
          </div>

          {/* Row 6: Cohort Settings */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="has_cohort"
              label={<span className="font-medium text-gray-700">Has Cohort</span>}
              rules={[{ required: true, message: 'Please select if course has cohorts' }]}
              className="mb-0 w-full md:w-1/3"
            >
              <SelectTemplate
                placeHolder="Select"
                options={[
                  { value: 'false', label: 'False' },
                  { value: 'true', label: 'True' }
                ]}
                label={''}
                fieldName="has_cohort"
                className="font-regular-text text-black"
              />
            </Form.Item>

            <Form.Item
              name="has_curriculum"
              label={<span className="font-medium text-gray-700">Has Curriculum</span>}
              rules={[{ required: true, message: 'Please select if course has curriculum' }]}
              className="mb-0 w-full md:w-1/3"
            >
              <SelectTemplate
                placeHolder="Select"
                options={[
                  { value: 'false', label: 'False' },
                  { value: 'true', label: 'True' }
                ]}
                label={''}
                fieldName="has_curriculum"
                className="font-regular-text text-black"
              />
            </Form.Item>

            <Form.Item
              name="is_self_paced"
              label={<span className="font-medium text-gray-700">Is Self Paced</span>}
              rules={[{ required: true, message: 'Please select if course is self-paced' }]}
              className="mb-0 w-full md:w-1/3"
            >
              <SelectTemplate
                placeHolder="Select"
                options={[
                  { value: 'false', label: 'False' },
                  { value: 'true', label: 'True' }
                ]}
                label={''}
                fieldName="is_self_paced"
                className="font-regular-text text-black"
              />
            </Form.Item>
          </div>

          {/* Row 7: Cohort Details */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="cohort_duration_weeks"
              label={<span className="font-medium text-gray-700">Cohort Duration (weeks)</span>}
              rules={[{ required: true, message: 'Please enter cohort duration' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'cohort_duration_weeks'} className="font-regular-text text-black" />
            </Form.Item>

            <Form.Item
              name="max_students_per_cohort"
              label={<span className="font-medium text-gray-700">Max Students Per Cohort</span>}
              rules={[{ required: true, message: 'Please enter max students' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate label={''} fieldName={'max_students_per_cohort'} className="font-regular-text text-black" />
            </Form.Item>
          </div>

          {/* Row 8: Auto Create Cohorts and Days Between Cohorts */}
          <div className="flex flex-col md:flex-row gap-4">
            <Form.Item
              name="auto_create_cohorts"
              label={<span className="font-medium text-gray-700">Auto Create Cohorts</span>}
              rules={[{ required: true, message: 'Please select if cohorts are auto-created' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <SelectTemplate
                placeHolder="Select"
                options={[
                  { value: 'false', label: 'False' },
                  { value: 'true', label: 'True' }
                ]}
                label={''}
                fieldName="auto_create_cohorts"
                className="font-regular-text text-black"
              />
            </Form.Item>

            <Form.Item
              name="days_between_cohorts"
              label={<span className="font-medium text-gray-700">Days Between Cohorts</span>}
              rules={[{ required: true, message: 'Please enter days between cohorts' }]}
              className="mb-0 w-full md:w-1/2"
            >
              <InputTemplate placeHolder="31" label={''} fieldName={'days_between_cohorts'} className="font-regular-text text-black" />
            </Form.Item>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-end mt-8 gap-2">
          <ButtonTemplate
            label="Cancel"
            handleClick={() => {
              setImageUrl(null);
              setDescriptionChars(500);
              onCancel();
            }}
            className="mr-2"
          />
          <Button
            type="primary"
            htmlType="submit"
            className="bg-teal-600 hover:bg-teal-700 text-white w-full sm:w-32 text-sm py-1 mt-1 sm:mt-0"
            loading={loading}
          >
            Save Changes
          </Button>
        </div>
      </Form>
    </Modal>
    </>
  );
};

export default EditModalTemplate;