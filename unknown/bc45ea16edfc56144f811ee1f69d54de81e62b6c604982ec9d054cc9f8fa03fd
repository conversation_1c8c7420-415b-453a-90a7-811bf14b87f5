'use client';
import { Course, CourseResponseData } from "@/types";
import { Progress, Rate, Tag } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useApi } from "@/hooks/useRequest";
import { Spin } from "antd";
import Image from "next/image";
import { UserOutlined } from "@ant-design/icons";

const LearningCard = ({ courseId }: { courseId: string }) => {
  const router = useRouter();
  const { request } = useApi();
  const [loading, setLoading] = useState(true);
  const [courseData, setCourseData] = useState<CourseResponseData | null>(null);

  async function getCourseDetails() {
    try {
      setLoading(true);
      const response = await request("GET", `/course/${courseId}`, null, "multipart/form-data");
      if (response?.status === 200) {
        setCourseData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching course details:', error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getCourseDetails();
  }, [courseId]);

  if (loading) {
    return <div className="bg-white p-4 rounded-lg"><Spin /></div>;
  }

  if (!courseData) {
    return null;
  }

  const { course, teacher, total_student_enrolled, cover_image } = courseData;
 
  return (
    <div onClick={()=>{ router.push(`/student/courses/${course.id}/details`)}} className="bg-white text-sm w-full rounded-lg overflow-hidden shadow-lg px-4 border border-gray-200 hover:cursor-pointer">
     <div className="relative pt-4">
      
     <Image src={ `${cover_image && cover_image.startsWith('iVBOR')? 'data:image/png;base64': 'data:image/jpeg;base64'},${cover_image}`}
     height={150} width={200} 
     className="h-[150px] object-cover"
     alt='course frame' />

     <div className="absolute bottom-4 right-2 px-2 py-1 rounded-lg">
          <Rate allowHalf defaultValue={teacher.rating || 4.5} />
        </div>
     </div>
      
      <div className="py-2">
        <div className="font-bold text-sm mb-2 text-black">
          {course.name}
        </div>
       
        <div className="flex items-center mb-4">
          {teacher.profile_image_path && !teacher.profile_image_path.startsWith('gs://') ? (
            <Image 
              src={`${teacher.profile_image_path.startsWith('iVBOR') ? 'data:image/png;base64' : 'data:image/jpeg;base64'},${teacher.profile_image_path}`}
              height={32} 
              width={32}
              className="rounded-full"
              alt="Avatar"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-teal-50 flex items-center justify-center">
              <UserOutlined className="text-teal-600" />
            </div>
          )}
          <span className="ml-2 text-xs font-semibold">{teacher.name}</span>
         </div>
         <div>{course.duration} weeks • {total_student_enrolled} student(s)</div>
         <Progress strokeColor={"#008080"} percent={0} />
      </div>
    </div>
  );
};

export default LearningCard;
