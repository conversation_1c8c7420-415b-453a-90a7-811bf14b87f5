'use client';
import { Rate, Tag } from "antd";
import TagTemplate from "./tag-template";
import { useRouter } from "next/navigation";

const InstructorCard = () => {
  const router = useRouter();
 
  return (
    <div onClick={()=>{  router.push('/student/courses/1/details')}} className="z-50 bg-white text-sm max-w-[18rem] rounded-lg overflow-hidden shadow-lg px-4  border border-gray-200 hover:cursor-pointer">
     <div className="relative pt-4">
     <img
        className="w-full h-[9rem] object-cover rounded-lg"
        src="/stud3.jpg" // Replace with actual image path
        alt="Course Image"
      />
     <div className="absolute bottom-4 right-2  px-2 py-1 rounded-lg">
          <Rate allowHalf defaultValue={4.5} />
        </div>
     </div>
      

      <div className=" py-2">
        <div className="flex  mb-2">
          <TagTemplate className="rounded-md !bg-black" handleClick={()=>{}} closeIcon={false} value={"Cloud"} />
          <TagTemplate className="rounded-md" handleClick={()=>{}} closeIcon={false} value={"Intermediate"} />
        </div>
        <div className="font-bold text-sm mb-2 text-primaryColor">
          The Ultimate Beginners Guide to UI Design
        </div>
       
        <div className="flex items-center mb-2">
          <img
            className="w-8 h-8 rounded-full"
            src="/rename.webp" // Replace with actual avatar path
            alt="Avatar"
          />
          <span className="ml-2 text-primaryColor text-xs font-semibold">Jane Doe</span>
        </div>
        <div className="">
          <span className="font-bold text-primaryColor">GHS 15</span>
          <span className="font-bold ml-2 line-through text-red-500">GHS 5</span>
        </div>
      </div>
    </div>
  );
};

export default InstructorCard;
