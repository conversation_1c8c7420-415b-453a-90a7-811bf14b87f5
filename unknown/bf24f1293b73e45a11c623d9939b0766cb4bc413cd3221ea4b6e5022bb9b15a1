import { Empty, Table } from 'antd';
import React from 'react'
import { PurchaseLogics } from '../_logics/purchase-logics';

export default function Purchases() {
    const {columns} = PurchaseLogics()
  return (
    <Table
                  columns={columns}
                  dataSource={[]}
                  pagination={false}
                  bordered
                  onChange={()=>{}}
                 sticky
                  onRow={(record) => ({
                    onClick: () => {
                     
                    },
                   })}
                />
  )
}
