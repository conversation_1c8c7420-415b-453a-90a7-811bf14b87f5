'use client';
import { ExamData } from "../data/exam-data";
import { Progress, Rate, Tag, Button } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useApi } from "@/hooks/useRequest";
import { Spin } from "antd";

const LearningCard = ({ exam }: { exam: ExamData }) => {
  const router = useRouter();
 
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'ongoing':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleExamAction = () => {
    if (exam.status === 'upcoming') {
      // Handle start exam
      router.push(`/student/exams/${exam.id}/start`);
    } else if (exam.status === 'ongoing') {
      // Handle resume exam
      router.push(`/student/exams/${exam.id}/resume`);
    }
  };

  return (
    <div 
      onClick={() => { router.push(`/student/exams/${exam.id}/details`) }} 
      className="bg-white text-sm w-full rounded-lg overflow-hidden shadow-lg px-4 border border-gray-200 hover:cursor-pointer"
    >
      <div className="py-4">
        <div className="flex justify-between items-start mb-2">
          <div className="font-bold text-base text-black">
            {exam.name}
          </div>
          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`}>
            {exam.status}
          </span>
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <div className="text-gray-600 text-sm">
            Duration: {exam.duration} mins
          </div>
          <div className="text-gray-600 text-sm">
            Pass Marks: {exam.pass_marks}
          </div>
        </div>

        <div className="mb-4">
          <div className="text-black text-sm mb-1">Exam Schedule</div>
          <div className="text-sm text-black">
            <div>Start: {formatDate(exam.start_datetime)}</div>
            <div>End: {formatDate(exam.end_datetime)}</div>
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="text-gray-600 text-sm">
            Total Marks: {exam.total_marks}
          </div>
          {exam.completed && (
            <div className={`px-2 py-1 rounded-full text-xs ${
              exam.total_marks >= exam.pass_marks ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {exam.total_marks >= exam.pass_marks ? 'Passed' : 'Failed'}
            </div>
          )}
        </div>

        {(exam.status === 'upcoming' || exam.status === 'ongoing') && (
          <Button 
            type="primary" 
            className="w-full"
            onClick={handleExamAction}
          >
            {exam.status === 'upcoming' ? 'Start' : 'Resume'}
          </Button>
        )}
      </div>
    </div>
  );
};

export default LearningCard;
