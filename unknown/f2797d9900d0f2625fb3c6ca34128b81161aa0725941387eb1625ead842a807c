// 'use client';

// import React, { useState, useEffect } from 'react';
// import { useRouter, useSearchParams } from 'next/navigation';
// import { Card, Steps, Button, Form, Input, Select, message, Tag, List, Avatar, Tooltip, Checkbox } from 'antd';
// import { VideoCameraOutlined, UserOutlined, SendOutlined, TeamOutlined, CheckCircleOutlined } from '@ant-design/icons';
// import { motion } from 'framer-motion';

// // Import components
// import Sidebar from '@/components/general/dashboard/sidebar/sidebar';
// import TopBar from '@/components/general/dashboard/sidebar/topbar';
// import { getThemeStyles, useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
// import ButtonTemplate from '@/components/ui/button-template';

// // Import data and types - fixed import
// import {
//   fetchSessionById,
//   mockStudents,
//   defaultSessionSettings,
//   generateSessionJoinLink,
//   getDefaultNotificationMessage,
//   SessionSettings,
//   SessionStudent
// } from '../dummydata/startsessiondata';
// import { LiveSessionData } from '../dummydata/livesessiondata';

// const StartSessionPage = () => {
//   const router = useRouter();
//   const searchParams = useSearchParams();
//   const sessionId = searchParams.get('id');

//   const [currentStep, setCurrentStep] = useState(0);
//   const [sessionData, setSessionData] = useState<LiveSessionData | null>(null);
//   const [isSidebarOpen, setIsSidebarOpen] = useState(true);
//   const [isMobile, setIsMobile] = useState(false);
//   const [loading, setLoading] = useState(true);
//   const [startingSession, setStartingSession] = useState(false);
//   const [sendingNotification, setSendingNotification] = useState(false);
//   const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
//   const [joinLink, setJoinLink] = useState('');
//   const [sessionSettings, setSessionSettings] = useState<SessionSettings>(defaultSessionSettings);
//   const [notificationMessage, setNotificationMessage] = useState('');

//   const { isDark } = useTeacherTheme();
//   const themeStyles = getThemeStyles(isDark);

//   // Check if screen is mobile on component mount and window resize
//   useEffect(() => {
//     const checkIfMobile = () => {
//       setIsMobile(window.innerWidth < 768);
//       if (window.innerWidth < 768) {
//         setIsSidebarOpen(false);
//       }
//     };

//     checkIfMobile();
//     window.addEventListener('resize', checkIfMobile);
//     return () => window.removeEventListener('resize', checkIfMobile);
//   }, []);

//   // // Fetch session data
//   // useEffect(() => {
//   //   const fetchSessionData = async () => {
//   //     if (!sessionId) {
//   //       message.error('No session ID provided');
//   //       router.push('../dashboard/livesession');
//   //       return;
//   //     }

//   //     try {
//   //       const session = await fetchSessionById(sessionId);

//   //       if (!session) {
//   //         message.error('Session not found');
//   //         router.push('../dashboard/livesession');
//   //         return;
//   //       }

//   //       // Fix: properly set the session data
//   //       setSessionData(session);
//   //       setJoinLink(generateSessionJoinLink(sessionId));
//   //       setSelectedStudents(mockStudents.map(student => student.id));
//   //       setNotificationMessage(getDefaultNotificationMessage(session.title));
//   //       setLoading(false);
//   //     } catch (error) {
//   //       console.error('Error fetching session data:', error);
//   //       message.error('Failed to load session data');
//   //       setLoading(false);
//   //     }
//   //   };

//   //   fetchSessionData();
//   // }, [sessionId, router]);

//   const toggleSidebar = () => {
//     setIsSidebarOpen(!isSidebarOpen);
//   };

//   // const handleSettingChange = (setting: keyof SessionSettings, value: boolean) => {
//   //   setSessionSettings(prev => ({
//   //     ...prev,
//   //     [setting]: value
//   //   }));
//   // };

//   // const handleStudentSelection = (studentId: string) => {
//   //   setSelectedStudents(prevSelected =>
//   //     prevSelected.includes(studentId)
//   //       ? prevSelected.filter(id => id !== studentId)
//   //       : [...prevSelected, studentId]
//   //   );
//   // };

//   const handleSelectAllStudents = () => {
//     setSelectedStudents(
//       selectedStudents.length === mockStudents.length
//         ? []
//         : mockStudents.map(student => student.id)
//     );
//   };

//   const handleSendNotification = async () => {
//     setSendingNotification(true);
//     try {
//       // Simulate API call
//       await new Promise(resolve => setTimeout(resolve, 1500));
//       message.success(`Notification sent to ${selectedStudents.length} students`);
//       setSendingNotification(false);
//       setCurrentStep(currentStep + 1);
//     } catch (error) {
//       console.error('Error sending notifications:', error);
//       message.error('Failed to send notifications');
//       setSendingNotification(false);
//     }
//   };

//   const handleStartSession = async () => {
//     setStartingSession(true);
//     try {
//       // Simulate API call
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       message.success('Session started successfully!');

//       // In a real app, you would update the session status via API

//       // Navigate to virtual classroom
//       router.push(`/dashboard/classroom/${sessionId}`);
//     } catch (error) {
//       console.error('Error starting session:', error);
//       message.error('Failed to start session');
//       setStartingSession(false);
//     }
//   };

//   // Steps content components to keep the main component cleaner
//   const SessionDetailsStep = () => (
//     <div className="space-y-6">
//     <Card className="shadow-sm" title="Session Information">
//       {sessionData && (
//         <div className="space-y-4">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div>
//               <p className="text-gray-500 text-sm">Title</p>
//               <p className="font-medium">{sessionData.title}</p>
//             </div>
//             <div>
//               <p className="text-gray-500 text-sm">Course</p>
//               <p className="font-medium">{sessionData.courseCode} - {sessionData.programName}</p>
//             </div>
//             <div>
//               <p className="text-gray-500 text-sm">Date & Time</p>
//               <p className="font-medium">{new Date(sessionData.scheduledDate).toLocaleString()}</p>
//             </div>
//             <div>
//               <p className="text-gray-500 text-sm">Duration</p>
//               <p className="font-medium">{sessionData.duration} minutes</p>
//             </div>
//             <div>
//               <p className="text-gray-500 text-sm">Students</p>
//               <p className="font-medium">
//                 {sessionData.status === 'completed' 
//                   ? `${sessionData.enrolledStudents} students attended`
//                   : `${sessionData.enrolledStudents} students expected`}
//               </p>
//             </div>
//           </div>
//           {sessionData.description && (
//             <div>
//               <p className="text-gray-500 text-sm">Description</p>
//               <p>{sessionData.description}</p>
//             </div>
//           )}
//           {sessionData.materials && sessionData.materials.length > 0 && (
//             <div>
//               <p className="text-gray-500 text-sm">Materials</p>
//               <div className="flex flex-wrap gap-2 mt-1">
//                 {sessionData.materials.map((material, index) => (
//                   <Tag key={index} color="blue">{material}</Tag>
//                 ))}
//               </div>
//             </div>
//           )}
//         </div>
//       )}
//     </Card>
    

//       <Card className="shadow-sm" title="Session Settings">
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//           {Object.entries(sessionSettings).map(([key, value]) => (
//             <div key={key} className="flex items-center justify-between">
//               <span>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
//               <Select
//                 value={value}
//                 // onChange={(val) => handleSettingChange(key as keyof SessionSettings, val)}
//                 options={[
//                   { value: true, label: key.startsWith('allow') ? 'Allow' : 'Enable' },
//                   { value: false, label: key.startsWith('allow') ? 'Disable' : 'Disable' }
//                 ]}
//                 style={{ width: 100 }}
//               />
//             </div>
//           ))}
//         </div>
//       </Card>
//     </div>
//   );

//   const InviteStudentsStep = () => (
//     <div className="space-y-6">
//       <Card className="shadow-sm" title="Session Link">
//         <div className="space-y-4">
//           <p className="text-sm text-gray-500">Share this link with your students:</p>
//           <div className="flex items-center">
//             <Input readOnly value={joinLink} className="flex-grow" />
//             <Button
//               type="primary"
//               className="ml-2"
//               onClick={() => {
//                 navigator.clipboard.writeText(joinLink);
//                 message.success('Link copied to clipboard');
//               }}
//             >
//               Copy
//             </Button>
//           </div>
//         </div>
//       </Card>

//       <Card className="shadow-sm" title="Students">
//         <div className="space-y-4">
//           <div className="flex justify-between items-center">
//             <p className="text-sm text-gray-500">Select students to notify:</p>
//             <Button type="link" onClick={handleSelectAllStudents}>
//               {selectedStudents.length === mockStudents.length ? 'Deselect All' : 'Select All'}
//             </Button>
//           </div>

//           <List
//             itemLayout="horizontal"
//             dataSource={mockStudents}
//             renderItem={student => (
//               <List.Item
//                 className="cursor-pointer hover:bg-gray-50 rounded-md p-2"
//                 // onClick={() => handleStudentSelection(student.id)}
//               >
//                 <List.Item.Meta
//                   avatar={
//                     <div className="relative">
//                       <Avatar src={student.avatar} icon={<UserOutlined />} />
//                       <div
//                         className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white ${student.status === 'online' ? 'bg-green-500' :
//                             student.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
//                           }`}
//                       />
//                     </div>
//                   }
//                   title={student.name}
//                   description={<span className="capitalize">{student.status}</span>}
//                 />
//                 {/* Fixed: Checkbox should be from Form, not Form.Checkbox */}
//                 <Checkbox
//                   checked={selectedStudents.includes(student.id)}
//                   // onChange={() => handleStudentSelection(student.id)}
//                 />
//               </List.Item>
//             )}
//           />

//           <Form layout="vertical" className="mt-4">
//             <Form.Item label="Notification Message">
//               <Input.TextArea
//                 value={notificationMessage}
//                 onChange={(e) => setNotificationMessage(e.target.value)}
//                 rows={4}
//               />
//             </Form.Item>
//           </Form>
//         </div>
//       </Card>
//     </div>
//   );

//   const StartSessionStep = () => (
//     <Card className="shadow-sm text-center py-8">
//       <div className="space-y-6">
//         <div className="flex justify-center">
//           <div className="w-24 h-24 rounded-full bg-blue-50 flex items-center justify-center">
//             <VideoCameraOutlined style={{ fontSize: '36px', color: '#1890ff' }} />
//           </div>
//         </div>

//         <div className="space-y-2">
//           <h2 className="text-2xl font-medium">Ready to Begin</h2>
//           <p className="text-gray-500 max-w-md mx-auto">
//             You're all set to start your session. Students have been notified and can join using the link provided.
//           </p>
//         </div>

//         <div className="space-y-4">
//           <div className="flex justify-center items-center space-x-4">
//             <div className="flex items-center">
//               <TeamOutlined className="mr-2 text-blue-500" />
//               <span>{selectedStudents.length} students invited</span>
//             </div>
//             <div className="flex items-center">
//               <CheckCircleOutlined className="mr-2 text-green-500" />
//               <span>Settings configured</span>
//             </div>
//           </div>

//           <p className="text-sm text-gray-500">
//             You can adjust additional settings once the session begins.
//           </p>
//         </div>
//       </div>
//     </Card>
//   );

//   const steps = [
//     {
//       title: 'Session Details',
//       content: <SessionDetailsStep />
//     },
//     {
//       title: 'Invite Students',
//       content: <InviteStudentsStep />
//     },
//     {
//       title: 'Start Session',
//       content: <StartSessionStep />
//     },
//   ];

//   return (
//     <div className="flex h-screen">
//       {/* Sidebar */}
//       <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />

//       <div className="flex flex-col flex-grow">
//         {/* Top Bar */}
//         <TopBar toggleSidebar={toggleSidebar} />

//         {/* Main Content */}
//         <div className="p-3 md:p-6 overflow-auto">
//           <motion.div
//             initial={{ opacity: 0, y: -20 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ duration: 0.5 }}
//           >
//             <div className="flex justify-between items-center mb-6">
//               <h1 className="text-xl md:text-2xl font-light">Start Session</h1>
//               <Button onClick={() => router.push('/dashboard/livesession')}>
//                 Cancel
//               </Button>
//             </div>

//             <div className="bg-white rounded-lg shadow-md p-4 md:p-6">
//               {loading ? (
//                 <div className="flex justify-center items-center h-64">
//                   <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
//                 </div>
//               ) : (
//                 <>
//                   <Steps
//                     current={currentStep}
//                     items={steps.map(item => ({ title: item.title }))}
//                     className="mb-8"
//                   />

//                   <div className="steps-content">
//                     {steps[currentStep].content}
//                   </div>

//                   <div className="steps-action mt-8 flex justify-between">
//                     {currentStep > 0 && (
//                       <Button onClick={() => setCurrentStep(currentStep - 1)}>
//                         Previous
//                       </Button>
//                     )}

//                     {currentStep === 0 && (
//                       <div className="ml-auto">
//                         <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
//                           Continue
//                         </Button>
//                       </div>
//                     )}

//                     {currentStep === 1 && (
//                       <div className="ml-auto">
//                         <ButtonTemplate
//                           label="Send Notification & Continue"
//                           icon={<SendOutlined />}
//                           onClick={handleSendNotification}
//                           loading={sendingNotification}
//                           disabled={selectedStudents.length === 0}
//                           className="!px-4 !py-2"
//                         />
//                       </div>
//                     )}

//                     {currentStep === 2 && (
//                       <div className="ml-auto">
//                         <ButtonTemplate
//                           label="Start Session Now"
//                           icon={<VideoCameraOutlined />}
//                           onClick={handleStartSession}
//                           loading={startingSession}
//                           className="!px-4 !py-2"
//                         />
//                       </div>
//                     )}
//                   </div>
//                 </>
//               )}
//             </div>
//           </motion.div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default StartSessionPage;