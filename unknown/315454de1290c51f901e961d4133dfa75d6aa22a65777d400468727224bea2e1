export interface PlanInterface {
  title: string;
  description: string;
  price: number;
  recommended?: boolean;
  features: string[];
  disabledFeatures?: string[];
}

export const getPricingPlans = (isYearly: boolean): PlanInterface[] => [
  {
    title: "Starter Verification",
    description: "For small to medium organizations",
    price: isYearly ? 299 : 35,
    features: [
      "Up to 1,000 Certificate Verifications/Month",
      "Basic Fraud Detection",
      "Single Domain Verification",
      "Standard API Access",
      "Email Support"
    ],
    disabledFeatures: [
      "Advanced Fraud Analytics",
      "Multi-Domain Verification", 
      "Custom Branding"
    ]
  },
  {
    title: "Enterprise Credential Shield",
    description: "Comprehensive verification for growing organizations",
    price: isYearly ? 999 : 119,
    recommended: true,
    features: [
      "Unlimited Certificate Verifications",
      "Advanced Fraud Prevention",
      "Multi-Domain Verification",
      "Real-time Verification Alerts",
      "Customizable Verification Workflows",
      "Dedicated Account Manager",
      "Custom Branding Options",
      "Compliance reportsing"
    ]
  },
  {
    title: "Global Trust Network",
    description: "For large enterprises with complex verification needs",
    price: isYearly ? 2499 : 299,
    features: [
      "Enterprise-Scale Verification",
      "AI-Powered Fraud Detection",
      "Unlimited Domains & Integrations",
      "24/7 Premium Support",
      "Blockchain-Backed Verification",
      "Custom Machine Learning Models",
      "Comprehensive Compliance Suite",
      "Executive Quarterly Review"
    ]
  }
];