import { useState, useEffect } from "react";
import axios from "axios";
import Cookies from "js-cookie";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export const useFetch = (endpoint: string) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  // We'll get the token at the time of the request




  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const currentToken = Cookies.get("access_token");
      console.log('Current token:', currentToken)

      try {
        const response = await axios.get(`${API_BASE_URL}${endpoint}`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: currentToken ? `Bearer ${currentToken}` : "", // Add Bearer token if available
          },
        });
        setData(response.data);

      } catch (err: any) {
        console.log('Fetch error:', err);
        console.log('Error response:', err?.response);
        console.log('Error message:', err?.message);
        setError(err?.response || { status: 500, data: { detail: err?.message || 'Unknown error' } });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [endpoint]);

  return { data, loading, error };
};
