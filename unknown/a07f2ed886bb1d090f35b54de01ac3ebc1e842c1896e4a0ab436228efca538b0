'use client'
import React from 'react';
import { Button, Card, Form, Modal, Spin, Switch } from 'antd';
import { ExclamationCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { useUserStore } from '@/store/userStore';
import { useTeacherSettings } from '@/logics/useTeacherSettings';

export default function TeacherSettings() {
  const { user } = useUserStore();
  const {
    loading,
    autoLogoutEnabled,
    handleAutoLogoutChange,
    deactivateTeacherAccount,
    deleteTeacherAccount
  } = useTeacherSettings();
  const [modal, contextHolder] = Modal.useModal();
  const [autoLogoutForm] = Form.useForm();

  // Confirmation modals
  const showDeleteConfirm = () => {
    modal.confirm({
      title: 'Are you sure you want to delete your account?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone. All your data will be permanently deleted.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        deleteTeacherAccount();
      },
    });
  };

  const showDeactivateConfirm = () => {
    modal.confirm({
      title: 'Are you sure you want to deactivate your account?',
      icon: <WarningOutlined />,
      content: 'You can reactivate your account by contacting support or using the account recovery option.',
      okText: 'Yes, Deactivate',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        deactivateTeacherAccount();
      },
    });
  };

  return (
    <div className="p-6">
      {contextHolder}
      {loading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-75">
          <Spin size="large" />
        </div>
      )}
      {/* <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-teal-700">Settings</h1>
      </div> */}

      <div className="max-w-5xl mx-auto">
        {/* Privacy Settings Card */}
        <Card className="shadow-lg border-teal-100 rounded-xl overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-6">
          <div className="px-2">
            <h3 className="text-lg font-medium text-teal-700 mb-4 border-b pb-2 border-teal-100">Privacy Settings</h3>

            <div className="mt-4">
              <div className="flex items-center justify-between p-4 border border-gray-100 rounded-md">
                <div>
                  <h4 className="text-md font-medium text-gray-900">Auto Logout</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatically log out after a period of inactivity
                  </p>
                </div>
                <Form
                  name="autoLogoutForm"
                  form={autoLogoutForm}
                  initialValues={{ autoLogout: autoLogoutEnabled }}
                >
                  <Switch
                    checked={autoLogoutEnabled}
                    onChange={handleAutoLogoutChange}
                    className={autoLogoutEnabled ? "bg-teal-500" : "bg-gray-300"}
                  />
                </Form>
              </div>
            </div>
          </div>
        </Card>

        {/* Privacy & Security Card */}
        <Card className="shadow-lg border-teal-100 rounded-xl overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-6">
          <div className="px-2">
            <h3 className="text-lg font-medium text-teal-700 mb-4 border-b pb-2 border-teal-100">Privacy & Security</h3>

            <div className="space-y-4">
              {/* Two-Factor Authentication */}
              <div className="flex items-center justify-between p-4 border border-gray-100 rounded-md">
                <div>
                  <h4 className="text-md font-medium text-gray-900">Two-Factor Authentication</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button
                  className="bg-teal-500 hover:bg-teal-600 text-white"
                  disabled
                >
                  Coming Soon
                </Button>
              </div>

              {/* Email Notifications */}
              <div className="flex items-center justify-between p-4 border border-gray-100 rounded-md">
                <div>
                  <h4 className="text-md font-medium text-gray-900">Email Notifications</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Receive email alerts for account activities
                  </p>
                </div>
                <Switch
                  defaultChecked
                  disabled
                  className="bg-teal-500"
                />
              </div>

              {/* Session Management
              <div className="flex items-center justify-between p-4 border border-gray-100 rounded-md">
                <div>
                  <h4 className="text-md font-medium text-gray-900">Active Sessions</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage your active login sessions
                  </p>
                </div>
                <Button
                  className="bg-teal-500 hover:bg-teal-600 text-white"
                  disabled
                >
                  View Sessions
                </Button>
              </div> */}

              {/* Role Information */}
              <div className="p-4 border border-gray-100 rounded-md">
                <h4 className="text-md font-medium text-gray-900 mb-2">Account Type</h4>
                <div className="flex items-center space-x-2">
                  <div className="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-medium">
                    {user?.role === 3 ? "Teacher" : "Unknown"}
                  </div>
                  <p className="text-sm text-gray-600">
                    This determines your access level and permissions
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Security FAQ Card */}
        <Card className="shadow-lg border-teal-100 rounded-xl overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-6">
          <div className="px-2">
            <h3 className="text-lg font-medium text-teal-700 mb-4 border-b pb-2 border-teal-100">Security Tips</h3>

            <div className="space-y-4">
              <div className="p-4 border-l-4 border-teal-500 bg-teal-50 rounded-r-md">
                <h4 className="text-md font-medium text-gray-900 mb-1">Use a Strong Password</h4>
                <p className="text-sm text-gray-700">
                  Create a unique password with a mix of letters, numbers, and symbols. Avoid using personal information.
                </p>
              </div>

              <div className="p-4 border-l-4 border-teal-500 bg-teal-50 rounded-r-md">
                <h4 className="text-md font-medium text-gray-900 mb-1">Be Careful with Shared Devices</h4>
                <p className="text-sm text-gray-700">
                  Always log out when using public or shared computers. Don't save your login information on these devices.
                </p>
              </div>

              <div className="p-4 border-l-4 border-teal-500 bg-teal-50 rounded-r-md">
                <h4 className="text-md font-medium text-gray-900 mb-1">Watch Out for Phishing</h4>
                <p className="text-sm text-gray-700">
                  Be cautious of emails asking for your password or personal information. We will never ask for your password via email.
                </p>
              </div>

              <div className="p-4 border-l-4 border-teal-500 bg-teal-50 rounded-r-md">
                <h4 className="text-md font-medium text-gray-900 mb-1">Keep Your Contact Info Updated</h4>
                <p className="text-sm text-gray-700">
                  Ensure your email and phone number are current so we can contact you about security issues or account recovery.
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Account Management Card */}
        <Card className="shadow-lg border-teal-100 rounded-xl overflow-hidden hover:shadow-xl transition-shadow duration-300">
          <div className="px-2">
            <h3 className="text-lg font-medium text-teal-700 mb-4 border-b pb-2 border-teal-100">Account Management</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Deactivate Account */}
              <div className="p-4 border border-gray-200 rounded-md hover:border-yellow-300 transition-colors">
                <h4 className="text-md font-medium mb-2 text-gray-900">Deactivate Account</h4>
                <p className="text-sm text-gray-700 mb-4">
                  Temporarily disable your account. You can reactivate it later.
                </p>
                <Button
                  onClick={showDeactivateConfirm}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white"
                >
                  Deactivate Account
                </Button>
              </div>

              {/* Delete Account */}
              <div className="p-4 border border-red-200 rounded-md bg-red-50 hover:bg-red-100 transition-colors">
                <h4 className="text-md font-medium mb-2 text-red-700">Delete Account</h4>
                <p className="text-sm text-gray-700 mb-4">
                  Permanently remove your account and all data.
                </p>
                <Button
                  danger
                  onClick={showDeleteConfirm}
                >
                  Delete Account
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
