"use client";
import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  MailOutlined} from "@ant-design/icons";
import {
  Form,
  FormProps,
  ThemeConfig,
} from "antd";
import InputTemplate from "@/components/ui/input-template";
import ButtonTemplate from "@/components/ui/button-template";
import { AuthLogics } from "../_logics/auth-logics";


const theme: ThemeConfig = {
  token: {
    colorPrimary: "#008080", // Use `colorPrimary` instead of `basic`
    //   primary: "#FFFFFF",
  },
};

function ForgotPassword() {
  const router = useRouter();

     const {forgotPassword, loading} = AuthLogics()
  
const onFinish: FormProps<any>['onFinish'] = (info) => {
      // Create A new User
      forgotPassword(info);
     };


  return (
    <div className="bg-[#f4f8f7]  rounded-xl  ">
      <div className="flex flex-row    ">
        <div className="hidden md:block ">
          <Image className="mt-40" src="/signup.png" alt="Image1" width={400} height={10} />
        </div>

        <div className="mt-5 mr-3 flex flex-1 px-4 flex-col ">
          <div className="flex justify-center mt-12">
            <Image className="shadow-xl rounded-full" src="/logo.png" alt="logo" width={70} height={70} />
          </div>
          <div className="flex justify-center mt-12">
            <div className="font-bold text-2xl text-primaryColor">
              Forgot Password!!
            </div>
          </div>
          <div className="flex justify-center text-center text-xs font-semibold text-black mt-4 mb-12">
          Enter the email associated with your account and we’ll send<br/>a message with instructions to reset your account 
          </div>
          <Form
            name="forgotPasswordForm"
            onFinish={onFinish}
            >
          <div className="flex justify-center gap-x-10 px-20">
          
            <div className="w-1/2">
              <InputTemplate fieldName="email" prefix={<MailOutlined />} placeHolder="<EMAIL>" label={"Email"} className="" />
            </div>

          </div>
          <div className="justify-center flex px-20">
          </div>
          <div className="flex justify-center mt-10">
            <ButtonTemplate
           htmlType="submit"
           label={loading? 'Loading...':'Submit'}
          
           className="bg-transparent !text-base text-primaryColor font-semibold h-10 w-[16rem]" />
          </div>
          </Form>
          <span className="flex text-xs font-semibold mt-5 justify-center"> <p className="flex justify-center  text-gray-400 mr-1">Already have an account?</p> <p onClick={()=>{
        router.push('/auth/signin')
          }} className="text-primaryColor hover:underline hover:cursor-pointer ">Sign in</p></span>


        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
