/**
 * Utility functions for handling image URLs in the application
 */

/**
 * Converts a Google Cloud Storage URL (gs://) to a publicly accessible HTTP URL
 * 
 * @param url The URL to convert, which may be a gs:// URL or already HTTP
 * @returns A browser-compatible URL
 */
export const getImageUrl = (url: string | null | undefined): string => {
  if (!url) return '';
  
  // For debugging
  console.log('Processing image URL:', url);
  
  // If URL is already a data URL, return it as is
  if (url.startsWith('data:')) return url;
  
  // If URL is a base64 string without the data prefix, add it
  if (url.startsWith('/9j/') || url.match(/^[A-Za-z0-9+/=]+$/)) {
    return `data:image/jpeg;base64,${url}`;
  }
  
  // Convert Google Cloud Storage URLs
  if (url.startsWith('gs://')) {
    try {
      // Extract bucket name and object path
      const gcsRegex = /gs:\/\/([^\/]+)\/(.+)/;
      const match = url.match(gcsRegex);
      
      if (match && match.length >= 3) {
        const bucket = match[1];
        let objectPath = match[2];
        
        // Handle special case where email is directly attached to 'students' without slash
        if (objectPath.startsWith('students') && objectPath.includes('@') && !objectPath.startsWith('students/')) {
          // Insert a slash after 'students'
          objectPath = objectPath.replace('students', 'students/');
        }
        
        // Special case for your specific URL pattern with email
        if (objectPath.includes('<EMAIL>')) {
          objectPath = objectPath.replace('<EMAIL>', 'students/<EMAIL>');
          console.log('Fixed special URL pattern, new path:', objectPath);
        }
        
        // Convert to a public HTTP URL
        // This uses the Storage Browser public URL format
        const publicUrl = `https://storage.googleapis.com/${bucket}/${objectPath}`;
        console.log('Converted GCS URL:', publicUrl);
        return publicUrl;
      }
    } catch (error) {
      console.error('Error parsing GCS URL:', error);
      // Return a default fallback image or empty string
      return '';
    }
  }
  
  // If it's an HTTP URL, return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // Handle potential Firebase/GCP storage URLs that don't start with gs://
  // For example "learnkonnect-np/students/profile_image/..."
  if (url.includes('learnkonnect-np/') && !url.startsWith('/')) {
    return `https://storage.googleapis.com/${url}`;
  }
  
  // If it's a relative URL, make sure it starts with / (could add baseURL if needed)
  if (!url.startsWith('/')) {
    return `/${url}`;
  }
  
  return url;
};

/**
 * Gets a profile image URL from various possible student object formats
 * 
 * @param student The student object which might contain image in different formats
 * @returns A browser-compatible profile image URL
 */
export const getProfileImageUrl = (student: any): string => {
  if (!student) return '';
  
  console.log('Student profile image data:', {
    profile_image_path: student.profile_image_path,
    profile_image: student.profile_image?.substring(0, 50) + '...',
    profile_image_path_server: student.profile_image_path_server
  });
  
  // Try different possible image sources in order of preference
  if (student.profile_image_path) {
    const url = getImageUrl(student.profile_image_path);
    console.log('Using profile_image_path:', url);
    return url;
  }
  
  if (student.profile_image) {
    const url = getImageUrl(student.profile_image);
    console.log('Using profile_image:', url);
    return url;
  }
  
  if (student.profile_image_path_server) {
    const url = getImageUrl(student.profile_image_path_server);
    console.log('Using profile_image_path_server:', url);
    return url;
  }
  
  return '';
};

/**
 * Gets a cover image URL from various possible student object formats
 * 
 * @param student The student object which might contain image in different formats
 * @returns A browser-compatible cover image URL
 */
export const getCoverImageUrl = (student: any): string => {
  if (!student) return '';
  
  console.log('Student cover image data:', {
    cover_image_path: student.cover_image_path,
    cover_image: student.cover_image?.substring(0, 50) + '...',
    cover_image_path_server: student.cover_image_path_server
  });
  
  // Try different possible image sources in order of preference
  if (student.cover_image_path) {
    const url = getImageUrl(student.cover_image_path);
    console.log('Using cover_image_path:', url);
    return url;
  }
  
  if (student.cover_image) {
    const url = getImageUrl(student.cover_image);
    console.log('Using cover_image:', url);
    return url;
  }
  
  if (student.cover_image_path_server) {
    const url = getImageUrl(student.cover_image_path_server);
    console.log('Using cover_image_path_server:', url);
    return url;
  }
  
  return '';
};
