import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  const publicPages = [
    '/',
    // AUTH SCREENS PAGES
    '/auth/signin',
    '/auth/signup',
    '/auth/change-password',
    '/auth/forgot-password',
    '/auth/restore',


    // LANDING PAGE
    '/pricing',
    '/product',
    '/customerSolution',
    '/community',
    '/contact us',
    '/cert-verification',
    '/aboutPage',
    '/contactInformation',
  ];

  const { pathname } = req.nextUrl;
  const accessToken = req.cookies.get("access_token");

  // Log token for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log(`Middleware - Path: ${pathname}, Token exists: ${!!accessToken}`);
  }

  const isPublicRoute = publicPages.includes(pathname);

  // If there's no access token and the route is NOT public, redirect to home
  if (!accessToken || accessToken.value === 'undefined') {
    if (!isPublicRoute) {
      return NextResponse.redirect(new URL(`/`, req.url));
    }
  } else {
    // If the user is authenticated and tries to access a public page, redirect based on role
    // Note: We can't access user role in middleware directly, so we'll rely on the setUpMe function
    // for proper role-based routing after login. This is just a fallback.

    // Don't redirect dashboard or student pages - they're already in the right place
    if(pathname.startsWith("/dashboard") || pathname.startsWith("/student")){
      return NextResponse.next();
    }

    // For other public pages, we'll let the login flow handle proper routing
    // based on user role in the setUpMe function
  }

  return NextResponse.next(); // Allow request to proceed
}

// Protect all routes except static files and public assets
export const config = {
  matcher: "/((?!_next|static|public|favicon.ico|.*\\.(?:png|jpg|jpeg|svg|gif|webp)$).*)",
};
