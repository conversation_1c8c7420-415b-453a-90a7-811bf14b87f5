import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form } from "antd";
import { useState } from "react"
import { useRouter } from "next/navigation";

import Cookies from "js-cookie";
import { Spin } from 'antd';
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useStudentStore } from "@/store/studentStore";
import { useCartStore } from "@/store/cartStore";
import { useUserStore } from "@/store/userStore";
import { useLecturerStore } from "@/store/lecturerStore";

export const UserOverviewLogics = () => {

  const { new_course_subscription, new_trending_course_subscription, setNewCourseSubscription, setNewTrendingCourseSubscription } = useSubscriptionStore();
  const { user, setUser, clearUser } = useUserStore();
  const { student, setStudent, clearStudent } = useStudentStore();
  const { cart, setCart, clearCart } = useCartStore();
  const { Lecturer, setLecturer, clearLecturer } = useLecturerStore();
  const { showNotification, destroyNotifications } = useNotification();






  //Update Profile
  async function setUpMe(data: any, route?: boolean) {
    try {
      console.log('setUpMe - Full response data:', data.data);
      console.log('setUpMe - User data:', data.data.data?.user);
      console.log('setUpMe - User role:', data.data.data?.user?.role);

      setUser(data.data.data.user)
      setNewCourseSubscription(data.data.data.new_course_subscription)
      setNewTrendingCourseSubscription(data.data.data.new_trending_course_subscription)
      setCart(data.data.data.cart)

      if (data.data.data?.user.role === 5) {
        console.log('setUpMe - Setting student data:', data.data.data?.student);
        setStudent(data.data.data?.student)
      } else if (data.data.data?.user.role === 3) {
        console.log('setUpMe - Setting teacher data:', data.data.data?.teacher);
        setLecturer(data.data.data?.teacher)
      }
      // Destroy any existing notifications before showing success message
      destroyNotifications();
      if (route) {
      // Show success message based on user role before routing
      switch (data.data.data?.user.role) {
        case 1:
          // Admin user
          showNotification('success', 'Welcome Admin', 'Successfully signed in as Admin');
          setTimeout(() => {
            window.location.href = "/admin";
          }, 1500); // Delay routing to allow toast to be seen
          break;
        case 2:
          // Role 2 user
          showNotification('success', 'Welcome', 'Successfully signed in');
          setTimeout(() => {
            window.location.href = "/dashboard";
          }, 1500);
          break;
        case 3:
          // Lecturer user
          showNotification('success', 'Welcome Lecturer', 'Successfully signed in as Lecturer');
          setTimeout(() => {
            window.location.href = "/dashboard";
          }, 1500);
          break;
        case 4:
          // Role 4 user
          showNotification('success', 'Welcome', 'Successfully signed in');
          setTimeout(() => {
            window.location.href = "/dashboard";
          }, 1500);
          break;
        case 5:
          // Student user
          showNotification('success', 'Welcome Student', 'Successfully signed in as Student');
          setTimeout(() => {
            window.location.href = "/student/notice-board";
          }, 1500);
          break;
        default:
          showNotification('success', 'Welcome', 'Successfully signed in');
          setTimeout(() => {
            window.location.href = "/";
          }, 1500);
          break;
      }
    }


    } catch (error) {

    }
  }





  return {
    setUpMe

  }
}
