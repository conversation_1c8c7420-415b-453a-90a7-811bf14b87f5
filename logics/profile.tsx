import { useNotification } from "@/hooks/useNotifs";
import { useApi } from "@/hooks/useRequest"
import { Form } from "antd";
import { useState } from "react"
import { useRouter } from "next/navigation";

import Cookies from "js-cookie";
import { Spin } from 'antd';
import { useSubscriptionStore } from "@/store/subscriptionStore";

export const ProfileLogics = () => {
  const router = useRouter();
  const { request } = useApi();
  const { showNotification, destroyNotifications } = useNotification(); // Get notification function
  const [loading, setLoading] = useState(false);
  const [studentForm] = Form.useForm();
  const [studentUploadForm] = Form.useForm();
  const [studentLogoutForm] = Form.useForm();
  const [subscriptionForm] = Form.useForm();

  const { new_course_subscription, new_trending_course_subscription, setNewCourseSubscription, setNewTrendingCourseSubscription } = useSubscriptionStore();




  //Create User
  async function updateStudentProfile(userData: any, isUpdate?: boolean) {
    try {
      console.log('updating student profile', userData);
      showNotification('success', 'Updating Profile', 'Saving your student profile...', true, <Spin />);

      setLoading(true);
      const requestResponse = await request(isUpdate ? 'PUT' : "POST", "/student/",

        {
          ...userData,
        }, "multipart/form-data");
      console.log(requestResponse, 'mainData');
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
        studentUploadForm.resetFields(); // Clears form when component unmounts
        return requestResponse.data.data
      } else {
        showNotification('error', 'Error updating student profile', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }

  //Update Profile
  async function updateStudentProfileDetails(userData: any, student_id: string) {
    try {
      console.log('updating student profile', userData);
      showNotification('success', 'Updating Profile', 'Saving your student profile...', true, <Spin />);

      // setLoading(true);
      const requestResponse = await request('PUT', `/student/${student_id}`,

        {
          ...userData,
        }, "");
      console.log(requestResponse, 'mainData');
      // setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
        // studentForm.resetFields(); // Clears form when component unmounts
        return requestResponse.data.data
      } else {
        // showNotification('error', 'Error updating student profile', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }

  async function deleteStudentProfile(studentID: string, userID: string) {
    try {
      console.log('deleting student profile', studentID);
      showNotification('success', 'Deleting Profile', 'Deleting your student profile...', true, <Spin />);

      setLoading(true);
      const requestResponse = await request("DELETE", `/student/${studentID}`,

        {}, "multipart/form-data");

      console.log(requestResponse, 'mainData');
      setLoading(false);
     if (requestResponse && requestResponse?.status === 200) {
        const requestUserResponse = await request("PUT", `/authdelete_user?user_id=${userID}`,

          {}, "");
          destroyNotifications();
     
        if (requestUserResponse && requestUserResponse?.status === 200) {
          showNotification('success', 'Success', requestResponse.data.message);
      
          setTimeout(() => {
            Cookies.remove("access_token"); 
            window.location.href = "/auth/signin"; 
          },1000); 
     

          studentUploadForm.resetFields(); // Clears form when component unmounts
          return requestUserResponse.data.data
        }else{
          showNotification('error', 'Error deleting student profile', requestResponse.data.detail);

        }

      } else {
        showNotification('error', 'Error updating student profile', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }
  
  // Deactivate
  async function deactivateStudentProfile(studentID: string) {
    try {
      console.log('deactivating student profile', studentID);
      showNotification('success', 'Deactivating Profile', 'Deactivating your student profile...', true, <Spin />);

      setLoading(true);
      const requestResponse = await request("PUT", `/auth/user_deactivate`,

        {}, "multipart/form-data");
      console.log(requestResponse, 'mainData');
      setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
        studentUploadForm.resetFields(); // Clears form when component unmounts
        return requestResponse.data.data
      } else {
        showNotification('error', 'Error deactivating student profile', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }


  //Update Profile
  async function updateNewCourse(userData: any, subscription_id: string, isNew: boolean) {
    try {
      console.log('updating new course notification', userData);
      showNotification('success', 'Updating Preference', 'Subscribing to new course...', true, <Spin />);

      // setLoading(true);
      const requestResponse = await request(isNew ? 'POST' : 'PUT', isNew ? `/new_course_notifications/subscribe` : `/new_course_notifications/${subscription_id}`,

        {
          ...userData,
        }, "");
      console.log(requestResponse, 'mainData');
      // setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
        // studentForm.resetFields(); // Clears form when component unmounts
        setNewCourseSubscription(requestResponse.data.data)

        return requestResponse.data.data

      } else {
        setNewCourseSubscription({
          ...userData,
          is_active: new_course_subscription
        })
        // showNotification('error', 'Error updating student profile', requestResponse.data.detail);

      }




    } catch (error) {

    }
  }

  //Update Profile
  async function updateTrendingCourse(userData: any, subscription_id: string, isNew: boolean) {
    try {
      console.log('updating new course notification', userData);
      showNotification('success', 'Updating Preference', 'Subscribing to trending course...', true, <Spin />);

      // setLoading(true);
      const requestResponse = await request(isNew ? 'POST' : 'PUT', isNew ? `/new_trending_course_notifications/trending/subscribe` : `/new_trending_course_notifications/trending/${subscription_id}`,

        {
          ...userData,
        }, "");
      console.log(requestResponse, 'mainData');
      // setLoading(false);
      destroyNotifications();
      if (requestResponse && requestResponse?.status === 200) {
        showNotification('success', 'Success', requestResponse.data.message);
        setNewTrendingCourseSubscription(requestResponse.data.data)

        // studentForm.resetFields(); // Clears form when component unmounts
        return requestResponse.data.data
      } else {
        // showNotification('error', 'Error updating student profile', requestResponse.data.detail);
        setNewTrendingCourseSubscription({
          ...userData,
          is_active: new_trending_course_subscription
        })
      }




    } catch (error) {

    }
  }





  return {
    updateStudentProfile, loading, studentForm, updateStudentProfileDetails, studentUploadForm,
    studentLogoutForm, deleteStudentProfile, subscriptionForm, deactivateStudentProfile, updateNewCourse,
    updateTrendingCourse

  }
}
