@tailwind base;
@tailwind components;
@tailwind utilities;

/* Added Self-Hosting Fonts or Local Fonts to the Project - Montserrat */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Setting root variables for colors and fonts */
:root {
  --background: #FFFFFF;
  --foreground: #ededed;
  --primaryColor: #008080;
  --secondaryColor: #252B42;
  --buttonColor: #008080;
  --studentSideMenuColor: #FBFBFE;
  --sideMenuTextColor: #505050;
  --blackColor: #000000;
  --textColor: #505050;
  --cancelPriceColor: #EC9418;
  --font-primary: 'Montserrat', sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #FFFFFF;
    --foreground: #ededed;
    --primaryColor: #008080;
    --secondaryColor: #252B42;
    --buttonColor: #008080;
    --studentSideMenuColor: #FBFBFE;
    --sideMenuTextColor: #505050;
    --blackColor: #000000;
    --textColor: #505050;
    --cancelPriceColor: #EC9418;
  }
}

/* Global body styles */
body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-primary);
  height: 100dvh;
}

.input-placeholder::placeholder {
  color: white !important;
  opacity: 1; /* Ensures visibility */
}


/* Override Ant Design's default font family */
.ant-layout,
.ant-btn,
.ant-table,
.ant-pagination,
.ant-select,
.ant-input,
.ant-modal,
.ant-menu,
.ant-dropdown,
.ant-tabs,
.ant-form,
.ant-typography {
  font-family: var(--font-primary) !important;
}

/* Custom font sizes for headings */
h1 {
  font-size: 2.5rem; /* 40px */
  font-weight: 700; /* Bold */
  font-family: var(--font-primary);
}

h2 {
  font-size: 2rem; /* 32px */
  font-weight: 600; /* Semi-bold */
  font-family: var(--font-primary);
}

button {
  font-size: 1rem; /* 16px */
  font-weight: 500; /* Medium */
  font-family: var(--font-primary);
}

/* Custom styles for Ant Design Select component */
.ant-select-selector {
  border: 1px solid #d9d9d9;
  background: transparent !important;
  font-family: var(--font-primary);
}

.font-regular-title {
  font-size: 1.25rem; /* 20px */
  font-weight: 600; /* Semi-bold */
  font-family: var(--font-primary);
  line-height: 1.5;
  color: var(--textColor);
}

.font-regular-subtitle {
  font-size: 1rem; /* 16px */
  font-weight: 400; /* Regular */
  font-family: var(--font-primary);
  line-height: 1.5;
  color: var(--textColor);
}
.font-regular-text {
  font-size: 0.870rem; /* 14px */
  font-weight: 100; /* Lightest */
  font-family: var(--font-primary);
  line-height: 1.5;
  color: var(--textColor);
}
.font-regular-text-small {
  font-size: 0.75rem; /* 12px */
  font-weight: 400; /* Regular */
  font-family: var(--font-primary);
  line-height: 1.5;
  color: var(--textColor);
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-in-out forwards;
}


.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
/* Override Ant Design Button Hover Color */
.ant-btn-primary {
  background-color: #0d9488;
  border-color: #0d9488;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #0f766e;
  border-color: #0f766e;
}

.glass-card {
  background: rgba(5, 33, 72, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes marquee {
  0% { transform: translateX(0%); }
  100% { transform: translateX(-50%); }
}

.animate-marquee {
  animation: marquee 20s linear infinite;
}

@media (prefers-reduced-motion: reduce) {
  .animate-marquee {
    animation: none;
  }
}

/* Course form field styles */
.course-form-field {
  color: var(--blackColor) !important;
  font-weight: 600 !important;
}

.course-form-field::placeholder {
  color: rgba(0, 0, 0, 0.45) !important;
  font-weight: normal !important;
  opacity: 0.7 !important;
}

.course-name-placeholder::placeholder {
  color: rgba(107, 114, 128, 0.5) !important; /* gray-500 with opacity */
  font-weight: normal !important;
}

/* Form input styles */
::placeholder {
  color: black !important;
  opacity: 1 !important;
}

::-webkit-input-placeholder {
  color: black !important;
  opacity: 1 !important;
}

::-moz-placeholder {
  color: black !important;
  opacity: 1 !important;
}

:-ms-input-placeholder {
  color: black !important;
  opacity: 1 !important;
}

:-moz-placeholder {
  color: black !important;
  opacity: 1 !important;
}

.ant-select-selection-placeholder {
  color: black !important;
  opacity: 1 !important;
}

.ant-input-affix-wrapper input::placeholder {
  color: black !important;
  opacity: 1 !important;
}

/* Make input text black and bold */
.ant-input,
.ant-input-affix-wrapper input,
.ant-select-selection-item,
.ant-select-item,
.ant-select-dropdown,
.ant-select-item-option-content,
textarea.ant-input {
  color: black !important;
  font-weight: 500 !important;
}

/* Make placeholder text appear as if it's already filled in */
input::placeholder,
textarea::placeholder,
.ant-select-selection-placeholder {
  font-weight: 600 !important;
  color: #000000 !important;
  opacity: 1 !important;
}

/* Additional selectors to ensure all placeholders are black */
.ant-form input::placeholder,
.ant-form textarea::placeholder,
.ant-form-item input::placeholder,
.ant-form-item textarea::placeholder,
.ant-select-selector .ant-select-selection-placeholder {
  color: #000000 !important;
  font-weight: 600 !important;
  opacity: 1 !important;
}

/* Special case for the course name placeholder */
.course-name-placeholder input::placeholder {
  color: #6B7280 !important; /* Gray-500 color */
  font-weight: normal !important;
  opacity: 0.7 !important;
}

/* Select focus styles */
.ant-select-focused .ant-select-selector,
.ant-select-selector:focus,
.ant-select-open .ant-select-selector,
.ant-select-selector:active {
  border-color: #0D9488 !important;
}


/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 128, 128, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 128, 128, 0.5);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 128, 128, 0.7);
}


