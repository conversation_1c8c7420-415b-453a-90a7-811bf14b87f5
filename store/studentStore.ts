import { create } from "zustand";

interface Student {
    [x: string]: any;
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    role: number;
    school_id: string;
    program_id: string;
    level: string;
    verified: boolean;
    created_at: string;
    updated_at: string;
    // Profile image properties
    profile_image?: string;
    profile_image_path?: string;
    profile_image_path_server?: string;
    // Cover image properties
    cover_image?: string;
    cover_image_path?: string;
    cover_image_path_server?: string;
}

interface StudentState {
    student: Student | null;
    setStudent: (user: any) => void;
    clearStudent: () => void;
}

export const useStudentStore = create<StudentState>((set) => ({
    student: null,
    setStudent: (student) => set({ student }),
    clearStudent: () => set({ student: null }),
}));
