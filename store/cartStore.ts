import { Course } from "@/types";
import { create } from "zustand";

interface Cart {
    id: string;
    items: CartItem[];
    item_count: number;
    total_price: number;
    last_updated: string;
  }
  interface CartItem {
    id: string;
    cart_id: string;
    course_id: string;
    price: number;
    course: Course;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }  

interface CartState {
    cart: Cart | null;
    setCart: (cart: Cart) => void;
    clearCart: () => void;
}

export const useCartStore = create<CartState>((set) => ({
    cart: null,
    setCart: (cart) => set({ cart }),
    clearCart: () => set({ cart: null }),
}));
