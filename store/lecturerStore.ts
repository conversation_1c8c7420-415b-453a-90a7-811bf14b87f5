import { create } from "zustand";

interface Lecturer {
    [x: string]: string | boolean | number | undefined;
    // bio: string;
    specialization: string;
    qualification: string;
    profile_image?: string;
    cover_image?: string;
    profile_image_path?: string;
    // cover_image?: string;
    introduction?: string;
    name: string;
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    role: number;
    school_id: string;
    verified: boolean;
    created_at: string;
    updated_at: string;
}

interface LecturerState {
    Lecturer: Lecturer | null;
    setLecturer: (Lecturer: Lecturer) => void;
    clearLecturer: () => void;
}

export const useLecturerStore = create<LecturerState>((set) => ({
    Lecturer: null,
    setLecturer: (Lecturer) => set({ Lecturer }),
    clearLecturer: () => set({ Lecturer: null }),
}));
