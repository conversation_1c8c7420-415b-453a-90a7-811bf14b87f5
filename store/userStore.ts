import { create } from "zustand";

interface User {
    [x: string]: any;
    name: any;
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    role: number;
    school_id: string;
    verified: boolean;
    created_at: string;
    updated_at: string;
}

interface UserState {
    user: User | null;
    setUser: (user: User) => void;
    clearUser: () => void;
}

export const useUserStore = create<UserState>((set) => ({
    user: null,
    setUser: (user) => set({ user }),
    clearUser: () => set({ user: null }),
}));
