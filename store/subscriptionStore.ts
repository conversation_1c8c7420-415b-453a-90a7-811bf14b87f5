import { create } from "zustand";



interface SubscriptionState {
    new_course_subscription: any | null;
    new_trending_course_subscription: any | null;
    setNewCourseSubscription: (data: any) => void;
    setNewTrendingCourseSubscription: (data: any) => void;
    clearAllSubScriptions: () => void;
}

export const useSubscriptionStore = create<SubscriptionState>((set) => ({
    new_course_subscription: null,
    new_trending_course_subscription: null,
    setNewCourseSubscription: (new_course_subscription) => set({ new_course_subscription }),
    setNewTrendingCourseSubscription: (new_trending_course_subscription) => set({ new_trending_course_subscription }),
    clearAllSubScriptions: () => set({ new_course_subscription: null, new_trending_course_subscription: null }),
}));
