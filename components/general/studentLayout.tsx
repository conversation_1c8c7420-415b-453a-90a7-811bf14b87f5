'use client'
import React, { useEffect } from 'react'
import LayoutWrapper from './student'
import { ProfileLogics } from '@/logics/profile'
import { useFetch } from '@/hooks/useFetch'
import { useAutoLogout } from '@/hooks/useAutoLogout'
import { Loader } from './loader'
import { useUserStore } from '@/store/userStore'
import NotFoundPage from './not-found'
import { useStudentStore } from '@/store/studentStore'
import { Modal, Progress } from 'antd'
import Cookies from "js-cookie"; // Import cookies library
import { useSubscriptionStore } from '@/store/subscriptionStore'
import {  useCartStore } from '@/store/cartStore'
import { useStudentProfile } from '@/logics/useStudentProfile'


export const StudentLayout = ({ children }: { children: React.ReactNode }) => {
  const { data, loading, error } = useFetch('/protected/me')
  const { user, setUser, clearUser } = useUserStore();
  const { student, setStudent, clearStudent } = useStudentStore();
  const { cart, setCart, clearCart } = useCartStore();
  const { setNewCourseSubscription, setNewTrendingCourseSubscription } = useSubscriptionStore();
  const { showWarning, resetTimer, countdown } = useAutoLogout({
    // timeoutDuration: 900000, // 15 minutes
    // warningTime: 300000      // 5 minutes
  });


    const {
      fetchStudentProfile,
    } = useStudentProfile();
  // Load student profile on component mount
    useEffect(() => {
      fetchStudentProfile();
    }, []);

  // useEffect(()=>{},[])
  useEffect(() => {

    if (data) {
      setUser(data.data.user)
      console.log('new course',data.data.new_course_subscription)
      setNewCourseSubscription(data.data.new_course_subscription)
      setNewTrendingCourseSubscription(data.data.new_trending_course_subscription)
      setCart(data.data.cart)
      setCart(data.data.cart)
      if (data?.data?.user.role === 5) {
        setStudent(data?.data?.student)
      }else{
        // setLecturer(data?.data?.Lecturer)
      }
      
      
    }
  }, [data])
  if (loading) return <Loader />
  if (error) return <NotFoundPage code={error.status} value={error.data.detail} />


  return (
    <>
      {showWarning && (
        <Modal
          className='flex flex-col mt-20 h-full w-full'
          title="Inactivity Detected"
          open={showWarning}
          onCancel={() => {
            resetTimer()
          }}
          footer={null} // Hide default footer buttons
        >
          <div className='flex flex-col items-center'>
          <div className='mb-4'>You will be logged out in: {countdown} seconds</div>
          <Progress type="circle" percent={Math.ceil((countdown/60)*100)} format={(percent) => `${countdown}`} />
          </div>
        </Modal>
      )}
      {

        //  data && data.data.role === 5 &&
        <LayoutWrapper >{children}</LayoutWrapper>}
    </>
  )
}

