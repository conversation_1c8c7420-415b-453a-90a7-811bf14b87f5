'use client';

import React, { useEffect, useState } from 'react';
import { Ava<PERSON>, Badge, Button, ConfigProvider, Layout, Menu, theme } from 'antd';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import Cookies from "js-cookie";
import { useUserStore } from '@/store/userStore';
import TopBar from './dashboard/sidebar/topbar';
import StudentSideBar from './dashboard/sidebar/student-sidebar';
import { getStudentMenuItems, studentBottomsItems } from '@/app/(student)/student/_data/student-layout-data';
import { useNotificationsLogic } from '@/app/(student)/student/notifications/_logics/notifications-logic';


const { Header, Sider } = Layout;

const LayoutWrapper = ({ children }: { children: React.ReactNode }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user } = useUserStore();
  const [isMobile, setIsMobile] = useState(false);
  const {
    fetchNotifications,
    unreadCount,
  } = useNotificationsLogic();


  const {
    token: { colorBgContainer },
  } = theme.useToken();
  const router = useRouter();
  const pathname = usePathname();

  const [activeKey, setActiveKey] = useState(pathname);


  // Fetch notifications on component mount
  useEffect(() => {
    fetchNotifications(1, 5, false); 
  }, []);

  // On page load, check localStorage for saved path
  useEffect(() => {
    const savedPath = localStorage.getItem("activeMenuPath");
    setActiveKey(pathname);
  }, [pathname]);

  const handleBellClick = () => {
    router.push('/student/notifications');
  };

 
  // On page load, check localStorage for saved path, fall back to URL if none exists
  useEffect(() => {
    console.log('pathname chaning');
    const savedPath = localStorage.getItem("activeMenuPath");
    // if (savedPath) {
    //   setActiveKey(savedPath); // Use the saved path from localStorage
    // } else {
    setActiveKey(pathname); // Fall back to URL if no path is saved

    // }
  }, [pathname]);
// Check if screen is mobile on component mount and window resize
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Check initially
    checkIfMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  return (
    <div className=" h-screen flex flex-col overflow-hidden">
      <TopBar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />

      <Layout className=" w-full flex flex-1 flex-row h-full ">

        <StudentSideBar

          menuItems={getStudentMenuItems()}
          bottomItems={studentBottomsItems}
          isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} isMobile={isMobile} />

        <Layout className={` w-full  h-full ${!isMobile? 'max-w-[100%]': ''} `}>

          {children}
        </Layout>


      </Layout>
    </div>
  );
};

export default LayoutWrapper;