const mentorsData = [
  {
    name: "<PERSON>",
    specialty: "Programming Fundamentals",
    image: "/student2.jpg",
    description: "<PERSON> is a seasoned software engineer with over 10 years of experience in the tech industry. He specializes in teaching complex programming concepts, helping students build a solid foundation, and improving their problem-solving.",
    rating: 4.8
  },
  {
    name: "<PERSON>",
    specialty: "Medical Nursing",
    image: "/student2.jpg",
    description: "<PERSON>, a practicing nurse with extensive clinical experience, offers a comprehensive approach to nursing education to cover patient care, emergency response, and healthcare ethics, empowering students to excel in their medical future.",
    rating: 4.7
  },
  {
    name: "<PERSON>",
    specialty: "Law and Humanitarian Studies",
    image: "/student2.jpg",
    description: "<PERSON> is a legal scholar passionate about human rights and international law. Her courses combine theoretical foundations with revealing insights into humanitarian frameworks and preparing students for impactful roles in the legal field.",
    rating: 4.9
  },
  {
    name: "<PERSON>",
    specialty: "Python Developer",
    image: "/student2.jpg",
    description: "<PERSON> is a Python expert who specializes in web development and data analysis. Her student-centered approach equips them with the skills to write efficient Python code, create robust applications, and dive into data science projects.",
    rating: 4.9
  },
  {
    name: "<PERSON>",
    specialty: "Python Developer",
    image: "/student2.jpg",
    description: "<PERSON> is a Python expert who specializes in web development and data analysis. Her student-centered approach equips them with the skills to write efficient Python code, create robust applications, and dive into data science projects.",
    rating: 4.9
  },
  {
    name: "Sophia Clarke",
    specialty: "Python Developer",
    image: "/student2.jpg",
    description: "Sophia is a Python expert who specializes in web development and data analysis. Her student-centered approach equips them with the skills to write efficient Python code, create robust applications, and dive into data science projects.",
    rating: 4.9
  }
];
  
export default mentorsData;