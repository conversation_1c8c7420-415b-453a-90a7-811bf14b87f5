import { useEffect, useState } from 'react';
import { useApi } from '@/hooks/useRequest';

interface Course {
  id: number;
  name: string;
  description: string;
  cover_image: string;
  level: string;
  base_price: number;
  credits: number;
  cohort_duration_weeks: number;
  max_students_per_cohort: number;
}

interface PaginationParams {
  page: number;
  pageSize: number;
  search?: string;
  filters?: Record<string, any>;
}

export const useCourses = (pagination: PaginationParams) => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { request } = useApi();
  const { page, pageSize, search, filters } = pagination;

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();
    
    const fetchCourses = async () => {
      try {
        setLoading(true);
        
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          limit: pageSize.toString(),
          ...(search && { q: search }),
          ...filters
        });

        const response = await request('GET', `/unprotected/courses?${params}`, {
          signal: controller.signal
        });
        
        if (!isMounted) return;
        
        if (response?.status === 200 && response.data?.status === 'success') {
          const { data, pagination: paginationData } = response.data;
          
          if (data && Array.isArray(data.courses)) {
            const validCourses = data.courses.map((course: any) => ({
              id: course.id,
              name: course.name || 'Untitled Course',
              description: course.description || 'No description available',
              cover_image: course.cover_image || '/course-frame.png',
              level: course.level || 'Beginner',
              base_price: typeof course.base_price === 'number' ? course.base_price : 0,
              credits: typeof course.credits === 'number' ? course.credits : 0,
              cohort_duration_weeks: course.cohort_duration_weeks || 8,
              max_students_per_cohort: course.max_students_per_cohort || 20
            }));

            setCourses(validCourses);
            setTotal(paginationData?.total || validCourses.length);
            setError(null);
          } else {
            throw new Error('Invalid courses data format');
          }
        } else {
          throw new Error(response?.data?.message || 'Failed to fetch courses');
        }
      } catch (error: unknown) {
        if (isMounted) {
          if (error instanceof Error) {
            if (error.name !== 'AbortError') {
              setError(error.message);
            }
          } else {
            setError('An unknown error occurred');
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchCourses();

    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [page, pageSize, search, JSON.stringify(filters)]);

  return { courses, loading, error, total };
};
