'use client';
import { SetStateAction, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StarFilled, EllipsisOutlined } from '@ant-design/icons';
import Image from 'next/image';
import { testimonialData } from '../dummy-data/testimonialData';
import "../../../styles/globals.css";

interface TestimonialData {
  id: number;
  text: string;
  rating: number;
  name: string;
  role: string;
  image: string; 
}

const TestimonialCard = ({ testimonial, index }: { testimonial: TestimonialData; index: number }) => {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarFilled key={i} className="text-yellow-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<StarFilled key={i} className="text-yellow-400 opacity-60" />);
      } else {
        stars.push(<StarFilled key={i} className="text-gray-200" />);
      }
    }

    return stars;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="bg-white p-6 rounded-lg shadow-sm mx-auto w-full"
    >
      <div className="flex mb-3">{renderStars(testimonial.rating)}</div>
      <p className="text-gray-600 text-sm mb-4">{testimonial.text}</p>
      <div className="flex items-center">
        <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
          <Image
            src={testimonial.image}
            alt={testimonial.name}
            fill
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div>
          <p className="font-medium text-teal-700">{testimonial.name}</p>
          <p className="text-xs text-gray-500">{testimonial.role}</p>
        </div>
      </div>
    </motion.div>
  );
};

export default function TestimonialsSection() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [slidesPerView, setSlidesPerView] = useState(3);
  
  // Set slides per view based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setSlidesPerView(1);
      } else if (window.innerWidth < 1024) {
        setSlidesPerView(2);
      } else {
        setSlidesPerView(3);
      }
    };
    
    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const totalSlides = Math.ceil(testimonialData.length / slidesPerView);

  // Automatic carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [totalSlides]);

  const visibleTestimonials = testimonialData.slice(
    currentSlide * slidesPerView,
    currentSlide * slidesPerView + slidesPerView
  );

  const handleSlideChange = (index: SetStateAction<number>) => {
    setCurrentSlide(index);
  };

  return (
    <section className="py-8 sm:py-12 md:py-16 px-4 bg-white">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="mb-8 sm:mb-12 text-left sm:text-center md:text-left md:pl-4 lg:pl-0">
          <p className="text-sm uppercase tracking-wider text-text-teal-700 font-medium mb-2">
            LearnKonnect Testimonials
          </p>
          <h2 className="text-2xl sm:text-3xl font-bold text-teal-700 mb-2">What our clients say</h2>
          <p className="text-gray-600 max-w-2xl opacity-0 animate-fade-in">
            See what our clients have to say about our services and solutions. Their feedback is what
            drives us to excel.
          </p>
        </div>

        {/* Testimonial Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8">
          <AnimatePresence mode="popLayout">
            {visibleTestimonials.map((testimonial: TestimonialData, index: number) => (
              <TestimonialCard key={testimonial.id} testimonial={testimonial} index={index} />
            ))}
          </AnimatePresence>
        </div>

        {/* Dot Navigation */}
        <div className="flex justify-center items-center space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <motion.div
              key={index}
              onClick={() => handleSlideChange(index)}
              className={`h-2 w-2 rounded-full cursor-pointer transition-all duration-300 ${
                currentSlide === index ? 'bg-text-teal-700 w-4' : 'bg-gray-300'
              }`}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </div>
      </div>
    </section>
  );
}