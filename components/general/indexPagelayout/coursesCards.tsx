'use client';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowRightOutlined, 
  HeartOutlined,
  ShoppingCartOutlined,
  EyeOutlined,
  StarFilled,
  UserOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons';
import { Pagination } from 'antd';
import Image from 'next/image';
import Link from 'next/link';
import { useCourses } from '../dummy-data/logic/landingpage_courses_api';

interface Course {
  description: string;
  credits: number;
  level: string;
  base_price: number;
  name: string;
  max_students_per_cohort: number;
  cohort_duration_weeks: number;
  cover_image: string;
}

interface CourseCardProps {
  course: Course;
  index: number;
  navAnimation: { left: boolean; right: boolean };
}

const CourseCard = ({ course, index, navAnimation }: CourseCardProps) => {
  // Format the price to 2 decimal places and include currency
  const formattedPrice = new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: 'GHS'
  }).format(course.base_price);

  return (
    <motion.div 
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ 
        y: -8, 
        transition: { duration: 0.2 },
        boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)"
      }}
      className="bg-white rounded-lg overflow-hidden shadow-md h-full flex flex-col relative"
    >
      {/* Course Image */}
      <div className="relative w-full overflow-hidden" style={{height: '200px'}}>
        <img 
          src={!course.cover_image || course.cover_image.startsWith('gs://') 
            ? '/course-frame.png'
            : course.cover_image}
          alt={course.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/course-frame.png';
          }}
        />
      </div>

      {/* Course Content */}
      <div className="p-4 flex-grow flex flex-col">
        {/* Level, Rating and Credits */}
        <div className="flex justify-between items-center mb-2">
          <p className="text-xs font-medium text-teal-700">{course.level}</p>
          <div className="flex items-center gap-1">
            <div className="flex items-center mr-1">
              {[...Array(5)].map((_, i) => (
                <StarFilled 
                  key={i} 
                  className={`text-xs ${i < Math.min(course.credits || 0, 5) ? 'text-yellow-400' : 'text-gray-300'}`} 
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">({(typeof course.credits === 'number' ? course.credits : parseFloat(course.credits)).toFixed(1)})</span>
          </div>
        </div>

        {/* Title */}
        <h3 className="font-semibold text-lg text-teal-700 mb-3">{course.name}</h3>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-4 flex-grow line-clamp-3">{course.description}</p>

        {/* Duration and Capacity */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <span>{course.cohort_duration_weeks} weeks</span>
          <span>Max {course.max_students_per_cohort} students</span>
        </div>

        {/* Price and Enroll now */}
        <div className="flex justify-between items-center">
          <div>
            <span className="text-base font-medium text-teal-700">{formattedPrice}</span>
          </div>
          <Link href="/auth/signin">
            <motion.button 
              whileHover={{ scale: 1.05, backgroundColor: "#0D9488", color: "white" }}
              whileTap={{ scale: 0.95 }}
              className="text-teal-700 bg-white border border-teal-700 rounded-full px-3 py-1 text-sm flex items-center transition-colors duration-300"
            >
              Enroll now <ArrowRightOutlined className="ml-1" />
            </motion.button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default function CoursesSection() {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(9); // Show 9 courses per page
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    level: '',
    minPrice: 0,
    maxPrice: 1000
  });

  const { courses, loading, error, total } = useCourses({
    page: currentPage,
    pageSize,
    search: searchQuery,
    filters
  });

  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    if (size !== pageSize) {
      setPageSize(size);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
  };

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <section className="bg-white py-12 px-4 sm:px-6 lg:px-8 relative" style={{marginTop: '8em'}}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((n) => (
              <div key={n} className="bg-white rounded-lg overflow-hidden shadow-md h-[400px] animate-pulse">
                <div className="h-[200px] bg-gray-200" />
                <div className="p-4 space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-6 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-full" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="bg-white py-12 px-4 sm:px-6 lg:px-8 relative" style={{marginTop: '8em'}}>
        <div className="max-w-7xl mx-auto text-center text-red-600">
          Error loading courses: {error}
        </div>
      </section>
    );
  }

  return (
    <section className="bg-white py-12 px-4 sm:px-6 lg:px-8 relative" style={{marginTop: '8em'}}>
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="mb-10">
          <div className="text-sm uppercase tracking-wider text-text-teal-700 font-medium mb-2">
            LearnKonnect courses
          </div>
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-bold text-teal-700 mb-4"
          >
            Our Courses
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 max-w-3xl"
          >
            Our courses are carefully designed to combine in-depth knowledge with real-world applications, ensuring students gain practical skills while developing a theoretical understanding. Learn at your own pace with expert guidance every step of the way.
          </motion.p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search courses..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
              <button 
                type="submit" 
                className="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
              >
                Search
              </button>
            </div>
          </form>
          
          <div className="flex flex-wrap gap-4 justify-center">
            <select 
              value={filters.level}
              onChange={(e) => handleFilterChange({ level: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg"
            >
              <option value="">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
            
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Price:</span>
              <input 
                type="number" 
                placeholder="Min" 
                value={filters.minPrice}
                onChange={(e) => handleFilterChange({ minPrice: Number(e.target.value) })}
                className="w-24 px-3 py-2 border border-gray-300 rounded-lg"
              />
              <span>-</span>
              <input 
                type="number" 
                placeholder="Max" 
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange({ maxPrice: Number(e.target.value) })}
                className="w-24 px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>
          </div>
        </div>

        {/* Course Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {courses.map((course, index) => (
            <CourseCard 
              key={`course-${course.id}`} 
              course={course} 
              index={index} 
              navAnimation={{ left: false, right: false }} 
            />
          ))}
        </div>

        {/* Pagination */}
        {total > 0 && (
          <div className="flex justify-center mt-8">
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={total}
              onChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `Total ${total} courses`}
              pageSizeOptions={['9', '18', '27', '36']}
              className="pagination-custom"
            />
          </div>
        )}

        {/* Empty State */}
        {!loading && courses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No courses found. Try adjusting your search or filters.</p>
          </div>
        )}
      </div>
    </section>
  );
}