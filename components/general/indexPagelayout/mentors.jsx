import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LeftOutlined, RightOutlined, StarFilled } from '@ant-design/icons';
import mentorsData from '../dummy-data/mentorsData';

const MentorCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [itemsPerSlide, setItemsPerSlide] = useState(4);
  
  // Update items per slide based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setItemsPerSlide(1);
      } else if (window.innerWidth < 1024) {
        setItemsPerSlide(2);
      } else {
        setItemsPerSlide(4);
      }
    };
    
    handleResize(); // Set initial value
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Auto rotate the carousel
  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000); 
    
    return () => clearInterval(interval);
  }, [currentIndex, itemsPerSlide]);

  const handleNext = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => 
      prevIndex >= mentorsData.length - itemsPerSlide ? 0 : prevIndex + itemsPerSlide
    );
  };

  const handleDotClick = (index) => {
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
  };

  const variants = {
    enter: (direction) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    }),
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-5 py-8 font-sans">
      <div className="text-center mb-8">
        <h3 className="text-teal-700 mb-2 font-medium text-left sm:text-center md:text-left md:ml-4 lg:ml-0">LearnKonnect Mentors</h3>
        <h2 className="text-gray-800 text-2xl sm:text-3xl mb-4 font-semibold text-left sm:text-center md:text-left md:ml-4 lg:ml-0" style={{color: 'var(--primaryColor)'}}>Our Popular Mentors</h2>
        <p className="text-gray-600 text-left sm:text-center md:text-left md:ml-4 lg:ml-0 leading-relaxed max-w-xl md:max-w-3xl">
          Our expert teachers are passionate educators and industry professionals 
          dedicated to simplifying complex topics and inspiring learners. With their 
          expertise, they ensure every student gains a deeper understanding and 
          practical knowledge to excel.
        </p>
      </div>

      <div className="relative flex items-center mb-3">
        <div className="w-full overflow-hidden">
          <AnimatePresence initial={false} custom={direction} mode="popLayout">
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
              className="flex flex-wrap sm:flex-nowrap gap-4 py-3"
            >
              {(mentorsData.slice(currentIndex, currentIndex + itemsPerSlide).length < itemsPerSlide 
                ? [...mentorsData.slice(currentIndex), ...mentorsData.slice(0, itemsPerSlide - mentorsData.slice(currentIndex).length)]
                : mentorsData.slice(currentIndex, currentIndex + itemsPerSlide)
              ).map((mentor, index) => (
                <div 
                  key={index} 
                  className="w-full sm:w-1/2 lg:w-1/4 flex-grow bg-gray-50 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md"
                >
                  {/* Mentor Image */}
                  <div className="h-48 overflow-hidden">
                    <img 
                      alt={mentor.name} 
                      src={mentor.image} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Mentor Content */}
                  <div className="p-4">
                    <h3 className="text-gray-800 text-lg font-semibold">{mentor.name}</h3>
                    <p className="text-teal-700 text-sm font-medium">{mentor.specialty}</p>
                    <p className="text-gray-600 text-xs mt-2 leading-relaxed line-clamp-3">{mentor.description}</p>
                    
                    {/* Rating Badge */}
                    <div className="mt-3">
                      <div className="inline-flex items-center justify-center bg-teal-700 text-white rounded-full px-2 py-1">
                        <StarFilled className="text-xs mr-1" />
                        <span className="text-xs font-semibold">{mentor.rating}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      <div className="flex justify-center gap-2 mt-4">
        {Array(Math.ceil(mentorsData.length / itemsPerSlide)).fill(0).map((_, index) => (
          <button
            key={index}
            className={`w-2.5 h-2.5 rounded-full ${Math.floor(currentIndex / itemsPerSlide) === index ? 'bg-teal-700 scale-110' : 'bg-gray-300'} transition-all duration-200`}
            onClick={() => handleDotClick(index * itemsPerSlide)}
          />
        ))}
      </div>
    </div>
  );
};

export default MentorCarousel;
