'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TwitterOutlined, FacebookOutlined, InstagramOutlined } from '@ant-design/icons';
import { div } from 'framer-motion/client';

export default function Footer() {
  const [showCookieBanner, setShowCookieBanner] = useState(false);

  useEffect(() => {
    // Check if the user has already accepted cookies
    const hasAcceptedCookies = localStorage.getItem('cookiesAccepted');
    if (!hasAcceptedCookies) {
      setShowCookieBanner(true);
    }
  }, []);

  const handleAcceptCookies = () => {
    localStorage.setItem('cookiesAccepted', 'true');
    setShowCookieBanner(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* New Section Above Footer */}
      <div className="relative" style={{ marginTop: "10em" }}>
        {/* Content Div */}
        <div className="bg-teal-600 text-white py-8 w-4/5 md:w-2/5 mx-auto rounded-3xl relative overflow-hidden">
          {/* Content */}
          <div className="px-6 md:px-12 lg:px-16 relative z-10">
            <p className="text-xl md:text-2xl font-bold mb-4">Get Started Today</p>
            <p className="text-lg mb-4">Join Thousands Of Users And Learn</p>
            <button className="bg-white text-teal-700 px-5 py-2 rounded-lg font-semibold hover:bg-gray-200 transition">
              Get Started
            </button>
          </div>

          {/* Image - Fixed container with preserveAspectRatio */}
          <div className="hidden md:block" style={{
            position: 'absolute',
            right: '-5%',
            bottom: '-15%',
            width: '75%',  /* Increased width */
            height: '140%' /* Increased height */
          }}>
            <svg width="100%" height="100%" viewBox="0 0 100 120" preserveAspectRatio="xMidYMid meet">
              <image href="/footer-image.png" width="135" height="130" x="0" y="0" />
            </svg>
          </div>
        </div>
      </div>
      <br /><br /><br />

      {/* Footer Section */}
      <footer className="bg-teal-100 text-white py-28 relative overflow-hidden">
        <motion.div className="absolute inset-0 bg-teal-700 opacity-50" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 1 }} />
        <div className="container mx-auto px-6 md:px-12 lg:px-20 relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-start gap-10 py-8 border-b border-white">
            <div className="flex flex-col items-start md:w-1/3">
              <div className="text-3xl font-bold">*LearnKonnect*</div>
              <div className="flex items-center gap-4 mt-36">
                <a href="https://x.com/BuildBridgeApp" target="_blank" rel="noopener noreferrer">
                  <TwitterOutlined className="text-2xl cursor-pointer hover:text-gray-300 transition" />
                </a>
                <a href="https://www.youtube.com/@BuildBridgeApp" target="_blank" rel="noopener noreferrer">
                  <FacebookOutlined className="text-2xl cursor-pointer hover:text-gray-300 transition" />
                </a>
                <a href="https://www.instagram.com/BuildBridgeApp/#" target="_blank" rel="noopener noreferrer">
                  <InstagramOutlined className="text-2xl cursor-pointer hover:text-gray-300 transition" />
                </a>

              </div>
            </div>

            <div className="w-full md:w-2/3 grid grid-cols-2 md:grid-cols-3 gap-6 text-sm text-white">
              <div>
                <h3 className="font-semibold text-lg">Navigation</h3>
                <ul className="mt-2 space-y-2 opacity-90 cursor-pointer">
                  <li><a href="#home">Home</a></li>
                  <li><a href="#features">Features</a></li>
                  <li><a href="#question-bank">Question Bank</a></li>
                  <li><a href="#blog">Blog</a></li>
                  <li><a href="#career">Career</a></li>
                  <li><a href="#about">About Us</a></li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-lg">Features</h3>
                <ul className="mt-2 space-y-2 opacity-90 cursor-pointer">
                  <li><a href="#virtual-lesson">Virtual Lesson</a></li>
                  <li><a href="#certificate-verification">Certificate Verification</a></li>
                  <li><a href="#live-chat">Live Chat</a></li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-lg">Legal</h3>
                <ul className="mt-2 space-y-2 cursor-pointer">
                  <li><a href="#terms">Terms of Service</a></li>
                  <li><a href="#privacy">Privacy Policy</a></li>
                  <li><a href="#license">License & Regulation</a></li>
                </ul>
              </div>
            </div>
          </div>

          {/* Newsletter Section */}
          <div className="py-6 border-b border-white flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left">
              <p className="text-lg font-semibold">Join Our Newsletter</p>
              <p className="text-sm opacity-90">Keep up with everything LearnKonnect</p>
            </div>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 mt-4 md:mt-0">
              <input
                type="email"
                placeholder="Enter Your Email"
                className="p-4 rounded-full w-full lg:w-2/3 text-white bg-teal-700 border border-white focus:outline-none focus:border-white"
              />
              <button
                className="bg-teal-700 text-white px-5 py-2 rounded-full font-semibold hover:bg-teal-700 transition border border-white"
              >
                Subscribe
              </button>
            </div>
          </div>

          {/* Bottom Links */}
          <div className="mt-6 flex flex-col sm:flex-row justify-between text-xs sm:text-sm opacity-70 text-white">
            <p className="text-center sm:text-left mb-2 sm:mb-0">Terms of Service | Policy Service | Cookie Policy</p>
            <p className="text-center sm:text-right">&copy; 2025 LearnKonnect. All Rights Reserved.</p>
          </div>
        </div>
      </footer>

      {/* Cookie Consent Banner */}
      <AnimatePresence>
        {showCookieBanner && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.5 }}
            className="fixed bottom-0 left-0 right-0 bg-teal-700 text-white p-4 flex flex-col md:flex-row items-center justify-between gap-4 z-50"
          >
            <p className="text-center md:text-left">
              We use cookies to ensure you get the best experience on our website.
              <a href="#cookie-policy" className="underline ml-1">Learn more</a>
            </p>
            <button
              onClick={handleAcceptCookies}
              className="bg-white text-teal-700 px-4 py-2 rounded-full font-semibold hover:bg-gray-200 transition"
            >
              Accept Cookies
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}