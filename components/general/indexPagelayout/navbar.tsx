'use client';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { CloseOutlined, SearchOutlined, ExclamationCircleOutlined, MenuOutlined } from '@ant-design/icons';
import Image from 'next/image';

export default function Navbar() {
  const pathname = usePathname();
  const [showBanner, setShowBanner] = useState(true);
  const [showSearch, setShowSearch] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);

  const menuItems = [
    { name: 'Home', href: '/', isActive: pathname === '/' },
    { name: 'About us', href: '/aboutPage', isActive: pathname === '/aboutPage' },
    { name: 'Product', href: '/product', isActive: pathname === '/product' },
    { name: 'Community', href: '/community', isActive: pathname === '/community' },
    { name: 'Certificate verification', href: '/cert-verification', isActive: pathname === '/cert-verification' },
    { name: 'Contact', href: '/contactInformation', isActive: pathname === '/contactInformation' },
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    if (!isMobileMenuOpen) {
      setShowSearch(false);
    }
  };

  const handleSearchToggle = () => {
    setShowSearch(!showSearch);
    setSearchFocused(!showSearch);
  };

  return (
    <>
      {showBanner && (
        <div className="w-full bg-white overflow-x-hidden">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-[#EDF3FE] shadow-sm relative max-w-3xl mx-auto overflow-hidden"
          >
            <div className="absolute top-0 bottom-0 left-0 w-1 bg-[#008080]" />
            <div className="px-4 sm:px-6 lg:px-5 py-3">
              <div className="flex justify-between items-center gap-2">
                <span className="text-sm text-[#505050] font-medium flex items-center">
                  <ExclamationCircleOutlined className="text-[#000000] mr-3 h-12" />
                  LearnMingo Writing Coach by LearnKonnect is coming soon! Mentors - join the popLayoutlist for free access.
                </span>
                <button
                  onClick={() => setShowBanner(false)}
                  className="text-[#000000] hover:text-[#006666] transition-all duration-300 transform hover:scale-110 flex-shrink-0"
                  aria-label="Close banner"
                >
                  <CloseOutlined />
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      <motion.nav
        className={`transition-all duration-300 sticky top-0 z-40 ${scrolled ? 'bg-[#008080]' : 'bg-white'} w-full`}
        animate={{
          height: scrolled ? "auto" : "auto",
          padding: scrolled ? "0.5rem 0" : "1rem 0",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row justify-between items-center">
            {/* Logo and Mobile Menu Button */}
            <div className="flex justify-between w-full lg:w-auto items-center">
              <motion.div
                className="flex items-center flex-shrink-0"
                whileHover={{ scale: 1.02 }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <Image
                  src="/logo.png"
                  alt="LearnKonnet Logo"
                  width={48}
                  height={48}
                  className="mr-2"
                />
                <Link href="/">
                  <h1 className={`text-2xl font-bold ${scrolled ? 'text-white' : 'text-[#008080]'} lowercase tracking-tight font-montserrat`}>
                    LearnKonnect
                  </h1>
                </Link>
              </motion.div>

              <motion.button
                onClick={toggleMobileMenu}
                className={`lg:hidden ${scrolled ? 'text-white hover:text-gray-200' : 'text-[#505050] hover:text-[#008080]'} transition-colors p-2 rounded-md ${scrolled ? 'hover:bg-[#006666]' : 'hover:bg-gray-100'}`}
                whileTap={{ scale: 0.95 }}
                aria-label="Toggle mobile menu"
                aria-expanded={isMobileMenuOpen}
              >
                <MenuOutlined className="text-2xl" />
              </motion.button>
            </div>

            {/* Desktop Menu Items */}
            <div className="hidden lg:flex items-center space-x-8">
              {menuItems.map((item) => (
                <motion.div
                  key={item.name}
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Link
                    href={item.href}
                    className={`${
                      scrolled ? 'text-white hover:text-gray-200' : 'text-[#505050] hover:text-[#008080]'
                    } ${
                      item.isActive ? 'font-bold underline' : ''
                    } transition-colors text-sm font-medium font-montserrat relative`}
                  >
                    {item.name}
                    <motion.span
                      className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#008080]"
                      whileHover={{ width: '100%' }}
                      transition={{ duration: 0.3 }}
                    />
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Search and Get Started Button */}
            <div className="hidden lg:flex items-center space-x-6 relative">
              <AnimatePresence>
                {showSearch && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 2, width: "110px" }}
                    exit={{ opacity: 0, width: 0 }}
                    transition={{ duration: 0.3 }}
                    className="absolute right-full top-1/2 -translate-y-1/2 ml-4"
                  >
                    <input
                      type="text"
                      placeholder="Search..."
                      className={`w-full rounded-full px-3 py-1.5 text-sm focus:outline-none focus:ring-2 ${
                        scrolled 
                          ? 'bg-white text-[#008080] focus:ring-white border-white' 
                          : 'bg-gray-100 text-[#505050] focus:ring-[#008080] border-[#008080]'
                      } border`}
                      autoFocus
                    />
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* <motion.button
                onClick={handleSearchToggle}
                className={`${
                  scrolled ? 'text-white hover:text-gray-200' : 'text-[#505050] hover:text-[#008080]'
                } transition-colors z-10`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {showSearch ? <CloseOutlined className="text-xl" /> : <SearchOutlined className="text-xl" />}
              </motion.button> */}
              
              <Link href="/auth/signin">
                <motion.button
                  whileHover={{ scale: 1.02, backgroundColor: scrolled ? '#ffffff' : '#006666', color: scrolled ? '#008080' : '#ffffff' }}
                  whileTap={{ scale: 0.98 }}
                  className={`${
                    scrolled ? 'bg-white text-[#008080] border border-white' : 'bg-[#008080] text-white'
                  } px-6 py-2 rounded-full text-sm font-bold transition-all duration-300 font-montserrat`}
                >
                  GET STARTED
                </motion.button>
              </Link>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className={`lg:hidden ${scrolled ? 'bg-[#008080]' : 'bg-white'} shadow-lg overflow-hidden w-full`}
            >
              <div className="px-4 py-4">
                <ul className="flex flex-col space-y-3 mb-6">
                  {menuItems.map((item) => (
                    <motion.li
                      key={item.name}
                      className="relative pb-2"
                      whileHover={{ x: 5 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                    >
                      <Link
                        href={item.href}
                        className={`${scrolled ? 'text-white hover:text-gray-200' : 'text-[#505050] hover:text-[#008080]'} ${
                          item.isActive ? 'font-bold underline' : ''
                        } transition-colors text-sm font-medium font-montserrat block py-1 relative`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.name}
                        <motion.span
                          className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#008080]"
                          whileHover={{ width: '100%' }}
                          transition={{ duration: 0.3 }}
                        />
                      </Link>
                    </motion.li>
                  ))}
                </ul>

                <div className="space-y-4 w-full">
                  <div className="relative w-full">
                    <input
                      placeholder="Search..."
                      className="w-full rounded-full border border-[#008080] px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#008080] text-black"
                    />
                    <button
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#008080]"
                    >
                      <SearchOutlined className="text-lg" />
                    </button>
                  </div>

                  <Link href="/auth/signin" className="block">
                    <motion.button
                      whileHover={{ scale: 1.02, backgroundColor: scrolled ? '#ffffff' : '#006666', color: scrolled ? '#008080' : '#ffffff' }}
                      whileTap={{ scale: 0.98 }}
                      className={`${scrolled ? 'bg-white text-[#008080] border border-white' : 'bg-[#008080] text-white'} px-6 py-3 rounded-full text-lg font-bold transition-all duration-300 font-montserrat w-full`}
                    >
                      GET STARTED
                    </motion.button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>
    </>
  );
}