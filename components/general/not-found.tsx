import React, { useEffect } from 'react'
import notFoundImg from '@/public/learnkonnect.gif'
import Image from 'next/image'
import Cookies from "js-cookie";
import { AuthLogics } from '@/app/(auth)/auth/_logics/auth-logics';

interface NotFoundPage  {
  code: string
  value: string
}

const NotFoundPage = ({code, value}:NotFoundPage) => {
  const {refreshToken} = AuthLogics()
  useEffect(()=>{
    
    if(code.toString() === "401"){
      refreshToken();
    }
  }, [])
  return (
    <div className="fixed inset-0 z-[9999] bg-teal-700 flex items-center justify-center p-4">
      <div className="bg-white w-full h-auto max-w-6xl max-h-[90vh] flex flex-col md:flex-row shadow-lg rounded-lg overflow-hidden">
        <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-6 md:p-10">
            <h1 className="text-6xl md:text-[8rem] font-bold flex items-center">
                <span className="">😱</span>
                <span className="ml-2 text-primaryColor">{code}</span>
            </h1>
            <p className="text-center text-primaryColor mt-4 w-full max-w-[28rem] text-sm md:text-base">
            Oops! Something Went Wrong. {value}
            </p>
            <div
            onClick={()=>{
              Cookies.remove("access_token");
              window.location.href = "/auth/signin";
            }}
            className="mt-8 px-6 py-2 border border-primaryColor text-primaryColor rounded-full flex items-center gap-2 hover:bg-primaryColor hover:text-white transition cursor-pointer">
                ← Back To Home
            </div>
        </div>

        <div className="w-full md:w-1/2 flex items-center justify-center p-4 md:p-0">
          <Image src={notFoundImg} className='h-full w-full object-contain' alt="logo" width={500} height={500} priority />
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
