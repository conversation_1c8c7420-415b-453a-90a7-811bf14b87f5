'use client';
import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { VideoCameraOutlined, LeftOutlined, RightOutlined, CalendarOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation'; // Changed from 'next/router' to 'next/navigation'
import Link from 'next/link';

interface SessionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SessionModal: React.FC<SessionModalProps> = ({ isOpen, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [localImages, setLocalImages] = useState<string[]>([]);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const [currentDateTime, setCurrentDateTime] = useState(new Date());
  const router = useRouter(); // Now using the App Router

  // Default images for carousel
  const defaultImages = [
    "/frist one.png",
    "/second to last.png",
    "/lstone.png"
  ];

  // Update current date and time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Format date and time to get tje simple datet
  const formattedTime = currentDateTime.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const formattedDate = currentDateTime.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  });

  // Close modal with escape key
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') onClose();
    };

    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [onClose]);

  // Prevent scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Auto-changing carousel
  useEffect(() => {
    if (isOpen) {
      autoPlayRef.current = setInterval(() => {
        setCurrentIndex(prev => (prev === (localImages.length || defaultImages.length) - 1 ? 0 : prev + 1));
      }, 5000);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isOpen, localImages.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev === (localImages.length || defaultImages.length) - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev === 0 ? (localImages.length || defaultImages.length) - 1 : prev - 1));
  };

  if (!isOpen) return null;

  const images = localImages.length > 0 ? localImages : defaultImages;




  function handleClick(event: React.MouseEvent<HTMLButtonElement>): void {
    throw new Error('Function not implemented.');
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-2 sm:p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="relative bg-white rounded-lg w-full max-w-3xl overflow-hidden max-h-[95vh] flex flex-col"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with timestamp and close button */}
            <div className="flex justify-between items-center px-2 sm:px-4 py-1 sm:py-2 bg-white border-b">
              {/* Timestamp */}
              <div className="flex items-center text-xs sm:text-sm text-teal-700">
                <span>{formattedTime} · {formattedDate}</span>
                <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-1 text-teal-700" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>

              {/* Close button */}
              <button
                className="bg-white rounded-full p-1 sm:p-2 hover:bg-gray-100 transition-all"
                onClick={onClose}
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Mobile view: Date and Time Display  Simplified */}
            <div className="sm:hidden p-3 bg-white">
              <div className="bg-[#E6F6F6] rounded-lg p-3">
                <h3 className="text-[#0A6B6A] font-bold text-base mb-2">Session Details</h3>
                <div className="flex items-center mb-2">
                  <CalendarOutlined className="text-[#0A6B6A] mr-2" />
                  <span className="text-sm">{formattedDate}</span>
                </div>
                <div className="flex items-center">
                  <ClockCircleOutlined className="text-[#0A6B6A] mr-2" />
                  <span className="text-sm">{formattedTime}</span>
                </div>
              </div>
              <Link href="/dashboard/courses" passHref>
                <button
                  className="w-full mt-3 flex items-center justify-center border border-[#0A6B6A] text-[#0A6B6A] px-3 py-2 rounded text-sm hover:bg-[#E6F6F6] transition-colors"
                >
                  <VideoCameraOutlined className="mr-2" />
                  <span>Start Session</span>
                </button>
              </Link>
            </div>

            {/* Desktop view: Carousel - Hidden on smaller screens */}
            <div className="hidden sm:block relative h-56 md:h-72 w-full bg-gray-100">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  className="absolute inset-0"
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="w-full h-full relative">
                    {localImages.length > 0 ? (
                      <img
                        src={localImages[currentIndex]}
                        alt={`Session slide ${currentIndex + 1}`}
                        className="object-contain w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex flex-row justify-between items-center bg-white p-4 md:p-6">
                        <div className="w-1/2 pr-4 text-left">
                          <h2 className="text-[#0A6B6A] font-bold text-lg md:text-xl mb-2">
                            {currentIndex === 0 && "Join live interactive sessions"}
                            {currentIndex === 1 && "Connect with industry experts"}
                            {currentIndex === 2 && "Enhance your learning experience"}
                          </h2>
                          <p className="text-[#0A6B6A] text-sm md:text-base mb-2 md:mb-4">
                            {currentIndex === 0 && "Participate in real-time learning environments"}
                            {currentIndex === 1 && "Access exclusive knowledge and insights"}
                            {currentIndex === 2 && "Apply new skills through practical sessions"}
                          </p>
                          <div className="flex justify-start space-x-1 mt-2">
                            <span className={`w-2 h-2 rounded-full ${currentIndex === 0 ? 'bg-[#0A6B6A]' : 'bg-gray-300'}`}></span>
                            <span className={`w-2 h-2 rounded-full ${currentIndex === 1 ? 'bg-[#0A6B6A]' : 'bg-gray-300'}`}></span>
                            <span className={`w-2 h-2 rounded-full ${currentIndex === 2 ? 'bg-[#0A6B6A]' : 'bg-gray-300'}`}></span>
                          </div>
                          <Link href="/dashboard/startsession" passHref>
                            <button
                              className="mt-4 flex items-center justify-start border border-[#0A6B6A] text-[#0A6B6A] px-3 md:px-4 py-2 rounded text-sm md:text-base hover:bg-[#E6F6F6] transition-colors"
                            >
                              <VideoCameraOutlined className="mr-2" />
                              <span>Schedule Session</span>
                            </button>
                          </Link>
                          <p className="text-sm text-gray-500 mt-2">
                            <span className='text-teal-700 text-sm md:text-base font-bold'>Learn more</span> about learnKonnect live session
                          </p>
                        </div>
                        <div className="w-1/2">
                          <img
                            src={defaultImages[currentIndex]}
                            alt="Live session illustration"
                            className="w-full h-auto object-contain"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </AnimatePresence>

              {/* Dots indicators */}
              <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-3">
                {images.map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${index === currentIndex ? 'bg-[#0A6B6A]' : 'bg-gray-300'
                      }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentIndex(index);
                    }}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>

            {/* Modal footer - Always visible */}
            <div className="p-2 xs:p-3 sm:p-4 md:p-6 bg-white mt-auto">
              <div className="flex justify-end gap-2">
                <button
                  className="border border-gray-300 text-gray-700 px-2 sm:px-4 py-1 sm:py-2 rounded text-xs sm:text-sm md:text-base hover:bg-gray-50 transition-colors"
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  className="bg-[#0A6B6A] hover:bg-[#085453] text-white px-2 sm:px-4 py-1 sm:py-2 rounded text-xs sm:text-sm md:text-base transition-colors"
                  onClick={handleClick}
                >
                  Start Session
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SessionModal;