'use client';
import React, { useEffect, useState } from 'react';
import {
  SearchOutlined,
  BellOutlined,
  MenuOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Badge, Avatar } from 'antd';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { useRouter } from 'next/navigation';
import { useNotificationsLogic } from '@/app/(student)/student/notifications/_logics/notifications-logic';
import { useUserStore } from '@/store/userStore';
import { useLecturerStore } from '@/store/lecturerStore';
import { useStudentStore } from '@/store/studentStore';

interface TopBarProps {
  toggleSidebar: () => void;
}

const TopBar: React.FC<TopBarProps> = ({ toggleSidebar }) => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { isDark } = useTeacherTheme();
  const { user } = useUserStore();
  const { Lecturer } = useLecturerStore();
  const { student } = useStudentStore();

  // Dynamically set colors based on theme
  const bgColor = isDark ? '#252B42' : '#0A6B6A';
  const hoverBgColor = isDark ? '#1e232e' : '#095958';
  const searchBgColor = isDark ? '#323845' : '#095958';

  const {
    fetchNotifications,
    unreadCount,
  } = useNotificationsLogic();

  // Fetch notifications on component mount
    useEffect(() => {
      fetchNotifications(1, 5, false);
    }, []);

  // Notifications menu - commented out for now
  // const notificationsMenu = (
  //   <Menu>
  //     <Menu.Item key="1">New course added</Menu.Item>
  //     <Menu.Item key="2">Assignment due</Menu.Item>
  //     <Menu.Item key="3">5 new students enrolled</Menu.Item>
  //     <Menu.Item key="4">View all notifications</Menu.Item>
  //   </Menu>
  // );
  const router = useRouter();

  return (
    <header
      className={`text-white h-16 px-3 sm:px-5 flex items-center justify-between w-full sticky top-0 z-40 shadow-md`}
      style={{ backgroundColor: bgColor }}
    >
      {/* Left section with hamburger menu */}
      <div className="flex items-center">
        <button
          onClick={toggleSidebar}
          className={`p-2 rounded-full transition-colors hover:bg-opacity-80 flex items-center justify-center`}
          style={{ backgroundColor: 'transparent' }}
          aria-label="Toggle sidebar"
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = hoverBgColor;
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <MenuOutlined className="text-base" />
        </button>
        {/* Hide dashboard text on small screens when search is open */}
        <h1 className={`font-semibold text-base sm:text-lg ml-3 ${isSearchOpen ? 'hidden sm:block' : 'block'}`}>
          Dashboard
        </h1>
      </div>

      {/* Right section (search, notifications, profile) */}
      <div className="flex items-center space-x-3 sm:space-x-4">
        {/* Search input - hidden on mobile unless search is open */}
        {isSearchOpen ? (
          // Mobile search overlay
          <div
            className="block fixed top-16 left-0 right-0 z-50 py-3 shadow-lg animate-fadeIn"
            style={{
              backgroundColor: bgColor,
              borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
            }}
          >
            <div className="container mx-auto px-4 relative">
              <input
                type="text"
                placeholder="Search..."
                className="text-white w-full px-4 py-2.5 rounded-lg text-sm focus:outline-none shadow-md border border-teal-600"
                style={{ backgroundColor: searchBgColor }}
                onBlur={() => setTimeout(() => setIsSearchOpen(false), 200)}
                autoFocus={true}
              />
              <div className="absolute right-6 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full bg-teal-600 text-white">
                <SearchOutlined className="text-base" />
              </div>
            </div>
          </div>
        ) : (
          // Desktop search input
          <div className="relative hidden sm:block">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                className="text-white px-4 py-2 rounded-lg text-sm w-max sm:w-32 md:w-48 lg:w-64 focus:outline-none focus:ring-2 focus:ring-teal-500 border border-transparent focus:border-teal-500 transition-all duration-300"
                style={{ backgroundColor: searchBgColor }}
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-6 h-6 rounded-full text-white">
                <SearchOutlined className="text-sm" />
              </div>
            </div>
          </div>
        )}

        {/* Search button - only visible on mobile */}
        <button
          className={`
            w-9 h-9 rounded-full transition-all duration-300 sm:hidden flex items-center justify-center
            ${isSearchOpen
              ? 'bg-teal-600 shadow-md'
              : 'bg-transparent hover:bg-teal-600 hover:shadow-sm'
            }
          `}
          onClick={() => setIsSearchOpen(!isSearchOpen)}
          style={{
            transform: isSearchOpen ? 'scale(1.05)' : 'scale(1)'
          }}
          aria-label="Search"
        >
          <SearchOutlined className={`text-lg ${isSearchOpen ? 'text-white' : 'text-white'}`} />
        </button>

        {/* Notifications button */}
        <button
          className="p-2.5 rounded-full transition-colors flex items-center justify-center relative"
          style={{ backgroundColor: 'transparent' }}
          onClick={()=>{
            router.push('/student/notifications'); // we route to the notifications page
          }}
          onMouseOver={(e) => {
            // e.currentTarget.style.backgroundColor = hoverBgColor;
          }}
          onMouseOut={(e) => {
            // e.currentTarget.style.backgroundColor = 'transparent';
          }}
          aria-label="Notifications"
        >
          <Badge count={unreadCount} size="small" className="absolute -top-1 -right-1">
            <BellOutlined className="text-lg" />
          </Badge>
        </button>

        {/* Profile button */}
        <div
          className="flex items-center cursor-pointer rounded-full p-1.5 pr-3 ml-1 hover:bg-teal-600 transition-colors"
          style={{ backgroundColor: 'transparent' }}
          onClick={() => {
            student?.id?
               router.push('/student/profile'):
            router.push('/dashboard/profile')}}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = hoverBgColor;
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          {/* Show student profile image if available */}
          {student?.profile_image_path ? (
            <Avatar
              size={32}
              src={student.profile_image_path}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
            />
          ) : student?.profile_image ? (
            <Avatar
              size={32}
              src={student.profile_image.startsWith('data:') ?
                student.profile_image :
                `data:image/jpeg;base64,${student.profile_image}`}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
            />
          ) : student?.profile_image_path_server ? (
            <Avatar
              size={32}
              src={student.profile_image_path_server}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
            />
          ) : Lecturer?.profile_image_path ? (
            <Avatar
              size={32}
              src={Lecturer.profile_image_path}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
            />
          ) : Lecturer?.profile_image ? (
            <Avatar
              size={32}
              src={Lecturer.profile_image.startsWith('data:') ?
                Lecturer.profile_image :
                `data:image/jpeg;base64,${Lecturer.profile_image}`}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
            />
          ) : (
            <Avatar
              size={32}
              icon={<UserOutlined />}
              className="mr-2 sm:mr-3 border-2 border-white shadow-sm"
              style={{ backgroundColor: '#f0f0f0', color: '#0A6B6A' }}
            />
          )}
          <span className="hidden md:inline text-sm font-medium truncate max-w-[120px] lg:max-w-none">
            {user ?
              `${user.first_name} ${user.last_name}` :
              (Lecturer && Lecturer.name ? Lecturer.name : 'Lecturer')
            }
          </span>
        </div>


      </div>
    </header>
  );
};

export default TopBar;


