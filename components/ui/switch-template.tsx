import React from 'react';
import { ConfigProvider, Form, Switch, } from 'antd';


interface SwitchTemplateInterface {
   fieldName: string
  errorMessage?: string
  required?: boolean
  label?: string
  
  disabled?: boolean
  options?: any[]
  fieldNames?: any
  onChange?: (val: boolean) => void

}

export default function SwitchTemplate({fieldName, required, label, onChange}: SwitchTemplateInterface) {
    return (
        <ConfigProvider
  theme={{
    components: {
      Switch: {
        /* here is your component tokens */
        colorPrimary: "#008080",
        colorPrimaryHover: "#008080"
      },
    },
  }}
> <Form.Item
        className='my-0 py-0'
        name={fieldName}
        rules={[{ required: required, message: `${label} is required` }]}
      >
        <Switch className='active:bg-primaryColor'  onChange={onChange} />
        </Form.Item>
        </ConfigProvider>
        
    )
};

