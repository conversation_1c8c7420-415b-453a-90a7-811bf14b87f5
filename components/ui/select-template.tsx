import React, { ReactNode } from 'react';
import { ConfigProvider, Form, Select, ThemeConfig } from 'antd';

interface SelectTemplateInterface {
  prefix?: ReactNode
  visible?: boolean
  suffix?: ReactNode
  className?: string
  placeHolder?: string
  label: string
  textarea?: boolean
  textareaRows?: number
  fieldName: string
  errorMessage?: string
  required?: boolean
  disabled?: boolean
  options?: any[]
  fieldNames?: any
  onChange?: (val: any) => void

}
const theme: ThemeConfig = {
  components: {
    Select: {
      /* here is your component tokens */
      activeBorderColor: "#008080",
      hoverBorderColor: "#008080",
    },
  },
};
const SelectTemplate = ({ prefix, placeHolder, label, options, className, fieldName, required = true, disabled, fieldNames, onChange }: SelectTemplateInterface) => (
  <div>
    <div className='text-xs text-textColor font-semibold mb-2 '>{label}</div>
    <ConfigProvider theme={theme}>
      <Form.Item
        className='my-0 py-0'
        name={fieldName}
        rules={[{ required: required, message: `${label} is required` }]}
      >
        <Select
          disabled={disabled}
          style={{ background: 'transparent', border: '1px solid #008080' }}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          className={`mt-0 text-xs rounded-md h-[2.4rem] border !border-primaryColor w-full !focus:border-primaryColor !hover:border-primaryColor
        active:border-primaryColor course-form-field
        ${className}`}
          placeholder={placeHolder}
          optionFilterProp={label}
          prefix={prefix}
          options={options}
          fieldNames={fieldNames}
          onChange={(val: any) => {
            if (onChange) {
              onChange(val)
            }
          }}

        />
      </Form.Item>
    </ConfigProvider>
  </div>
);

export default SelectTemplate;