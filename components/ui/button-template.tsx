import { Button} from 'antd'
import React, { ReactNode } from 'react'

interface ButtonTemplateInterface{
    label: string | ReactNode
    className?: string
    htmlType?: "button" | "submit" | "reset" | undefined
    icon?: React.ReactNode
    trailingIcon?: React.ReactNode
    type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
    size?: 'small' | 'middle' | 'large';
    handleClick?: () => void;
    disabled?: boolean;
    onClick?: () => void;
    loading?: boolean;
}

export default function ButtonTemplate({ label, className, handleClick, onClick, htmlType, icon, type, size, disabled, loading, trailingIcon }: ButtonTemplateInterface) {
  // Use either handleClick or onClick, with handleClick taking precedence
  const clickHandler = handleClick || onClick;

  return (
    <div>
         <Button
         htmlType={htmlType}
         onClick={clickHandler}
         className={`border border-primaryColor rounded-lg text-xs px-8 hover:!bg-primaryColor hover:!text-white hover:!border-primaryColor ${className}`}
         type={type}
         size={size}
         disabled={disabled}
         loading={loading}
         >{icon}{label}{trailingIcon}</Button>
    </div>
  );
}