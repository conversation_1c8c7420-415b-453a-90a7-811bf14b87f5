"use client";

import { useState, ChangeEvent } from "react";
import { CameraOutlined } from "@ant-design/icons";

export default function ProfilePictureTemplate() {
    const [image, setImage] = useState(
        "https://images.pexels.com/photos/2743754/pexels-photo-2743754.jpeg?auto=compress&cs=tinysrgb&w=800"
    );

    const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const imageUrl = URL.createObjectURL(file);
            setImage(imageUrl);
        }
    };

    return (
        <div className="">
            {/* Profile Picture */}
            <img src={image} className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20 rounded-full border-4 border-white object-cover" alt="Profile Picture" />
                    <label
                        htmlFor="fileInput"
                        className="absolute bottom-0 left-5 transform translate-y-1/2 w-20 h-20  border-4 border-white object-cover  bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
                    >
                        <CameraOutlined className='left-10' style={{
                            fontSize: "24px", color: "white", marginLeft: "24px",
                            marginTop: "24px"
                        }} />
                    </label>

            {/* Hidden File Input */}
            <input
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
                id="fileInput"
            />

            
        </div>
    );
}
