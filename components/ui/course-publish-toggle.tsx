import React, { useState } from 'react';
import { Switch, Tooltip } from 'antd';
import { useCourseLogic } from '@/logics/course';

interface CoursePublishToggleProps {
  courseId: string;
  initialPublishState: boolean;
  disabled?: boolean;
}

const CoursePublishToggle: React.FC<CoursePublishToggleProps> = ({
  courseId,
  initialPublishState = false,
  disabled = false
}) => {
  const [isPublished, setIsPublished] = useState(initialPublishState);
  const [isLoading, setIsLoading] = useState(false);
  const { publishCourse } = useCourseLogic();

  const handleToggle = async (checked: boolean) => {
    setIsLoading(true);
    
    try {
      const result = await publishCourse(courseId, checked);
      
      if (result.success) {
        setIsPublished(checked);
      } else {
        // If the API call fails, revert the toggle
        console.error('Failed to update course publish status:', result.error);
      }
    } catch (error) {
      console.error('Error toggling course publish status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Tooltip title={isPublished ? 'Unpublish course' : 'Publish course'}>
      <Switch
        checked={isPublished}
        onChange={handleToggle}
        loading={isLoading}
        disabled={disabled}
        className={`${isPublished ? 'bg-teal-600' : 'bg-gray-400'}`}
      />
    </Tooltip>
  );
};

export default CoursePublishToggle;
