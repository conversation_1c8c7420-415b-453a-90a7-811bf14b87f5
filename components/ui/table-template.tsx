import React from 'react';
import { Table, Tag } from 'antd';
import type { TableProps } from 'antd';
import Image from 'next/image';
import { FileTextOutlined } from '@ant-design/icons';
interface DataType {
  key: string;
  course: string;
  date: string;
  payment_method: string;
  actions: string[];
}

interface TableFields {
  columns: any[];
  data: any[];
  handleRowClick?: (record: any) => void;
  }

const columns: TableProps<DataType>['columns'] = [
  {
    title: 'Course',
    dataIndex: 'course',
    key: 'course',
    render: (text) => <div className='flex'>
       <Image src={"/clock.png"} alt="logo" width={33} height={1} />
                      <div>{text}</div>        
    </div>,
  },
  
  {
    title: 'Payment Method',
    dataIndex: 'payment_method',
    key: 'payment_method',
  },
  {
    title: 'Date',
    dataIndex: 'date',
    key: 'date',
  },
 
];

const data: DataType[] = [
  {
    key: '1',
    course: '<PERSON>',
    date: '21/02/2025',
    payment_method: 'New York No. 1 Lake Park',
    actions: ['Receipt'],
  },
  {
    key: '2',
    course: 'Jim Green',
    date: '21/02/2025',
    payment_method: 'New York No. 1 Lake Park',
    actions: ['Receipt'],
  },
  {
    key: '3',
    course: 'Joe Black',
    date: '21/02/2025',
    payment_method: 'New York No. 1 Lake Park',
    actions: ['Receipt'],
  },
];

const TableTemplate = ({data, columns, handleRowClick}:TableFields) => <Table<DataType>
rowClassName={'hover:cursor-pointer'}  
pagination={false}
onRow={(record, rowIndex) => {
    return {
      onClick: event => {
        handleRowClick && handleRowClick(record);
        // console.log('Row clicked:', record);
      }, // click row
    };
  }}
className='w-full' columns={columns} dataSource={data} />;

export default TableTemplate;