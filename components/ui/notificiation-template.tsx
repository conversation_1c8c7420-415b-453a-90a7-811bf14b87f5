import React, { useMemo } from 'react';
import { Button, notification } from 'antd';
import type { NotificationArgsProps } from 'antd';

type NotificationPlacement = NotificationArgsProps['placement'];

const Context = React.createContext({ name: 'Default' });

const NotificationTemplate = ({ children }: { children: (api: any) => React.ReactNode }) => {
  const [api, contextHolder] = notification.useNotification();

  const contextValue = useMemo(() => ({ name: 'Ant Design' }), []);

  return (
    <Context.Provider value={contextValue}>
      {contextHolder}
      {children(api)}
    </Context.Provider>
  );
};

export default NotificationTemplate;










// import React, { useMemo } from 'react';
// import {
//   RadiusBottomleftOutlined,
//   RadiusBottomrightOutlined,
//   RadiusUpleftOutlined,
//   RadiusUprightOutlined,
// } from '@ant-design/icons';
// import { Button, Divider, notification, Space } from 'antd';
// import type { NotificationArgsProps } from 'antd';

// type NotificationPlacement = NotificationArgsProps['placement'];

// const Context = React.createContext({ name: 'Default' });

// const NotificationTemplate = () => {
//   const [api, contextHolder] = notification.useNotification();

//   const openNotification = (placement: NotificationPlacement) => {
//     api.info({
//       message: `Notification ${placement}`,
//       description: <Context.Consumer>{({ name }) => `Hello, ${name}!`}</Context.Consumer>,
//       placement,
//     });
//   };

//   const contextValue = useMemo(() => ({ name: 'Ant Design' }), []);

//   return (
//     <Context.Provider value={contextValue}>
//       {contextHolder}
//       <Space>
//         <Button
//           type="primary"
//           onClick={() => openNotification('topLeft')}
//           icon={<RadiusUpleftOutlined />}
//         >
//           topLeft
//         </Button>
//         <Button
//           type="primary"
//           onClick={() => openNotification('topRight')}
//           icon={<RadiusUprightOutlined />}
//         >
//           topRight
//         </Button>
//       </Space>
//       <Divider />
//       <Space>
//         <Button
//           type="primary"
//           onClick={() => openNotification('bottomLeft')}
//           icon={<RadiusBottomleftOutlined />}
//         >
//           bottomLeft
//         </Button>
//         <Button
//           type="primary"
//           onClick={() => openNotification('bottomRight')}
//           icon={<RadiusBottomrightOutlined />}
//         >
//           bottomRight
//         </Button>
//       </Space>
//     </Context.Provider>
//   );
// };

// export default NotificationTemplate;