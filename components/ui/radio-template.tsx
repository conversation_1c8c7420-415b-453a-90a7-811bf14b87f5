import React, { useState } from 'react';
import type { RadioChangeEvent } from 'antd';
import { ConfigProvider, Flex, Form, Radio } from 'antd';
interface RadioInterface {
  fieldName: string
  defaultValue?: string
 
  options?:  {
    value: string;
    label: string;
}[]
 }
const RadioTemplate = ({options,fieldName, defaultValue}:RadioInterface) => {
 
  return (
    <ConfigProvider
  theme={{
    token: {
      /* here is your global tokens */
      colorPrimary: "#008080",
    },
    components: {
      Radio: {
        /* here is your component tokens */
        buttonSolidCheckedBg: "#008080",
        buttonSolidCheckedActiveBg:  "#008080",
      },
    },
  }}
>
    <Form.Item
      className='my-0 py-0'
      name={fieldName}
      initialValue={defaultValue}
    
    >
    <Radio.Group
    className='text-primaryColor'
    defaultValue={defaultValue}
    value={defaultValue}
      options={
        options?.map((option)=>(
          {
            value: option.value,
            label: (
              <Flex gap="small" justify="center" align="center" vertical>
               {option.label}
              </Flex>
            ),
          }
        ))
        
      
    }
    />
    </Form.Item>
    </ConfigProvider>
  );
};

export default RadioTemplate;