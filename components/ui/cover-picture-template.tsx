// "use client";

// import { useState, ChangeEvent } from "react";
// import { CameraOutlined } from "@ant-design/icons";

// export default function CoverPictureTemplate() {
//     const [image, setImage] = useState(
//         "https://plus.unsplash.com/premium_photo-1681422570054-9ae5b8b03e46?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8c3Vuc2V0fGVufDB8fDB8fHww"
//     );

//     const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
//         const file = event.target.files?.[0];
//         if (file) {
//             const imageUrl = URL.createObjectURL(file);
//             setImage(imageUrl);
//         }
//     };

//     return (
//         <div className="">
//             {/* Profile Picture */}
//             <img src={image} className="w-full h-48 object-cover rounded-md" alt="Cover Picture" />
//                     <label
//                         htmlFor="coverInput"
//                         className="absolute bottom-0 z-[90] left-5 transform translate-y-1/2 w-20 h-20  border-4 border-white object-cover  bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
//                     >
//                         <CameraOutlined className='left-10' style={{
//                             fontSize: "24px", color: "white", marginLeft: "24px",
//                             marginTop: "24px"
//                         }} />
//                     </label>

//             {/* Hidden File Input */}
//             <input
//                 type="file"
//                 accept="image/*"
//                 onChange={handleImageChange}
//                 className="hidden"
//                 id="coverInput"
//             />

            
//         </div>
//     );
// }
