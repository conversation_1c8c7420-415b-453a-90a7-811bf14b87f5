import { ConfigProvider, Form, Input, ThemeConfig } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { ReactNode } from 'react';

interface InputTemplateInterface {

  loadingState?:boolean
  onEnterPressed?: (e: any) => void;
 prefix?: ReactNode;
  visible?: boolean;
  suffix?: ReactNode;
  className?: string;
  outerClassName?: string;
  placeHolder?: string;
  label: string;
  fieldName: string;
  value?: string;
  errorMessage?: string;
  textarea?: boolean;
  required?: boolean;
  disabled?: boolean;
  textareaRows?: number;
  allowClear?: boolean;
  maxLength?: number;
  type?: string; // Added type property
  step?: number; // Added step property
  min?: number;
  rules?: Array<{
    required?: boolean;
    message?: string;
    validator?: (rule: any, value: any) => Promise<void>;
    inputType?: 'button' | 'checkbox' | 'color' | 'date' | 'datetime-local' | 'email' | 'file' | 'hidden' | 'image' | 'month' | 'number' | 'password' | 'radio' | 'range' | 'reset' | 'search' | 'submit' | 'tel' | 'text' | 'time' | 'url' | 'week';
  }>;
  onChange?: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  inputType?: 'button' | 'checkbox' | 'color' | 'date' | 'datetime-local' | 'email' | 'file' | 'hidden' | 'image' | 'month' | 'number' | 'password' | 'radio' | 'range' | 'reset' | 'search' | 'submit' | 'tel' | 'text' | 'time' | 'url' | 'week';
}

const theme: ThemeConfig = {
  token: {
    colorPrimary: '#008080',
  },
};

const { Search } = Input;

export default function InputTemplate({
  prefix,
  suffix,
  value,
  className,
  placeHolder,
  label,
  textarea,
  textareaRows,
  inputType,
  fieldName,
  required = true,
  disabled,
  outerClassName,
  onChange,
  rules = [],
  maxLength,
  onEnterPressed,loadingState
}: InputTemplateInterface) {
  return (
    <ConfigProvider theme={theme}>
      <div className={`mb-8 ${outerClassName}`}>
        <div className="text-xs text-textColor font-semibold mb-2">{label}</div>

        <Form.Item
          className="my-0 py-0"
          name={fieldName}
          rules={[
            { required: required, message: `${label} is required` },
            ...rules,
          ]}
        >
          {inputType === 'search' ? (
            <Search
              className={`mt-0 bg-transparent text-xs rounded-md h-[2.4rem] border-primaryColor ${className}`}
              size="large"
              placeholder={placeHolder}
              prefix={prefix}
              type={inputType}
              disabled={disabled}
              suffix={suffix}
              enterButton
              onSearch={onEnterPressed}
              onPressEnter={(val)=>{onEnterPressed!(val.currentTarget.value)}}


              loading={loadingState}

            />
          ) : inputType === 'password' ? (
            <Input.Password
              className={`mt-0 bg-transparent text-xs rounded-md h-[2.4rem] border-primaryColor ${className}`}
              size="large"
              placeholder={placeHolder}
              prefix={prefix}
              type={inputType}
              disabled={disabled}
              suffix={suffix}
            />
          ) : (
            <>
              {!textarea ? (
                <Input
                  className={`mt-0 bg-transparent text-xs rounded-md h-[2.4rem] border-primaryColor course-form-field ${className}`}
                  size="large"
                  placeholder={placeHolder}
                  prefix={prefix}
                  type={inputType}
                  disabled={disabled}
                  onChange={onChange}
                  suffix={suffix}
                  value={value}
                  maxLength={maxLength}
                />
              ) : (
                <TextArea
                  disabled={disabled}
                  onChange={onChange}
                  className={`mt-0 bg-transparent text-xs rounded-md h-[2.4rem] border-primaryColor resize-none course-form-field ${className}`}
                  rows={textareaRows ?? 4}
                />
              )}
            </>
          )}
        </Form.Item>
      </div>
    </ConfigProvider>
  );
}