// components/ui/view-modal-template.tsx
import React from 'react';
import { Modal, Typography, Descriptions, Tag } from 'antd';
import ButtonTemplate from './button-template';
import { CourseType } from '@/logics/course';

const { Title } = Typography;

interface ViewModalTemplateProps {
  isVisible: boolean;
  onCancel: () => void;
  item: CourseType | null;
  title?: string;
}

const ViewModalTemplate: React.FC<ViewModalTemplateProps> = ({
  isVisible,
  onCancel,
  item,
  title = 'View Course Details',
}) => {
  if (!item) return null;

  const getStatusTag = (item: CourseType) => {
    // Check if the course is published using is_published property
    const isPublished = item.is_published || false;

    if (isPublished) {
      return <Tag color="green">Published</Tag>;
    } else {
      return <Tag color="gold">Draft</Tag>;
    }
  };

  return (
    <Modal
      open={isVisible}
      onCancel={onCancel}
      footer={null}
      title={
        <div className="flex items-center">
          <span className="text-teal-700 font-semibold">{title}</span>
        </div>
      }
      width="90%"
      style={{ maxWidth: '900px' }}
      className="view-modal-template"
      maskClosable={true}
      closeIcon={
        <div className="absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1 transition-colors cursor-pointer">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      }
    >
      <div className="py-2 sm:py-4">
        <div className="bg-teal-50 p-3 sm:p-4 rounded-lg mb-4 sm:mb-6">
          <Title level={4} className="text-teal-800 mb-0 sm:mb-1 text-sm sm:text-xl">{item.name || item.title}</Title>
          <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4">
            <p className="text-teal-700 text-sm sm:text-base mb-1 sm:mb-0">Course Code: <span className="font-semibold">{item.code || item.courseCode}</span></p>
            <p className="text-teal-700 text-sm sm:text-base">Credits: <span className="font-semibold">{item.credits || 'N/A'}</span></p>
          </div>
        </div>

        <Descriptions
          bordered
          column={{ xs: 1, sm: 1, md: 2 }}
          className="mb-4 sm:mb-6"
          size="small"
          labelStyle={{ fontWeight: '200' }}
          contentStyle={{ fontSize: '0.95rem' }}
        >
          <Descriptions.Item label="Course Level" span={1}>{item.level}</Descriptions.Item>
          <Descriptions.Item label="Credits" span={1}>{item.credits || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="Duration" span={1}>
            {item.cohort_duration_weeks ?
              `${item.cohort_duration_weeks} weeks` :
              (item.duration ?
                `${item.duration} ${Number(item.duration) === 1 ? 'week' : 'weeks'}` :
                'N/A')
            }
          </Descriptions.Item>
          <Descriptions.Item label="Price" span={1}>${item.base_price || item.price || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="Course Code" span={1}>{item.code || item.courseCode || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>{getStatusTag(item)}</Descriptions.Item>
        </Descriptions>

        {item.description && (
          <div className="mb-4 sm:mb-6">
            <Title level={5} className="mb-1 sm:mb-2 text-base sm:text-sm">Course Description</Title>
            <div className="p-3 sm:p-4 ">
              <p className="sm:text-sm sm:text-base leading-relaxed">{item.description}</p>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-2 sm:mt-4">
          <ButtonTemplate
            label="Close"
            className="bg-teal-600 hover:bg-teal-700 text-white text-sm sm:text-base px-4 py-1 sm:px-5 sm:py-2 rounded-md"
            handleClick={onCancel}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ViewModalTemplate;