import React from 'react'

type OfferCardTemplateType ={
    amount: string
    value: string
    date: string
}

export default function OfferCardTemplate({amount, value, date}: OfferCardTemplateType) {
  return (
    <div className="bg-white flex items-center rounded-lg shadow-2xl p-4 w-[20rem] mx-4 relative my-6 ">
 <div className="absolute -left-4 top-1/2 -translate-y-1/2 bg-[#f1f1f3] border-none w-8 h-8 rounded-full"></div>
 <div className="absolute -right-4 top-1/2 -translate-y-1/2 bg-[#f5f5f6] w-8 h-8 rounded-full"></div>
 <div className="flex items-center space-x-4">
     <div className="bg-blue-500 p-3 rounded-full flex items-center justify-center ml-4">
         <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor"><path d="M9 14H7v2h2v-2zM9 10H7v2h2v-2zM13 14h-2v2h2v-2zM13 10h-2v2h2v-2zM17 14h-2v2h2v-2zM17 10h-2v2h2v-2z"/><path d="M19 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H5V4h14v16z"/></svg>
     </div>
     <div>
         <h2 className="text-sm font-semibold">{amount}</h2>
         <p className="text-gray-600">{value}</p>
         <p className="text-sm text-gray-400">{date}</p>
     </div>
 </div>
</div>
  )
}
