import React from 'react';
import { Modal, Typography } from 'antd';
import ButtonTemplate from './button-template';
import { CourseType } from '@/logics/course';
import { Course } from '@/types';

const { Title, Text } = Typography;

interface DeleteModalTemplateProps {
  isVisible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  item: CourseType | Course | any | null;
  title?: string;
  loading?: boolean;
}

const DeleteModalTemplate: React.FC<DeleteModalTemplateProps> = ({
  isVisible,
  onCancel,
  onConfirm,
  item,
  title = 'Delete Confirmation',
  loading = false,
}) => {
  return (
    <Modal
      open={isVisible}
      onCancel={onCancel}
      footer={null}
      centered
      className="delete-modal-template"
      width="95%"
      style={{ maxWidth: '400px', borderRadius: '12px' }}
      maskClosable={true}
      closeIcon={
        <div className="absolute top-3 right-3 bg-gray-100 hover:bg-gray-200 rounded-full p-1 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      }
    >
      <div className="flex flex-col items-center py-4 sm:py-5">
        <div className="bg-red-100 p-3 rounded-full mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </div>
        <Title level={4} className="text-center mb-2 text-lg sm:text-xl font-bold">{title}</Title>
        <Text className="text-center text-gray-600 mb-4 text-sm sm:text-base px-2 leading-relaxed">
          Are you sure you want to delete this course? This action cannot be undone.
        </Text>
        {item && (
          <Text className="text-center font-semibold mb-5 text-base px-4 py-2 bg-gray-50 rounded-md border border-gray-200 w-full max-w-xs">"{item.name || item.title}"</Text>
        )}
        <div className="flex flex-col-reverse sm:flex-row justify-center gap-3 w-full">
          <ButtonTemplate
            label="Cancel"
            className="border border-gray-300 hover:bg-gray-100 text-gray-700 text-sm sm:text-base py-2 px-4 w-full sm:w-auto"
            handleClick={onCancel}
            disabled={loading}
          />
          <ButtonTemplate
            label="Delete"
            className="bg-red-600 hover:bg-red-700 text-white text-sm sm:text-base py-2 px-4 w-full sm:w-auto"
            handleClick={onConfirm}
            loading={loading}
          />
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModalTemplate;