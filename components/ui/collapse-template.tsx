import { CaretUpOutlined } from '@ant-design/icons'
import { Collapse } from 'antd'
import { div } from 'framer-motion/client'
import React, { ReactNode } from 'react'

interface CollapseTemplateProps{
    items:  {
        key: string,
        label: string,
        children: ReactNode,
      }[],
}

export default function CollapseTemplate({items}:CollapseTemplateProps) {
    
  return (
    <div><Collapse  expandIconPosition='end' defaultActiveKey={['1']} ghost items={items} /></div>
  )
}
