import React, { useState } from 'react';
import { Checkbox, Form } from 'antd';
import type { CheckboxProps } from 'antd';



interface CheckBoxTemplateInterface {
  fieldName: string
  errorMessage?: string
  textarea?: boolean
  required?: boolean
  disabled?: boolean
  textareaRows?: number
  inputType?: 'button' | 'checkbox' | 'color' | 'date' | 'datetime-local' | 'email' | 'file' | 'hidden' | 'image' | 'month' | 'number' | 'password' | 'radio' | 'range' | 'reset' | 'search' | 'submit' | 'tel' | 'text' | 'time' | 'url' | 'week'
}


export default function CheckboxTemplate({ fieldName }: CheckBoxTemplateInterface) {
  const [isChecked, setIsChecked] = useState(false);
  const onChange: CheckboxProps['onChange'] = (e) => {
    console.log(`checked = ${e.target.checked}`);
    setIsChecked(e.target.checked)
  };
  return (
    <Form.Item
valuePropName="checked"
      className='my-0 py-0'
      name={fieldName}
    >
      <Checkbox >Remember Me</Checkbox>
    </Form.Item>
  )
};
